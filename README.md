# 🔐 密保卡查询系统

一个基于 Streamlit 的密保卡查询工具，可以根据输入的代码快速查询对应的密保数字。

## 📋 功能特点

- 🎯 **精确查询**：输入如 `a3b4` 的代码，快速获取对应位置的数字组合
- 🎨 **美观界面**：现代化的 Web 界面，操作简单直观
- 📊 **数据展示**：清晰显示密保卡完整数据表格
- 🔍 **详细信息**：显示查询过程和每个位置的具体数值
- ✅ **输入验证**：自动验证输入格式，提供友好的错误提示

## 🚀 快速开始

### 环境要求

- Python 3.8+
- uv 包管理器

### 安装依赖

```bash
# 使用 uv 安装依赖
uv add streamlit pandas
```

### 运行应用

```bash
# 启动 Streamlit 应用
uv run streamlit run app.py
```

应用将在 `http://localhost:8501` 启动。

## 📖 使用说明

### 输入格式

输入4位代码，格式为：`字母数字字母数字`

- **字母范围**：A-H（对应密保卡的行）
- **数字范围**：1-8（对应密保卡的列）

### 使用示例

1. **基本查询**：
   - 输入：`a3b4`
   - 输出：`611286`
   - 说明：A行3列(611) + B行4列(286) = 611286

2. **其他示例**：
   - `A1B2` → `781329`
   - `c5d6` → `307875`
   - `h8a1` → `714781`

### 注意事项

- 输入不区分大小写
- 系统会自动去除多余空格
- 确保输入的位置在密保卡范围内（A-H行，1-8列）

## 📁 文件结构

```
密保卡/
├── app.py          # 主应用程序
├── card.csv        # 密保卡数据文件
├── test_app.py     # 测试脚本
├── README.md       # 说明文档
└── pyproject.toml  # 项目配置文件
```

## 🧪 测试

运行测试脚本验证功能：

```bash
uv run python test_app.py
```

测试脚本会验证：
- 数据加载功能
- 代码解析功能
- 查询结果准确性
- 特别验证 `a3b4 = 611286`

## 📊 密保卡数据格式

`card.csv` 文件格式：

```csv
行列,1,2,3,4,5,6,7,8
A,781,458,611,726,359,912,745,656
B,654,329,488,286,373,880,580,764
C,722,885,741,265,307,375,496,680
D,403,855,165,763,498,875,574,959
E,342,898,699,711,930,115,787,491
F,614,535,485,409,834,636,360,228
G,369,396,919,105,706,771,125,913
H,570,404,590,289,507,644,562,714
```

## 🛠️ 技术栈

- **Streamlit**：Web 应用框架
- **Pandas**：数据处理
- **Python**：核心编程语言
- **uv**：现代 Python 包管理器

## 📝 更新日志

### v1.0.0
- ✅ 基本查询功能
- ✅ Web 界面
- ✅ 输入验证
- ✅ 测试脚本
- ✅ 详细文档

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
