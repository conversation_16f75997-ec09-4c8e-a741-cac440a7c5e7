#!/usr/bin/env python3
"""
密保卡查询系统功能演示
"""

import pandas as pd
import re
import pyperclip
from app import load_card_data, parse_code, get_values

def demo_app():
    """演示应用的所有功能"""
    print("🔐 密保卡查询系统功能演示")
    print("=" * 50)
    
    # 加载数据
    print("📁 加载密保卡数据...")
    df = load_card_data()
    if df is None:
        print("❌ 数据加载失败")
        return
    print("✅ 数据加载成功")
    
    # 演示查询功能
    test_cases = [
        "a3b4",
        "A1B2", 
        "c5d6",
        "h8a1",
        "invalid",  # 错误格式测试
    ]
    
    print("\n🧪 功能演示:")
    print("-" * 30)
    
    for i, test_input in enumerate(test_cases, 1):
        print(f"\n测试 {i}: 输入 '{test_input}'")
        
        # 解析代码
        positions = parse_code(test_input)
        if positions is None:
            print("❌ 格式错误")
            continue
            
        # 获取结果
        result = get_values(df, positions)
        if result is None:
            print("❌ 查询失败")
            continue
            
        print(f"✅ 查询结果: {result}")
        
        # 演示复制功能
        try:
            pyperclip.copy(result)
            clipboard_content = pyperclip.paste()
            if clipboard_content == result:
                print("📋 已自动复制到剪贴板")
            else:
                print("⚠️ 剪贴板复制可能有问题")
        except Exception as e:
            print(f"⚠️ 剪贴板功能异常: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🎉 功能演示完成！")
    print("\n💡 使用说明:")
    print("1. 启动应用: uv run streamlit run app.py")
    print("2. 在浏览器中打开: http://localhost:8501")
    print("3. 输入代码格式: 字母数字字母数字 (如: a3b4)")
    print("4. 按回车查询，结果会自动复制到剪贴板")

if __name__ == "__main__":
    demo_app()
