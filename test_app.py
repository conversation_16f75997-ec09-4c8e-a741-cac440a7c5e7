#!/usr/bin/env python3
"""
测试密保卡查询功能
"""

import pandas as pd
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import load_card_data, parse_code, get_values

def test_card_query():
    """测试密保卡查询功能"""
    print("🔐 密保卡查询系统测试")
    print("=" * 50)
    
    # 加载数据
    print("📁 加载密保卡数据...")
    df = load_card_data()
    if df is None:
        print("❌ 无法加载数据")
        return False
    
    print("✅ 数据加载成功")
    print("\n📊 密保卡数据:")
    print(df)
    
    # 测试用例
    test_cases = [
        "a3b4",  # 应该输出 611286
        "A1B2",  # 测试大写
        "c5d6",  # 测试其他位置
        "h8a1",  # 测试边界值
    ]
    
    print("\n🧪 开始测试查询功能...")
    print("-" * 30)
    
    for i, test_code in enumerate(test_cases, 1):
        print(f"\n测试 {i}: 输入代码 '{test_code}'")
        
        # 解析代码
        positions = parse_code(test_code)
        if positions is None:
            print(f"❌ 代码格式错误: {test_code}")
            continue
        
        print(f"📍 解析位置: {positions}")
        
        # 获取值
        result = get_values(df, positions)
        if result is None:
            print(f"❌ 查询失败: {test_code}")
            continue
        
        # 显示详细信息
        pos1, pos2 = positions
        val1 = df.loc[pos1[0], str(pos1[1])]
        val2 = df.loc[pos2[0], str(pos2[1])]
        
        print(f"🔍 {pos1[0]}{pos1[1]} → {val1}")
        print(f"🔍 {pos2[0]}{pos2[1]} → {val2}")
        print(f"✅ 最终结果: {result}")
    
    # 特别验证 a3b4 = 611286
    print("\n" + "=" * 50)
    print("🎯 特别验证: a3b4 应该等于 611286")
    positions = parse_code("a3b4")
    if positions:
        result = get_values(df, positions)
        if result == "611286":
            print("✅ 验证成功！a3b4 = 611286")
        else:
            print(f"❌ 验证失败！a3b4 = {result}，期望 611286")
    
    print("\n🎉 测试完成！")
    return True

if __name__ == "__main__":
    test_card_query()
