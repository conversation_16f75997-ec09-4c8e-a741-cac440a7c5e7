/*! For license information please see main.749ca5aa.js.LICENSE.txt */
(()=>{"use strict";var e={110:(e,t,n)=>{var r=n(441),i={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},o={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},a={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},s={};function l(e){return r.isMemo(e)?a:s[e.$$typeof]||i}s[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},s[r.Memo]=a;var u=Object.defineProperty,c=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,f=Object.getOwnPropertyDescriptor,h=Object.getPrototypeOf,p=Object.prototype;e.exports=function e(t,n,r){if("string"!==typeof n){if(p){var i=h(n);i&&i!==p&&e(t,i,r)}var a=c(n);d&&(a=a.concat(d(n)));for(var s=l(t),y=l(n),m=0;m<a.length;++m){var b=a[m];if(!o[b]&&(!r||!r[b])&&(!y||!y[b])&&(!s||!s[b])){var g=f(n,b);try{u(t,b,g)}catch(v){}}}}return t}},725:e=>{var t=Object.getOwnPropertySymbols,n=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable;e.exports=function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var t={},n=0;n<10;n++)t["_"+String.fromCharCode(n)]=n;if("0123456789"!==Object.getOwnPropertyNames(t).map((function(e){return t[e]})).join(""))return!1;var r={};return"abcdefghijklmnopqrst".split("").forEach((function(e){r[e]=e})),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},r)).join("")}catch(i){return!1}}()?Object.assign:function(e,i){for(var o,a,s=function(e){if(null===e||void 0===e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}(e),l=1;l<arguments.length;l++){for(var u in o=Object(arguments[l]))n.call(o,u)&&(s[u]=o[u]);if(t){a=t(o);for(var c=0;c<a.length;c++)r.call(o,a[c])&&(s[a[c]]=o[a[c]])}}return s}},463:(e,t,n)=>{var r=n(791),i=n(296);function o(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var a=new Set,s={};function l(e,t){u(e,t),u(e+"Capture",t)}function u(e,t){for(s[e]=t,e=0;e<t.length;e++)a.add(t[e])}var c=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,h={},p={};function y(e,t,n,r,i,o,a){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=a}var m={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){m[e]=new y(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];m[t]=new y(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){m[e]=new y(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){m[e]=new y(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){m[e]=new y(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){m[e]=new y(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){m[e]=new y(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){m[e]=new y(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){m[e]=new y(e,5,!1,e.toLowerCase(),null,!1,!1)}));var b=/[\-:]([a-z])/g;function g(e){return e[1].toUpperCase()}function v(e,t,n,r){var i=m.hasOwnProperty(t)?m[t]:null;(null!==i?0!==i.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,i,r)&&(n=null),r||null===i?function(e){return!!d.call(p,e)||!d.call(h,e)&&(f.test(e)?p[e]=!0:(h[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=null===n?3!==i.type&&"":n:(t=i.attributeName,r=i.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(i=i.type)||4===i&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(b,g);m[t]=new y(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(b,g);m[t]=new y(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(b,g);m[t]=new y(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){m[e]=new y(e,1,!1,e.toLowerCase(),null,!1,!1)})),m.xlinkHref=new y("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){m[e]=new y(e,1,!1,e.toLowerCase(),null,!0,!0)}));var w=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,_=Symbol.for("react.element"),S=Symbol.for("react.portal"),x=Symbol.for("react.fragment"),k=Symbol.for("react.strict_mode"),I=Symbol.for("react.profiler"),T=Symbol.for("react.provider"),O=Symbol.for("react.context"),A=Symbol.for("react.forward_ref"),C=Symbol.for("react.suspense"),E=Symbol.for("react.suspense_list"),N=Symbol.for("react.memo"),M=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var B=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var L=Symbol.iterator;function D(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=L&&e[L]||e["@@iterator"])?e:null}var F,P=Object.assign;function R(e){if(void 0===F)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);F=t&&t[1]||""}return"\n"+F+e}var z=!1;function U(e,t){if(!e||z)return"";z=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&"string"===typeof u.stack){for(var i=u.stack.split("\n"),o=r.stack.split("\n"),a=i.length-1,s=o.length-1;1<=a&&0<=s&&i[a]!==o[s];)s--;for(;1<=a&&0<=s;a--,s--)if(i[a]!==o[s]){if(1!==a||1!==s)do{if(a--,0>--s||i[a]!==o[s]){var l="\n"+i[a].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}}while(1<=a&&0<=s);break}}}finally{z=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?R(e):""}function j(e){switch(e.tag){case 5:return R(e.type);case 16:return R("Lazy");case 13:return R("Suspense");case 19:return R("SuspenseList");case 0:case 2:case 15:return e=U(e.type,!1);case 11:return e=U(e.type.render,!1);case 1:return e=U(e.type,!0);default:return""}}function V(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case x:return"Fragment";case S:return"Portal";case I:return"Profiler";case k:return"StrictMode";case C:return"Suspense";case E:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case O:return(e.displayName||"Context")+".Consumer";case T:return(e._context.displayName||"Context")+".Provider";case A:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case N:return null!==(t=e.displayName||null)?t:V(e.type)||"Memo";case M:t=e._payload,e=e._init;try{return V(e(t))}catch(n){}}return null}function W(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return V(t);case 8:return t===k?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function $(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function H(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function Y(e){e._valueTracker||(e._valueTracker=function(e){var t=H(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(e){r=""+e,o.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function K(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=H(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function Q(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function q(e,t){var n=t.checked;return P({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function G(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=$(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function X(e,t){null!=(t=t.checked)&&v(e,"checked",t,!1)}function J(e,t){X(e,t);var n=$(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,$(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Z(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&Q(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+$(n),t=null,i=0;i<e.length;i++){if(e[i].value===n)return e[i].selected=!0,void(r&&(e[i].defaultSelected=!0));null!==t||e[i].disabled||(t=e[i])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(o(91));return P({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ie(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(o(92));if(te(n)){if(1<n.length)throw Error(o(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:$(n)}}function oe(e,t){var n=$(t.value),r=$(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function ae(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function se(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function le(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?se(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ue,ce,de=(ce=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ue=ue||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ue.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction((function(){return ce(e,t)}))}:ce);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var he={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},pe=["Webkit","ms","Moz","O"];function ye(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||he.hasOwnProperty(e)&&he[e]?(""+t).trim():t+"px"}function me(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),i=ye(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}Object.keys(he).forEach((function(e){pe.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),he[t]=he[e]}))}));var be=P({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ge(e,t){if(t){if(be[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(o(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(o(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(o(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(o(62))}}function ve(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var we=null;function _e(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Se=null,xe=null,ke=null;function Ie(e){if(e=vi(e)){if("function"!==typeof Se)throw Error(o(280));var t=e.stateNode;t&&(t=_i(t),Se(e.stateNode,e.type,t))}}function Te(e){xe?ke?ke.push(e):ke=[e]:xe=e}function Oe(){if(xe){var e=xe,t=ke;if(ke=xe=null,Ie(e),t)for(e=0;e<t.length;e++)Ie(t[e])}}function Ae(e,t){return e(t)}function Ce(){}var Ee=!1;function Ne(e,t,n){if(Ee)return e(t,n);Ee=!0;try{return Ae(e,t,n)}finally{Ee=!1,(null!==xe||null!==ke)&&(Ce(),Oe())}}function Me(e,t){var n=e.stateNode;if(null===n)return null;var r=_i(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(o(231,t,typeof n));return n}var Be=!1;if(c)try{var Le={};Object.defineProperty(Le,"passive",{get:function(){Be=!0}}),window.addEventListener("test",Le,Le),window.removeEventListener("test",Le,Le)}catch(ce){Be=!1}function De(e,t,n,r,i,o,a,s,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Fe=!1,Pe=null,Re=!1,ze=null,Ue={onError:function(e){Fe=!0,Pe=e}};function je(e,t,n,r,i,o,a,s,l){Fe=!1,Pe=null,De.apply(Ue,arguments)}function Ve(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function We(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function $e(e){if(Ve(e)!==e)throw Error(o(188))}function He(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=Ve(e)))throw Error(o(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(null===i)break;var a=i.alternate;if(null===a){if(null!==(r=i.return)){n=r;continue}break}if(i.child===a.child){for(a=i.child;a;){if(a===n)return $e(i),e;if(a===r)return $e(i),t;a=a.sibling}throw Error(o(188))}if(n.return!==r.return)n=i,r=a;else{for(var s=!1,l=i.child;l;){if(l===n){s=!0,n=i,r=a;break}if(l===r){s=!0,r=i,n=a;break}l=l.sibling}if(!s){for(l=a.child;l;){if(l===n){s=!0,n=a,r=i;break}if(l===r){s=!0,r=a,n=i;break}l=l.sibling}if(!s)throw Error(o(189))}}if(n.alternate!==r)throw Error(o(190))}if(3!==n.tag)throw Error(o(188));return n.stateNode.current===n?e:t}(e))?Ye(e):null}function Ye(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Ye(e);if(null!==t)return t;e=e.sibling}return null}var Ke=i.unstable_scheduleCallback,Qe=i.unstable_cancelCallback,qe=i.unstable_shouldYield,Ge=i.unstable_requestPaint,Xe=i.unstable_now,Je=i.unstable_getCurrentPriorityLevel,Ze=i.unstable_ImmediatePriority,et=i.unstable_UserBlockingPriority,tt=i.unstable_NormalPriority,nt=i.unstable_LowPriority,rt=i.unstable_IdlePriority,it=null,ot=null;var at=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(st(e)/lt|0)|0},st=Math.log,lt=Math.LN2;var ut=64,ct=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,a=268435455&n;if(0!==a){var s=a&~i;0!==s?r=dt(s):0!==(o&=a)&&(r=dt(o))}else 0!==(a=n&~i)?r=dt(a):0!==o&&(r=dt(o));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&i)&&((i=r&-r)>=(o=t&-t)||16===i&&0!==(4194240&o)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)i=1<<(n=31-at(t)),r|=e[n],t&=~i;return r}function ht(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function pt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function yt(){var e=ut;return 0===(4194240&(ut<<=1))&&(ut=64),e}function mt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function bt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-at(t)]=n}function gt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-at(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var vt=0;function wt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var _t,St,xt,kt,It,Tt=!1,Ot=[],At=null,Ct=null,Et=null,Nt=new Map,Mt=new Map,Bt=[],Lt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Dt(e,t){switch(e){case"focusin":case"focusout":At=null;break;case"dragenter":case"dragleave":Ct=null;break;case"mouseover":case"mouseout":Et=null;break;case"pointerover":case"pointerout":Nt.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Mt.delete(t.pointerId)}}function Ft(e,t,n,r,i,o){return null===e||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},null!==t&&(null!==(t=vi(t))&&St(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==i&&-1===t.indexOf(i)&&t.push(i),e)}function Pt(e){var t=gi(e.target);if(null!==t){var n=Ve(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=We(n)))return e.blockedOn=t,void It(e.priority,(function(){xt(n)}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Rt(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=qt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=vi(n))&&St(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);we=r,n.target.dispatchEvent(r),we=null,t.shift()}return!0}function zt(e,t,n){Rt(e)&&n.delete(t)}function Ut(){Tt=!1,null!==At&&Rt(At)&&(At=null),null!==Ct&&Rt(Ct)&&(Ct=null),null!==Et&&Rt(Et)&&(Et=null),Nt.forEach(zt),Mt.forEach(zt)}function jt(e,t){e.blockedOn===t&&(e.blockedOn=null,Tt||(Tt=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,Ut)))}function Vt(e){function t(t){return jt(t,e)}if(0<Ot.length){jt(Ot[0],e);for(var n=1;n<Ot.length;n++){var r=Ot[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==At&&jt(At,e),null!==Ct&&jt(Ct,e),null!==Et&&jt(Et,e),Nt.forEach(t),Mt.forEach(t),n=0;n<Bt.length;n++)(r=Bt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Bt.length&&null===(n=Bt[0]).blockedOn;)Pt(n),null===n.blockedOn&&Bt.shift()}var Wt=w.ReactCurrentBatchConfig,$t=!0;function Ht(e,t,n,r){var i=vt,o=Wt.transition;Wt.transition=null;try{vt=1,Kt(e,t,n,r)}finally{vt=i,Wt.transition=o}}function Yt(e,t,n,r){var i=vt,o=Wt.transition;Wt.transition=null;try{vt=4,Kt(e,t,n,r)}finally{vt=i,Wt.transition=o}}function Kt(e,t,n,r){if($t){var i=qt(e,t,n,r);if(null===i)$r(e,t,r,Qt,n),Dt(e,r);else if(function(e,t,n,r,i){switch(t){case"focusin":return At=Ft(At,e,t,n,r,i),!0;case"dragenter":return Ct=Ft(Ct,e,t,n,r,i),!0;case"mouseover":return Et=Ft(Et,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return Nt.set(o,Ft(Nt.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,Mt.set(o,Ft(Mt.get(o)||null,e,t,n,r,i)),!0}return!1}(i,e,t,n,r))r.stopPropagation();else if(Dt(e,r),4&t&&-1<Lt.indexOf(e)){for(;null!==i;){var o=vi(i);if(null!==o&&_t(o),null===(o=qt(e,t,n,r))&&$r(e,t,r,Qt,n),o===i)break;i=o}null!==i&&r.stopPropagation()}else $r(e,t,r,null,n)}}var Qt=null;function qt(e,t,n,r){if(Qt=null,null!==(e=gi(e=_e(r))))if(null===(t=Ve(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=We(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Qt=e,null}function Gt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Je()){case Ze:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Xt=null,Jt=null,Zt=null;function en(){if(Zt)return Zt;var e,t,n=Jt,r=n.length,i="value"in Xt?Xt.value:Xt.textContent,o=i.length;for(e=0;e<r&&n[e]===i[e];e++);var a=r-e;for(t=1;t<=a&&n[r-t]===i[o-t];t++);return Zt=i.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function on(e){function t(t,n,r,i,o){for(var a in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=i,this.target=o,this.currentTarget=null,e)e.hasOwnProperty(a)&&(t=e[a],this[a]=t?t(i):i[a]);return this.isDefaultPrevented=(null!=i.defaultPrevented?i.defaultPrevented:!1===i.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return P(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var an,sn,ln,un={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cn=on(un),dn=P({},un,{view:0,detail:0}),fn=on(dn),hn=P({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:In,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ln&&(ln&&"mousemove"===e.type?(an=e.screenX-ln.screenX,sn=e.screenY-ln.screenY):sn=an=0,ln=e),an)},movementY:function(e){return"movementY"in e?e.movementY:sn}}),pn=on(hn),yn=on(P({},hn,{dataTransfer:0})),mn=on(P({},dn,{relatedTarget:0})),bn=on(P({},un,{animationName:0,elapsedTime:0,pseudoElement:0})),gn=P({},un,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),vn=on(gn),wn=on(P({},un,{data:0})),_n={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Sn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},xn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function kn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=xn[e])&&!!t[e]}function In(){return kn}var Tn=P({},dn,{key:function(e){if(e.key){var t=_n[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?Sn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:In,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),On=on(Tn),An=on(P({},hn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Cn=on(P({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:In})),En=on(P({},un,{propertyName:0,elapsedTime:0,pseudoElement:0})),Nn=P({},hn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Mn=on(Nn),Bn=[9,13,27,32],Ln=c&&"CompositionEvent"in window,Dn=null;c&&"documentMode"in document&&(Dn=document.documentMode);var Fn=c&&"TextEvent"in window&&!Dn,Pn=c&&(!Ln||Dn&&8<Dn&&11>=Dn),Rn=String.fromCharCode(32),zn=!1;function Un(e,t){switch(e){case"keyup":return-1!==Bn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function jn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Vn=!1;var Wn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function $n(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Wn[e.type]:"textarea"===t}function Hn(e,t,n,r){Te(r),0<(t=Yr(t,"onChange")).length&&(n=new cn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Yn=null,Kn=null;function Qn(e){Rr(e,0)}function qn(e){if(K(wi(e)))return e}function Gn(e,t){if("change"===e)return t}var Xn=!1;if(c){var Jn;if(c){var Zn="oninput"in document;if(!Zn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Zn="function"===typeof er.oninput}Jn=Zn}else Jn=!1;Xn=Jn&&(!document.documentMode||9<document.documentMode)}function tr(){Yn&&(Yn.detachEvent("onpropertychange",nr),Kn=Yn=null)}function nr(e){if("value"===e.propertyName&&qn(Kn)){var t=[];Hn(t,Kn,e,_e(e)),Ne(Qn,t)}}function rr(e,t,n){"focusin"===e?(tr(),Kn=n,(Yn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function ir(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return qn(Kn)}function or(e,t){if("click"===e)return qn(t)}function ar(e,t){if("input"===e||"change"===e)return qn(t)}var sr="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function lr(e,t){if(sr(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!d.call(t,i)||!sr(e[i],t[i]))return!1}return!0}function ur(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function cr(e,t){var n,r=ur(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=ur(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=Q();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=Q((e=t.contentWindow).document)}return t}function hr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function pr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&hr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=void 0===r.end?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=cr(n,o);var a=cr(n,r);i&&a&&(1!==e.rangeCount||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&((t=t.createRange()).setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var yr=c&&"documentMode"in document&&11>=document.documentMode,mr=null,br=null,gr=null,vr=!1;function wr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;vr||null==mr||mr!==Q(r)||("selectionStart"in(r=mr)&&hr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},gr&&lr(gr,r)||(gr=r,0<(r=Yr(br,"onSelect")).length&&(t=new cn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=mr)))}function _r(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Sr={animationend:_r("Animation","AnimationEnd"),animationiteration:_r("Animation","AnimationIteration"),animationstart:_r("Animation","AnimationStart"),transitionend:_r("Transition","TransitionEnd")},xr={},kr={};function Ir(e){if(xr[e])return xr[e];if(!Sr[e])return e;var t,n=Sr[e];for(t in n)if(n.hasOwnProperty(t)&&t in kr)return xr[e]=n[t];return e}c&&(kr=document.createElement("div").style,"AnimationEvent"in window||(delete Sr.animationend.animation,delete Sr.animationiteration.animation,delete Sr.animationstart.animation),"TransitionEvent"in window||delete Sr.transitionend.transition);var Tr=Ir("animationend"),Or=Ir("animationiteration"),Ar=Ir("animationstart"),Cr=Ir("transitionend"),Er=new Map,Nr="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Mr(e,t){Er.set(e,t),l(t,[e])}for(var Br=0;Br<Nr.length;Br++){var Lr=Nr[Br];Mr(Lr.toLowerCase(),"on"+(Lr[0].toUpperCase()+Lr.slice(1)))}Mr(Tr,"onAnimationEnd"),Mr(Or,"onAnimationIteration"),Mr(Ar,"onAnimationStart"),Mr("dblclick","onDoubleClick"),Mr("focusin","onFocus"),Mr("focusout","onBlur"),Mr(Cr,"onTransitionEnd"),u("onMouseEnter",["mouseout","mouseover"]),u("onMouseLeave",["mouseout","mouseover"]),u("onPointerEnter",["pointerout","pointerover"]),u("onPointerLeave",["pointerout","pointerover"]),l("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),l("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),l("onBeforeInput",["compositionend","keypress","textInput","paste"]),l("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Dr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Fr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Dr));function Pr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,i,a,s,l,u){if(je.apply(this,arguments),Fe){if(!Fe)throw Error(o(198));var c=Pe;Fe=!1,Pe=null,Re||(Re=!0,ze=c)}}(r,t,void 0,e),e.currentTarget=null}function Rr(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var a=r.length-1;0<=a;a--){var s=r[a],l=s.instance,u=s.currentTarget;if(s=s.listener,l!==o&&i.isPropagationStopped())break e;Pr(i,s,u),o=l}else for(a=0;a<r.length;a++){if(l=(s=r[a]).instance,u=s.currentTarget,s=s.listener,l!==o&&i.isPropagationStopped())break e;Pr(i,s,u),o=l}}}if(Re)throw e=ze,Re=!1,ze=null,e}function zr(e,t){var n=t[yi];void 0===n&&(n=t[yi]=new Set);var r=e+"__bubble";n.has(r)||(Wr(t,e,2,!1),n.add(r))}function Ur(e,t,n){var r=0;t&&(r|=4),Wr(n,e,r,t)}var jr="_reactListening"+Math.random().toString(36).slice(2);function Vr(e){if(!e[jr]){e[jr]=!0,a.forEach((function(t){"selectionchange"!==t&&(Fr.has(t)||Ur(t,!1,e),Ur(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[jr]||(t[jr]=!0,Ur("selectionchange",!1,t))}}function Wr(e,t,n,r){switch(Gt(t)){case 1:var i=Ht;break;case 4:i=Yt;break;default:i=Kt}n=i.bind(null,t,n,e),i=void 0,!Be||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(i=!0),r?void 0!==i?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):void 0!==i?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function $r(e,t,n,r,i){var o=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var a=r.tag;if(3===a||4===a){var s=r.stateNode.containerInfo;if(s===i||8===s.nodeType&&s.parentNode===i)break;if(4===a)for(a=r.return;null!==a;){var l=a.tag;if((3===l||4===l)&&((l=a.stateNode.containerInfo)===i||8===l.nodeType&&l.parentNode===i))return;a=a.return}for(;null!==s;){if(null===(a=gi(s)))return;if(5===(l=a.tag)||6===l){r=o=a;continue e}s=s.parentNode}}r=r.return}Ne((function(){var r=o,i=_e(n),a=[];e:{var s=Er.get(e);if(void 0!==s){var l=cn,u=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":l=On;break;case"focusin":u="focus",l=mn;break;case"focusout":u="blur",l=mn;break;case"beforeblur":case"afterblur":l=mn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":l=pn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":l=yn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":l=Cn;break;case Tr:case Or:case Ar:l=bn;break;case Cr:l=En;break;case"scroll":l=fn;break;case"wheel":l=Mn;break;case"copy":case"cut":case"paste":l=vn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":l=An}var c=0!==(4&t),d=!c&&"scroll"===e,f=c?null!==s?s+"Capture":null:s;c=[];for(var h,p=r;null!==p;){var y=(h=p).stateNode;if(5===h.tag&&null!==y&&(h=y,null!==f&&(null!=(y=Me(p,f))&&c.push(Hr(p,y,h)))),d)break;p=p.return}0<c.length&&(s=new l(s,u,null,n,i),a.push({event:s,listeners:c}))}}if(0===(7&t)){if(l="mouseout"===e||"pointerout"===e,(!(s="mouseover"===e||"pointerover"===e)||n===we||!(u=n.relatedTarget||n.fromElement)||!gi(u)&&!u[pi])&&(l||s)&&(s=i.window===i?i:(s=i.ownerDocument)?s.defaultView||s.parentWindow:window,l?(l=r,null!==(u=(u=n.relatedTarget||n.toElement)?gi(u):null)&&(u!==(d=Ve(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(l=null,u=r),l!==u)){if(c=pn,y="onMouseLeave",f="onMouseEnter",p="mouse","pointerout"!==e&&"pointerover"!==e||(c=An,y="onPointerLeave",f="onPointerEnter",p="pointer"),d=null==l?s:wi(l),h=null==u?s:wi(u),(s=new c(y,p+"leave",l,n,i)).target=d,s.relatedTarget=h,y=null,gi(i)===r&&((c=new c(f,p+"enter",u,n,i)).target=h,c.relatedTarget=d,y=c),d=y,l&&u)e:{for(f=u,p=0,h=c=l;h;h=Kr(h))p++;for(h=0,y=f;y;y=Kr(y))h++;for(;0<p-h;)c=Kr(c),p--;for(;0<h-p;)f=Kr(f),h--;for(;p--;){if(c===f||null!==f&&c===f.alternate)break e;c=Kr(c),f=Kr(f)}c=null}else c=null;null!==l&&Qr(a,s,l,c,!1),null!==u&&null!==d&&Qr(a,d,u,c,!0)}if("select"===(l=(s=r?wi(r):window).nodeName&&s.nodeName.toLowerCase())||"input"===l&&"file"===s.type)var m=Gn;else if($n(s))if(Xn)m=ar;else{m=ir;var b=rr}else(l=s.nodeName)&&"input"===l.toLowerCase()&&("checkbox"===s.type||"radio"===s.type)&&(m=or);switch(m&&(m=m(e,r))?Hn(a,m,n,i):(b&&b(e,s,r),"focusout"===e&&(b=s._wrapperState)&&b.controlled&&"number"===s.type&&ee(s,"number",s.value)),b=r?wi(r):window,e){case"focusin":($n(b)||"true"===b.contentEditable)&&(mr=b,br=r,gr=null);break;case"focusout":gr=br=mr=null;break;case"mousedown":vr=!0;break;case"contextmenu":case"mouseup":case"dragend":vr=!1,wr(a,n,i);break;case"selectionchange":if(yr)break;case"keydown":case"keyup":wr(a,n,i)}var g;if(Ln)e:{switch(e){case"compositionstart":var v="onCompositionStart";break e;case"compositionend":v="onCompositionEnd";break e;case"compositionupdate":v="onCompositionUpdate";break e}v=void 0}else Vn?Un(e,n)&&(v="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(v="onCompositionStart");v&&(Pn&&"ko"!==n.locale&&(Vn||"onCompositionStart"!==v?"onCompositionEnd"===v&&Vn&&(g=en()):(Jt="value"in(Xt=i)?Xt.value:Xt.textContent,Vn=!0)),0<(b=Yr(r,v)).length&&(v=new wn(v,e,null,n,i),a.push({event:v,listeners:b}),g?v.data=g:null!==(g=jn(n))&&(v.data=g))),(g=Fn?function(e,t){switch(e){case"compositionend":return jn(t);case"keypress":return 32!==t.which?null:(zn=!0,Rn);case"textInput":return(e=t.data)===Rn&&zn?null:e;default:return null}}(e,n):function(e,t){if(Vn)return"compositionend"===e||!Ln&&Un(e,t)?(e=en(),Zt=Jt=Xt=null,Vn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Pn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=Yr(r,"onBeforeInput")).length&&(i=new wn("onBeforeInput","beforeinput",null,n,i),a.push({event:i,listeners:r}),i.data=g))}Rr(a,t)}))}function Hr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Yr(e,t){for(var n=t+"Capture",r=[];null!==e;){var i=e,o=i.stateNode;5===i.tag&&null!==o&&(i=o,null!=(o=Me(e,n))&&r.unshift(Hr(e,o,i)),null!=(o=Me(e,t))&&r.push(Hr(e,o,i))),e=e.return}return r}function Kr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Qr(e,t,n,r,i){for(var o=t._reactName,a=[];null!==n&&n!==r;){var s=n,l=s.alternate,u=s.stateNode;if(null!==l&&l===r)break;5===s.tag&&null!==u&&(s=u,i?null!=(l=Me(n,o))&&a.unshift(Hr(n,l,s)):i||null!=(l=Me(n,o))&&a.push(Hr(n,l,s))),n=n.return}0!==a.length&&e.push({event:t,listeners:a})}var qr=/\r\n?/g,Gr=/\u0000|\uFFFD/g;function Xr(e){return("string"===typeof e?e:""+e).replace(qr,"\n").replace(Gr,"")}function Jr(e,t,n){if(t=Xr(t),Xr(e)!==t&&n)throw Error(o(425))}function Zr(){}var ei=null,ti=null;function ni(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ri="function"===typeof setTimeout?setTimeout:void 0,ii="function"===typeof clearTimeout?clearTimeout:void 0,oi="function"===typeof Promise?Promise:void 0,ai="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof oi?function(e){return oi.resolve(null).then(e).catch(si)}:ri;function si(e){setTimeout((function(){throw e}))}function li(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&8===i.nodeType)if("/$"===(n=i.data)){if(0===r)return e.removeChild(i),void Vt(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=i}while(n);Vt(t)}function ui(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ci(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var di=Math.random().toString(36).slice(2),fi="__reactFiber$"+di,hi="__reactProps$"+di,pi="__reactContainer$"+di,yi="__reactEvents$"+di,mi="__reactListeners$"+di,bi="__reactHandles$"+di;function gi(e){var t=e[fi];if(t)return t;for(var n=e.parentNode;n;){if(t=n[pi]||n[fi]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ci(e);null!==e;){if(n=e[fi])return n;e=ci(e)}return t}n=(e=n).parentNode}return null}function vi(e){return!(e=e[fi]||e[pi])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function wi(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(o(33))}function _i(e){return e[hi]||null}var Si=[],xi=-1;function ki(e){return{current:e}}function Ii(e){0>xi||(e.current=Si[xi],Si[xi]=null,xi--)}function Ti(e,t){xi++,Si[xi]=e.current,e.current=t}var Oi={},Ai=ki(Oi),Ci=ki(!1),Ei=Oi;function Ni(e,t){var n=e.type.contextTypes;if(!n)return Oi;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i,o={};for(i in n)o[i]=t[i];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Mi(e){return null!==(e=e.childContextTypes)&&void 0!==e}function Bi(){Ii(Ci),Ii(Ai)}function Li(e,t,n){if(Ai.current!==Oi)throw Error(o(168));Ti(Ai,t),Ti(Ci,n)}function Di(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var i in r=r.getChildContext())if(!(i in t))throw Error(o(108,W(e)||"Unknown",i));return P({},n,r)}function Fi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Oi,Ei=Ai.current,Ti(Ai,e),Ti(Ci,Ci.current),!0}function Pi(e,t,n){var r=e.stateNode;if(!r)throw Error(o(169));n?(e=Di(e,t,Ei),r.__reactInternalMemoizedMergedChildContext=e,Ii(Ci),Ii(Ai),Ti(Ai,e)):Ii(Ci),Ti(Ci,n)}var Ri=null,zi=!1,Ui=!1;function ji(e){null===Ri?Ri=[e]:Ri.push(e)}function Vi(){if(!Ui&&null!==Ri){Ui=!0;var e=0,t=vt;try{var n=Ri;for(vt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Ri=null,zi=!1}catch(i){throw null!==Ri&&(Ri=Ri.slice(e+1)),Ke(Ze,Vi),i}finally{vt=t,Ui=!1}}return null}var Wi=[],$i=0,Hi=null,Yi=0,Ki=[],Qi=0,qi=null,Gi=1,Xi="";function Ji(e,t){Wi[$i++]=Yi,Wi[$i++]=Hi,Hi=e,Yi=t}function Zi(e,t,n){Ki[Qi++]=Gi,Ki[Qi++]=Xi,Ki[Qi++]=qi,qi=e;var r=Gi;e=Xi;var i=32-at(r)-1;r&=~(1<<i),n+=1;var o=32-at(t)+i;if(30<o){var a=i-i%5;o=(r&(1<<a)-1).toString(32),r>>=a,i-=a,Gi=1<<32-at(t)+i|n<<i|r,Xi=o+e}else Gi=1<<o|n<<i|r,Xi=e}function eo(e){null!==e.return&&(Ji(e,1),Zi(e,1,0))}function to(e){for(;e===Hi;)Hi=Wi[--$i],Wi[$i]=null,Yi=Wi[--$i],Wi[$i]=null;for(;e===qi;)qi=Ki[--Qi],Ki[Qi]=null,Xi=Ki[--Qi],Ki[Qi]=null,Gi=Ki[--Qi],Ki[Qi]=null}var no=null,ro=null,io=!1,oo=null;function ao(e,t){var n=Mu(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function so(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,no=e,ro=ui(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,no=e,ro=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==qi?{id:Gi,overflow:Xi}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Mu(18,null,null,0)).stateNode=t,n.return=e,e.child=n,no=e,ro=null,!0);default:return!1}}function lo(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function uo(e){if(io){var t=ro;if(t){var n=t;if(!so(e,t)){if(lo(e))throw Error(o(418));t=ui(n.nextSibling);var r=no;t&&so(e,t)?ao(r,n):(e.flags=-4097&e.flags|2,io=!1,no=e)}}else{if(lo(e))throw Error(o(418));e.flags=-4097&e.flags|2,io=!1,no=e}}}function co(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;no=e}function fo(e){if(e!==no)return!1;if(!io)return co(e),io=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!ni(e.type,e.memoizedProps)),t&&(t=ro)){if(lo(e))throw ho(),Error(o(418));for(;t;)ao(e,t),t=ui(t.nextSibling)}if(co(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){ro=ui(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}ro=null}}else ro=no?ui(e.stateNode.nextSibling):null;return!0}function ho(){for(var e=ro;e;)e=ui(e.nextSibling)}function po(){ro=no=null,io=!1}function yo(e){null===oo?oo=[e]:oo.push(e)}var mo=w.ReactCurrentBatchConfig;function bo(e,t){if(e&&e.defaultProps){for(var n in t=P({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}var go=ki(null),vo=null,wo=null,_o=null;function So(){_o=wo=vo=null}function xo(e){var t=go.current;Ii(go),e._currentValue=t}function ko(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Io(e,t){vo=e,_o=wo=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(ws=!0),e.firstContext=null)}function To(e){var t=e._currentValue;if(_o!==e)if(e={context:e,memoizedValue:t,next:null},null===wo){if(null===vo)throw Error(o(308));wo=e,vo.dependencies={lanes:0,firstContext:e}}else wo=wo.next=e;return t}var Oo=null;function Ao(e){null===Oo?Oo=[e]:Oo.push(e)}function Co(e,t,n,r){var i=t.interleaved;return null===i?(n.next=n,Ao(t)):(n.next=i.next,i.next=n),t.interleaved=n,Eo(e,r)}function Eo(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var No=!1;function Mo(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Bo(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Lo(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Do(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&Cl)){var i=r.pending;return null===i?t.next=t:(t.next=i.next,i.next=t),r.pending=t,Eo(e,n)}return null===(i=r.interleaved)?(t.next=t,Ao(r)):(t.next=i.next,i.next=t),r.interleaved=t,Eo(e,n)}function Fo(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,gt(e,n)}}function Po(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var i=null,o=null;if(null!==(n=n.firstBaseUpdate)){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===o?i=o=a:o=o.next=a,n=n.next}while(null!==n);null===o?i=o=t:o=o.next=t}else i=o=t;return n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ro(e,t,n,r){var i=e.updateQueue;No=!1;var o=i.firstBaseUpdate,a=i.lastBaseUpdate,s=i.shared.pending;if(null!==s){i.shared.pending=null;var l=s,u=l.next;l.next=null,null===a?o=u:a.next=u,a=l;var c=e.alternate;null!==c&&((s=(c=c.updateQueue).lastBaseUpdate)!==a&&(null===s?c.firstBaseUpdate=u:s.next=u,c.lastBaseUpdate=l))}if(null!==o){var d=i.baseState;for(a=0,c=u=l=null,s=o;;){var f=s.lane,h=s.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:h,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var p=e,y=s;switch(f=t,h=n,y.tag){case 1:if("function"===typeof(p=y.payload)){d=p.call(h,d,f);break e}d=p;break e;case 3:p.flags=-65537&p.flags|128;case 0:if(null===(f="function"===typeof(p=y.payload)?p.call(h,d,f):p)||void 0===f)break e;d=P({},d,f);break e;case 2:No=!0}}null!==s.callback&&0!==s.lane&&(e.flags|=64,null===(f=i.effects)?i.effects=[s]:f.push(s))}else h={eventTime:h,lane:f,tag:s.tag,payload:s.payload,callback:s.callback,next:null},null===c?(u=c=h,l=d):c=c.next=h,a|=f;if(null===(s=s.next)){if(null===(s=i.shared.pending))break;s=(f=s).next,f.next=null,i.lastBaseUpdate=f,i.shared.pending=null}}if(null===c&&(l=d),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=c,null!==(t=i.shared.interleaved)){i=t;do{a|=i.lane,i=i.next}while(i!==t)}else null===o&&(i.shared.lanes=0);Pl|=a,e.lanes=a,e.memoizedState=d}}function zo(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(null!==i){if(r.callback=null,r=n,"function"!==typeof i)throw Error(o(191,i));i.call(r)}}}var Uo=(new r.Component).refs;function jo(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:P({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var Vo={isMounted:function(e){return!!(e=e._reactInternals)&&Ve(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=tu(),i=nu(e),o=Lo(r,i);o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=Do(e,o,i))&&(ru(t,e,i,r),Fo(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=tu(),i=nu(e),o=Lo(r,i);o.tag=1,o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=Do(e,o,i))&&(ru(t,e,i,r),Fo(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=tu(),r=nu(e),i=Lo(n,r);i.tag=2,void 0!==t&&null!==t&&(i.callback=t),null!==(t=Do(e,i,r))&&(ru(t,e,r,n),Fo(t,e,r))}};function Wo(e,t,n,r,i,o,a){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,o,a):!t.prototype||!t.prototype.isPureReactComponent||(!lr(n,r)||!lr(i,o))}function $o(e,t,n){var r=!1,i=Oi,o=t.contextType;return"object"===typeof o&&null!==o?o=To(o):(i=Mi(t)?Ei:Ai.current,o=(r=null!==(r=t.contextTypes)&&void 0!==r)?Ni(e,i):Oi),t=new t(n,o),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=Vo,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function Ho(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Vo.enqueueReplaceState(t,t.state,null)}function Yo(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs=Uo,Mo(e);var o=t.contextType;"object"===typeof o&&null!==o?i.context=To(o):(o=Mi(t)?Ei:Ai.current,i.context=Ni(e,o)),i.state=e.memoizedState,"function"===typeof(o=t.getDerivedStateFromProps)&&(jo(e,t,o,n),i.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof i.getSnapshotBeforeUpdate||"function"!==typeof i.UNSAFE_componentWillMount&&"function"!==typeof i.componentWillMount||(t=i.state,"function"===typeof i.componentWillMount&&i.componentWillMount(),"function"===typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount(),t!==i.state&&Vo.enqueueReplaceState(i,i.state,null),Ro(e,n,i,r),i.state=e.memoizedState),"function"===typeof i.componentDidMount&&(e.flags|=4194308)}function Ko(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(o(309));var r=n.stateNode}if(!r)throw Error(o(147,e));var i=r,a=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===a?t.ref:(t=function(e){var t=i.refs;t===Uo&&(t=i.refs={}),null===e?delete t[a]:t[a]=e},t._stringRef=a,t)}if("string"!==typeof e)throw Error(o(284));if(!n._owner)throw Error(o(290,e))}return e}function Qo(e,t){throw e=Object.prototype.toString.call(t),Error(o(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function qo(e){return(0,e._init)(e._payload)}function Go(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function i(e,t){return(e=Lu(e,t)).index=0,e.sibling=null,e}function a(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function s(t){return e&&null===t.alternate&&(t.flags|=2),t}function l(e,t,n,r){return null===t||6!==t.tag?((t=Ru(n,e.mode,r)).return=e,t):((t=i(t,n)).return=e,t)}function u(e,t,n,r){var o=n.type;return o===x?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===o||"object"===typeof o&&null!==o&&o.$$typeof===M&&qo(o)===t.type)?((r=i(t,n.props)).ref=Ko(e,t,n),r.return=e,r):((r=Du(n.type,n.key,n.props,null,e.mode,r)).ref=Ko(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=zu(n,e.mode,r)).return=e,t):((t=i(t,n.children||[])).return=e,t)}function d(e,t,n,r,o){return null===t||7!==t.tag?((t=Fu(n,e.mode,r,o)).return=e,t):((t=i(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Ru(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case _:return(n=Du(t.type,t.key,t.props,null,e.mode,n)).ref=Ko(e,null,t),n.return=e,n;case S:return(t=zu(t,e.mode,n)).return=e,t;case M:return f(e,(0,t._init)(t._payload),n)}if(te(t)||D(t))return(t=Fu(t,e.mode,n,null)).return=e,t;Qo(e,t)}return null}function h(e,t,n,r){var i=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==i?null:l(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case _:return n.key===i?u(e,t,n,r):null;case S:return n.key===i?c(e,t,n,r):null;case M:return h(e,t,(i=n._init)(n._payload),r)}if(te(n)||D(n))return null!==i?null:d(e,t,n,r,null);Qo(e,n)}return null}function p(e,t,n,r,i){if("string"===typeof r&&""!==r||"number"===typeof r)return l(t,e=e.get(n)||null,""+r,i);if("object"===typeof r&&null!==r){switch(r.$$typeof){case _:return u(t,e=e.get(null===r.key?n:r.key)||null,r,i);case S:return c(t,e=e.get(null===r.key?n:r.key)||null,r,i);case M:return p(e,t,n,(0,r._init)(r._payload),i)}if(te(r)||D(r))return d(t,e=e.get(n)||null,r,i,null);Qo(t,r)}return null}function y(i,o,s,l){for(var u=null,c=null,d=o,y=o=0,m=null;null!==d&&y<s.length;y++){d.index>y?(m=d,d=null):m=d.sibling;var b=h(i,d,s[y],l);if(null===b){null===d&&(d=m);break}e&&d&&null===b.alternate&&t(i,d),o=a(b,o,y),null===c?u=b:c.sibling=b,c=b,d=m}if(y===s.length)return n(i,d),io&&Ji(i,y),u;if(null===d){for(;y<s.length;y++)null!==(d=f(i,s[y],l))&&(o=a(d,o,y),null===c?u=d:c.sibling=d,c=d);return io&&Ji(i,y),u}for(d=r(i,d);y<s.length;y++)null!==(m=p(d,i,y,s[y],l))&&(e&&null!==m.alternate&&d.delete(null===m.key?y:m.key),o=a(m,o,y),null===c?u=m:c.sibling=m,c=m);return e&&d.forEach((function(e){return t(i,e)})),io&&Ji(i,y),u}function m(i,s,l,u){var c=D(l);if("function"!==typeof c)throw Error(o(150));if(null==(l=c.call(l)))throw Error(o(151));for(var d=c=null,y=s,m=s=0,b=null,g=l.next();null!==y&&!g.done;m++,g=l.next()){y.index>m?(b=y,y=null):b=y.sibling;var v=h(i,y,g.value,u);if(null===v){null===y&&(y=b);break}e&&y&&null===v.alternate&&t(i,y),s=a(v,s,m),null===d?c=v:d.sibling=v,d=v,y=b}if(g.done)return n(i,y),io&&Ji(i,m),c;if(null===y){for(;!g.done;m++,g=l.next())null!==(g=f(i,g.value,u))&&(s=a(g,s,m),null===d?c=g:d.sibling=g,d=g);return io&&Ji(i,m),c}for(y=r(i,y);!g.done;m++,g=l.next())null!==(g=p(y,i,m,g.value,u))&&(e&&null!==g.alternate&&y.delete(null===g.key?m:g.key),s=a(g,s,m),null===d?c=g:d.sibling=g,d=g);return e&&y.forEach((function(e){return t(i,e)})),io&&Ji(i,m),c}return function e(r,o,a,l){if("object"===typeof a&&null!==a&&a.type===x&&null===a.key&&(a=a.props.children),"object"===typeof a&&null!==a){switch(a.$$typeof){case _:e:{for(var u=a.key,c=o;null!==c;){if(c.key===u){if((u=a.type)===x){if(7===c.tag){n(r,c.sibling),(o=i(c,a.props.children)).return=r,r=o;break e}}else if(c.elementType===u||"object"===typeof u&&null!==u&&u.$$typeof===M&&qo(u)===c.type){n(r,c.sibling),(o=i(c,a.props)).ref=Ko(r,c,a),o.return=r,r=o;break e}n(r,c);break}t(r,c),c=c.sibling}a.type===x?((o=Fu(a.props.children,r.mode,l,a.key)).return=r,r=o):((l=Du(a.type,a.key,a.props,null,r.mode,l)).ref=Ko(r,o,a),l.return=r,r=l)}return s(r);case S:e:{for(c=a.key;null!==o;){if(o.key===c){if(4===o.tag&&o.stateNode.containerInfo===a.containerInfo&&o.stateNode.implementation===a.implementation){n(r,o.sibling),(o=i(o,a.children||[])).return=r,r=o;break e}n(r,o);break}t(r,o),o=o.sibling}(o=zu(a,r.mode,l)).return=r,r=o}return s(r);case M:return e(r,o,(c=a._init)(a._payload),l)}if(te(a))return y(r,o,a,l);if(D(a))return m(r,o,a,l);Qo(r,a)}return"string"===typeof a&&""!==a||"number"===typeof a?(a=""+a,null!==o&&6===o.tag?(n(r,o.sibling),(o=i(o,a)).return=r,r=o):(n(r,o),(o=Ru(a,r.mode,l)).return=r,r=o),s(r)):n(r,o)}}var Xo=Go(!0),Jo=Go(!1),Zo={},ea=ki(Zo),ta=ki(Zo),na=ki(Zo);function ra(e){if(e===Zo)throw Error(o(174));return e}function ia(e,t){switch(Ti(na,t),Ti(ta,e),Ti(ea,Zo),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:le(null,"");break;default:t=le(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Ii(ea),Ti(ea,t)}function oa(){Ii(ea),Ii(ta),Ii(na)}function aa(e){ra(na.current);var t=ra(ea.current),n=le(t,e.type);t!==n&&(Ti(ta,e),Ti(ea,n))}function sa(e){ta.current===e&&(Ii(ea),Ii(ta))}var la=ki(0);function ua(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ca=[];function da(){for(var e=0;e<ca.length;e++)ca[e]._workInProgressVersionPrimary=null;ca.length=0}var fa=w.ReactCurrentDispatcher,ha=w.ReactCurrentBatchConfig,pa=0,ya=null,ma=null,ba=null,ga=!1,va=!1,wa=0,_a=0;function Sa(){throw Error(o(321))}function xa(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!sr(e[n],t[n]))return!1;return!0}function ka(e,t,n,r,i,a){if(pa=a,ya=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,fa.current=null===e||null===e.memoizedState?ss:ls,e=n(r,i),va){a=0;do{if(va=!1,wa=0,25<=a)throw Error(o(301));a+=1,ba=ma=null,t.updateQueue=null,fa.current=us,e=n(r,i)}while(va)}if(fa.current=as,t=null!==ma&&null!==ma.next,pa=0,ba=ma=ya=null,ga=!1,t)throw Error(o(300));return e}function Ia(){var e=0!==wa;return wa=0,e}function Ta(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===ba?ya.memoizedState=ba=e:ba=ba.next=e,ba}function Oa(){if(null===ma){var e=ya.alternate;e=null!==e?e.memoizedState:null}else e=ma.next;var t=null===ba?ya.memoizedState:ba.next;if(null!==t)ba=t,ma=e;else{if(null===e)throw Error(o(310));e={memoizedState:(ma=e).memoizedState,baseState:ma.baseState,baseQueue:ma.baseQueue,queue:ma.queue,next:null},null===ba?ya.memoizedState=ba=e:ba=ba.next=e}return ba}function Aa(e,t){return"function"===typeof t?t(e):t}function Ca(e){var t=Oa(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=ma,i=r.baseQueue,a=n.pending;if(null!==a){if(null!==i){var s=i.next;i.next=a.next,a.next=s}r.baseQueue=i=a,n.pending=null}if(null!==i){a=i.next,r=r.baseState;var l=s=null,u=null,c=a;do{var d=c.lane;if((pa&d)===d)null!==u&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===u?(l=u=f,s=r):u=u.next=f,ya.lanes|=d,Pl|=d}c=c.next}while(null!==c&&c!==a);null===u?s=r:u.next=l,sr(r,t.memoizedState)||(ws=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=u,n.lastRenderedState=r}if(null!==(e=n.interleaved)){i=e;do{a=i.lane,ya.lanes|=a,Pl|=a,i=i.next}while(i!==e)}else null===i&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Ea(e){var t=Oa(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,a=t.memoizedState;if(null!==i){n.pending=null;var s=i=i.next;do{a=e(a,s.action),s=s.next}while(s!==i);sr(a,t.memoizedState)||(ws=!0),t.memoizedState=a,null===t.baseQueue&&(t.baseState=a),n.lastRenderedState=a}return[a,r]}function Na(){}function Ma(e,t){var n=ya,r=Oa(),i=t(),a=!sr(r.memoizedState,i);if(a&&(r.memoizedState=i,ws=!0),r=r.queue,$a(Da.bind(null,n,r,e),[e]),r.getSnapshot!==t||a||null!==ba&&1&ba.memoizedState.tag){if(n.flags|=2048,za(9,La.bind(null,n,r,i,t),void 0,null),null===El)throw Error(o(349));0!==(30&pa)||Ba(n,t,i)}return i}function Ba(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=ya.updateQueue)?(t={lastEffect:null,stores:null},ya.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function La(e,t,n,r){t.value=n,t.getSnapshot=r,Fa(t)&&Pa(e)}function Da(e,t,n){return n((function(){Fa(t)&&Pa(e)}))}function Fa(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!sr(e,n)}catch(r){return!0}}function Pa(e){var t=Eo(e,1);null!==t&&ru(t,e,1,-1)}function Ra(e){var t=Ta();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Aa,lastRenderedState:e},t.queue=e,e=e.dispatch=ns.bind(null,ya,e),[t.memoizedState,e]}function za(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=ya.updateQueue)?(t={lastEffect:null,stores:null},ya.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Ua(){return Oa().memoizedState}function ja(e,t,n,r){var i=Ta();ya.flags|=e,i.memoizedState=za(1|t,n,void 0,void 0===r?null:r)}function Va(e,t,n,r){var i=Oa();r=void 0===r?null:r;var o=void 0;if(null!==ma){var a=ma.memoizedState;if(o=a.destroy,null!==r&&xa(r,a.deps))return void(i.memoizedState=za(t,n,o,r))}ya.flags|=e,i.memoizedState=za(1|t,n,o,r)}function Wa(e,t){return ja(8390656,8,e,t)}function $a(e,t){return Va(2048,8,e,t)}function Ha(e,t){return Va(4,2,e,t)}function Ya(e,t){return Va(4,4,e,t)}function Ka(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Qa(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Va(4,4,Ka.bind(null,t,e),n)}function qa(){}function Ga(e,t){var n=Oa();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&xa(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Xa(e,t){var n=Oa();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&xa(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Ja(e,t,n){return 0===(21&pa)?(e.baseState&&(e.baseState=!1,ws=!0),e.memoizedState=n):(sr(n,t)||(n=yt(),ya.lanes|=n,Pl|=n,e.baseState=!0),t)}function Za(e,t){var n=vt;vt=0!==n&&4>n?n:4,e(!0);var r=ha.transition;ha.transition={};try{e(!1),t()}finally{vt=n,ha.transition=r}}function es(){return Oa().memoizedState}function ts(e,t,n){var r=nu(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},rs(e))is(t,n);else if(null!==(n=Co(e,t,n,r))){ru(n,e,r,tu()),os(n,t,r)}}function ns(e,t,n){var r=nu(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(rs(e))is(t,i);else{var o=e.alternate;if(0===e.lanes&&(null===o||0===o.lanes)&&null!==(o=t.lastRenderedReducer))try{var a=t.lastRenderedState,s=o(a,n);if(i.hasEagerState=!0,i.eagerState=s,sr(s,a)){var l=t.interleaved;return null===l?(i.next=i,Ao(t)):(i.next=l.next,l.next=i),void(t.interleaved=i)}}catch(u){}null!==(n=Co(e,t,i,r))&&(ru(n,e,r,i=tu()),os(n,t,r))}}function rs(e){var t=e.alternate;return e===ya||null!==t&&t===ya}function is(e,t){va=ga=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function os(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,gt(e,n)}}var as={readContext:To,useCallback:Sa,useContext:Sa,useEffect:Sa,useImperativeHandle:Sa,useInsertionEffect:Sa,useLayoutEffect:Sa,useMemo:Sa,useReducer:Sa,useRef:Sa,useState:Sa,useDebugValue:Sa,useDeferredValue:Sa,useTransition:Sa,useMutableSource:Sa,useSyncExternalStore:Sa,useId:Sa,unstable_isNewReconciler:!1},ss={readContext:To,useCallback:function(e,t){return Ta().memoizedState=[e,void 0===t?null:t],e},useContext:To,useEffect:Wa,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,ja(4194308,4,Ka.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ja(4194308,4,e,t)},useInsertionEffect:function(e,t){return ja(4,2,e,t)},useMemo:function(e,t){var n=Ta();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Ta();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=ts.bind(null,ya,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Ta().memoizedState=e},useState:Ra,useDebugValue:qa,useDeferredValue:function(e){return Ta().memoizedState=e},useTransition:function(){var e=Ra(!1),t=e[0];return e=Za.bind(null,e[1]),Ta().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ya,i=Ta();if(io){if(void 0===n)throw Error(o(407));n=n()}else{if(n=t(),null===El)throw Error(o(349));0!==(30&pa)||Ba(r,t,n)}i.memoizedState=n;var a={value:n,getSnapshot:t};return i.queue=a,Wa(Da.bind(null,r,a,e),[e]),r.flags|=2048,za(9,La.bind(null,r,a,n,t),void 0,null),n},useId:function(){var e=Ta(),t=El.identifierPrefix;if(io){var n=Xi;t=":"+t+"R"+(n=(Gi&~(1<<32-at(Gi)-1)).toString(32)+n),0<(n=wa++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=_a++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},ls={readContext:To,useCallback:Ga,useContext:To,useEffect:$a,useImperativeHandle:Qa,useInsertionEffect:Ha,useLayoutEffect:Ya,useMemo:Xa,useReducer:Ca,useRef:Ua,useState:function(){return Ca(Aa)},useDebugValue:qa,useDeferredValue:function(e){return Ja(Oa(),ma.memoizedState,e)},useTransition:function(){return[Ca(Aa)[0],Oa().memoizedState]},useMutableSource:Na,useSyncExternalStore:Ma,useId:es,unstable_isNewReconciler:!1},us={readContext:To,useCallback:Ga,useContext:To,useEffect:$a,useImperativeHandle:Qa,useInsertionEffect:Ha,useLayoutEffect:Ya,useMemo:Xa,useReducer:Ea,useRef:Ua,useState:function(){return Ea(Aa)},useDebugValue:qa,useDeferredValue:function(e){var t=Oa();return null===ma?t.memoizedState=e:Ja(t,ma.memoizedState,e)},useTransition:function(){return[Ea(Aa)[0],Oa().memoizedState]},useMutableSource:Na,useSyncExternalStore:Ma,useId:es,unstable_isNewReconciler:!1};function cs(e,t){try{var n="",r=t;do{n+=j(r),r=r.return}while(r);var i=n}catch(o){i="\nError generating stack: "+o.message+"\n"+o.stack}return{value:e,source:t,stack:i,digest:null}}function ds(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function fs(e,t){try{console.error(t.value)}catch(n){setTimeout((function(){throw n}))}}var hs="function"===typeof WeakMap?WeakMap:Map;function ps(e,t,n){(n=Lo(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Hl||(Hl=!0,Yl=r),fs(0,t)},n}function ys(e,t,n){(n=Lo(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){fs(0,t)}}var o=e.stateNode;return null!==o&&"function"===typeof o.componentDidCatch&&(n.callback=function(){fs(0,t),"function"!==typeof r&&(null===Kl?Kl=new Set([this]):Kl.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function ms(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new hs;var i=new Set;r.set(t,i)}else void 0===(i=r.get(t))&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=Tu.bind(null,e,t,n),t.then(e,e))}function bs(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function gs(e,t,n,r,i){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Lo(-1,1)).tag=2,Do(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=i,e)}var vs=w.ReactCurrentOwner,ws=!1;function _s(e,t,n,r){t.child=null===e?Jo(t,null,n,r):Xo(t,e.child,n,r)}function Ss(e,t,n,r,i){n=n.render;var o=t.ref;return Io(t,i),r=ka(e,t,n,r,o,i),n=Ia(),null===e||ws?(io&&n&&eo(t),t.flags|=1,_s(e,t,r,i),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Hs(e,t,i))}function xs(e,t,n,r,i){if(null===e){var o=n.type;return"function"!==typeof o||Bu(o)||void 0!==o.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Du(n.type,null,r,t,t.mode,i)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=o,ks(e,t,o,r,i))}if(o=e.child,0===(e.lanes&i)){var a=o.memoizedProps;if((n=null!==(n=n.compare)?n:lr)(a,r)&&e.ref===t.ref)return Hs(e,t,i)}return t.flags|=1,(e=Lu(o,r)).ref=t.ref,e.return=t,t.child=e}function ks(e,t,n,r,i){if(null!==e){var o=e.memoizedProps;if(lr(o,r)&&e.ref===t.ref){if(ws=!1,t.pendingProps=r=o,0===(e.lanes&i))return t.lanes=e.lanes,Hs(e,t,i);0!==(131072&e.flags)&&(ws=!0)}}return Os(e,t,n,r,i)}function Is(e,t,n){var r=t.pendingProps,i=r.children,o=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ti(Ll,Bl),Bl|=n;else{if(0===(1073741824&n))return e=null!==o?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ti(Ll,Bl),Bl|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==o?o.baseLanes:n,Ti(Ll,Bl),Bl|=r}else null!==o?(r=o.baseLanes|n,t.memoizedState=null):r=n,Ti(Ll,Bl),Bl|=r;return _s(e,t,i,n),t.child}function Ts(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Os(e,t,n,r,i){var o=Mi(n)?Ei:Ai.current;return o=Ni(t,o),Io(t,i),n=ka(e,t,n,r,o,i),r=Ia(),null===e||ws?(io&&r&&eo(t),t.flags|=1,_s(e,t,n,i),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Hs(e,t,i))}function As(e,t,n,r,i){if(Mi(n)){var o=!0;Fi(t)}else o=!1;if(Io(t,i),null===t.stateNode)$s(e,t),$o(t,n,r),Yo(t,n,r,i),r=!0;else if(null===e){var a=t.stateNode,s=t.memoizedProps;a.props=s;var l=a.context,u=n.contextType;"object"===typeof u&&null!==u?u=To(u):u=Ni(t,u=Mi(n)?Ei:Ai.current);var c=n.getDerivedStateFromProps,d="function"===typeof c||"function"===typeof a.getSnapshotBeforeUpdate;d||"function"!==typeof a.UNSAFE_componentWillReceiveProps&&"function"!==typeof a.componentWillReceiveProps||(s!==r||l!==u)&&Ho(t,a,r,u),No=!1;var f=t.memoizedState;a.state=f,Ro(t,r,a,i),l=t.memoizedState,s!==r||f!==l||Ci.current||No?("function"===typeof c&&(jo(t,n,c,r),l=t.memoizedState),(s=No||Wo(t,n,s,r,f,l,u))?(d||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||("function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount()),"function"===typeof a.componentDidMount&&(t.flags|=4194308)):("function"===typeof a.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),a.props=r,a.state=l,a.context=u,r=s):("function"===typeof a.componentDidMount&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,Bo(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:bo(t.type,s),a.props=u,d=t.pendingProps,f=a.context,"object"===typeof(l=n.contextType)&&null!==l?l=To(l):l=Ni(t,l=Mi(n)?Ei:Ai.current);var h=n.getDerivedStateFromProps;(c="function"===typeof h||"function"===typeof a.getSnapshotBeforeUpdate)||"function"!==typeof a.UNSAFE_componentWillReceiveProps&&"function"!==typeof a.componentWillReceiveProps||(s!==d||f!==l)&&Ho(t,a,r,l),No=!1,f=t.memoizedState,a.state=f,Ro(t,r,a,i);var p=t.memoizedState;s!==d||f!==p||Ci.current||No?("function"===typeof h&&(jo(t,n,h,r),p=t.memoizedState),(u=No||Wo(t,n,u,r,f,p,l)||!1)?(c||"function"!==typeof a.UNSAFE_componentWillUpdate&&"function"!==typeof a.componentWillUpdate||("function"===typeof a.componentWillUpdate&&a.componentWillUpdate(r,p,l),"function"===typeof a.UNSAFE_componentWillUpdate&&a.UNSAFE_componentWillUpdate(r,p,l)),"function"===typeof a.componentDidUpdate&&(t.flags|=4),"function"===typeof a.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof a.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof a.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),a.props=r,a.state=p,a.context=l,r=u):("function"!==typeof a.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof a.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Cs(e,t,n,r,o,i)}function Cs(e,t,n,r,i,o){Ts(e,t);var a=0!==(128&t.flags);if(!r&&!a)return i&&Pi(t,n,!1),Hs(e,t,o);r=t.stateNode,vs.current=t;var s=a&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&a?(t.child=Xo(t,e.child,null,o),t.child=Xo(t,null,s,o)):_s(e,t,s,o),t.memoizedState=r.state,i&&Pi(t,n,!0),t.child}function Es(e){var t=e.stateNode;t.pendingContext?Li(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Li(0,t.context,!1),ia(e,t.containerInfo)}function Ns(e,t,n,r,i){return po(),yo(i),t.flags|=256,_s(e,t,n,r),t.child}var Ms,Bs,Ls,Ds,Fs={dehydrated:null,treeContext:null,retryLane:0};function Ps(e){return{baseLanes:e,cachePool:null,transitions:null}}function Rs(e,t,n){var r,i=t.pendingProps,a=la.current,s=!1,l=0!==(128&t.flags);if((r=l)||(r=(null===e||null!==e.memoizedState)&&0!==(2&a)),r?(s=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(a|=1),Ti(la,1&a),null===e)return uo(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(l=i.children,e=i.fallback,s?(i=t.mode,s=t.child,l={mode:"hidden",children:l},0===(1&i)&&null!==s?(s.childLanes=0,s.pendingProps=l):s=Pu(l,i,0,null),e=Fu(e,i,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=Ps(n),t.memoizedState=Fs,e):zs(t,l));if(null!==(a=e.memoizedState)&&null!==(r=a.dehydrated))return function(e,t,n,r,i,a,s){if(n)return 256&t.flags?(t.flags&=-257,Us(e,t,s,r=ds(Error(o(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(a=r.fallback,i=t.mode,r=Pu({mode:"visible",children:r.children},i,0,null),(a=Fu(a,i,s,null)).flags|=2,r.return=t,a.return=t,r.sibling=a,t.child=r,0!==(1&t.mode)&&Xo(t,e.child,null,s),t.child.memoizedState=Ps(s),t.memoizedState=Fs,a);if(0===(1&t.mode))return Us(e,t,s,null);if("$!"===i.data){if(r=i.nextSibling&&i.nextSibling.dataset)var l=r.dgst;return r=l,Us(e,t,s,r=ds(a=Error(o(419)),r,void 0))}if(l=0!==(s&e.childLanes),ws||l){if(null!==(r=El)){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}0!==(i=0!==(i&(r.suspendedLanes|s))?0:i)&&i!==a.retryLane&&(a.retryLane=i,Eo(e,i),ru(r,e,i,-1))}return mu(),Us(e,t,s,r=ds(Error(o(421))))}return"$?"===i.data?(t.flags|=128,t.child=e.child,t=Au.bind(null,e),i._reactRetry=t,null):(e=a.treeContext,ro=ui(i.nextSibling),no=t,io=!0,oo=null,null!==e&&(Ki[Qi++]=Gi,Ki[Qi++]=Xi,Ki[Qi++]=qi,Gi=e.id,Xi=e.overflow,qi=t),t=zs(t,r.children),t.flags|=4096,t)}(e,t,l,i,r,a,n);if(s){s=i.fallback,l=t.mode,r=(a=e.child).sibling;var u={mode:"hidden",children:i.children};return 0===(1&l)&&t.child!==a?((i=t.child).childLanes=0,i.pendingProps=u,t.deletions=null):(i=Lu(a,u)).subtreeFlags=14680064&a.subtreeFlags,null!==r?s=Lu(r,s):(s=Fu(s,l,n,null)).flags|=2,s.return=t,i.return=t,i.sibling=s,t.child=i,i=s,s=t.child,l=null===(l=e.child.memoizedState)?Ps(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},s.memoizedState=l,s.childLanes=e.childLanes&~n,t.memoizedState=Fs,i}return e=(s=e.child).sibling,i=Lu(s,{mode:"visible",children:i.children}),0===(1&t.mode)&&(i.lanes=n),i.return=t,i.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=i,t.memoizedState=null,i}function zs(e,t){return(t=Pu({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Us(e,t,n,r){return null!==r&&yo(r),Xo(t,e.child,null,n),(e=zs(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function js(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),ko(e.return,t,n)}function Vs(e,t,n,r,i){var o=e.memoizedState;null===o?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function Ws(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(_s(e,t,r.children,n),0!==(2&(r=la.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&js(e,n,t);else if(19===e.tag)js(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Ti(la,r),0===(1&t.mode))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;null!==n;)null!==(e=n.alternate)&&null===ua(e)&&(i=n),n=n.sibling;null===(n=i)?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Vs(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;null!==i;){if(null!==(e=i.alternate)&&null===ua(e)){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Vs(t,!0,n,null,o);break;case"together":Vs(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function $s(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Hs(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Pl|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(o(153));if(null!==t.child){for(n=Lu(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Lu(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Ys(e,t){if(!io)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ks(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;null!==i;)n|=i.lanes|i.childLanes,r|=14680064&i.subtreeFlags,r|=14680064&i.flags,i.return=e,i=i.sibling;else for(i=e.child;null!==i;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Qs(e,t,n){var r=t.pendingProps;switch(to(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ks(t),null;case 1:case 17:return Mi(t.type)&&Bi(),Ks(t),null;case 3:return r=t.stateNode,oa(),Ii(Ci),Ii(Ai),da(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(fo(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==oo&&(su(oo),oo=null))),Bs(e,t),Ks(t),null;case 5:sa(t);var i=ra(na.current);if(n=t.type,null!==e&&null!=t.stateNode)Ls(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(o(166));return Ks(t),null}if(e=ra(ea.current),fo(t)){r=t.stateNode,n=t.type;var a=t.memoizedProps;switch(r[fi]=t,r[hi]=a,e=0!==(1&t.mode),n){case"dialog":zr("cancel",r),zr("close",r);break;case"iframe":case"object":case"embed":zr("load",r);break;case"video":case"audio":for(i=0;i<Dr.length;i++)zr(Dr[i],r);break;case"source":zr("error",r);break;case"img":case"image":case"link":zr("error",r),zr("load",r);break;case"details":zr("toggle",r);break;case"input":G(r,a),zr("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!a.multiple},zr("invalid",r);break;case"textarea":ie(r,a),zr("invalid",r)}for(var l in ge(n,a),i=null,a)if(a.hasOwnProperty(l)){var u=a[l];"children"===l?"string"===typeof u?r.textContent!==u&&(!0!==a.suppressHydrationWarning&&Jr(r.textContent,u,e),i=["children",u]):"number"===typeof u&&r.textContent!==""+u&&(!0!==a.suppressHydrationWarning&&Jr(r.textContent,u,e),i=["children",""+u]):s.hasOwnProperty(l)&&null!=u&&"onScroll"===l&&zr("scroll",r)}switch(n){case"input":Y(r),Z(r,a,!0);break;case"textarea":Y(r),ae(r);break;case"select":case"option":break;default:"function"===typeof a.onClick&&(r.onclick=Zr)}r=i,t.updateQueue=r,null!==r&&(t.flags|=4)}else{l=9===i.nodeType?i:i.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=se(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=l.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),"select"===n&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[fi]=t,e[hi]=r,Ms(e,t,!1,!1),t.stateNode=e;e:{switch(l=ve(n,r),n){case"dialog":zr("cancel",e),zr("close",e),i=r;break;case"iframe":case"object":case"embed":zr("load",e),i=r;break;case"video":case"audio":for(i=0;i<Dr.length;i++)zr(Dr[i],e);i=r;break;case"source":zr("error",e),i=r;break;case"img":case"image":case"link":zr("error",e),zr("load",e),i=r;break;case"details":zr("toggle",e),i=r;break;case"input":G(e,r),i=q(e,r),zr("invalid",e);break;case"option":default:i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=P({},r,{value:void 0}),zr("invalid",e);break;case"textarea":ie(e,r),i=re(e,r),zr("invalid",e)}for(a in ge(n,i),u=i)if(u.hasOwnProperty(a)){var c=u[a];"style"===a?me(e,c):"dangerouslySetInnerHTML"===a?null!=(c=c?c.__html:void 0)&&de(e,c):"children"===a?"string"===typeof c?("textarea"!==n||""!==c)&&fe(e,c):"number"===typeof c&&fe(e,""+c):"suppressContentEditableWarning"!==a&&"suppressHydrationWarning"!==a&&"autoFocus"!==a&&(s.hasOwnProperty(a)?null!=c&&"onScroll"===a&&zr("scroll",e):null!=c&&v(e,a,c,l))}switch(n){case"input":Y(e),Z(e,r,!1);break;case"textarea":Y(e),ae(e);break;case"option":null!=r.value&&e.setAttribute("value",""+$(r.value));break;case"select":e.multiple=!!r.multiple,null!=(a=r.value)?ne(e,!!r.multiple,a,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof i.onClick&&(e.onclick=Zr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Ks(t),null;case 6:if(e&&null!=t.stateNode)Ds(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(o(166));if(n=ra(na.current),ra(ea.current),fo(t)){if(r=t.stateNode,n=t.memoizedProps,r[fi]=t,(a=r.nodeValue!==n)&&null!==(e=no))switch(e.tag){case 3:Jr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Jr(r.nodeValue,n,0!==(1&e.mode))}a&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[fi]=t,t.stateNode=r}return Ks(t),null;case 13:if(Ii(la),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(io&&null!==ro&&0!==(1&t.mode)&&0===(128&t.flags))ho(),po(),t.flags|=98560,a=!1;else if(a=fo(t),null!==r&&null!==r.dehydrated){if(null===e){if(!a)throw Error(o(318));if(!(a=null!==(a=t.memoizedState)?a.dehydrated:null))throw Error(o(317));a[fi]=t}else po(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Ks(t),a=!1}else null!==oo&&(su(oo),oo=null),a=!0;if(!a)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&la.current)?0===Dl&&(Dl=3):mu())),null!==t.updateQueue&&(t.flags|=4),Ks(t),null);case 4:return oa(),Bs(e,t),null===e&&Vr(t.stateNode.containerInfo),Ks(t),null;case 10:return xo(t.type._context),Ks(t),null;case 19:if(Ii(la),null===(a=t.memoizedState))return Ks(t),null;if(r=0!==(128&t.flags),null===(l=a.rendering))if(r)Ys(a,!1);else{if(0!==Dl||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(l=ua(e))){for(t.flags|=128,Ys(a,!1),null!==(r=l.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(a=n).flags&=14680066,null===(l=a.alternate)?(a.childLanes=0,a.lanes=e,a.child=null,a.subtreeFlags=0,a.memoizedProps=null,a.memoizedState=null,a.updateQueue=null,a.dependencies=null,a.stateNode=null):(a.childLanes=l.childLanes,a.lanes=l.lanes,a.child=l.child,a.subtreeFlags=0,a.deletions=null,a.memoizedProps=l.memoizedProps,a.memoizedState=l.memoizedState,a.updateQueue=l.updateQueue,a.type=l.type,e=l.dependencies,a.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Ti(la,1&la.current|2),t.child}e=e.sibling}null!==a.tail&&Xe()>Wl&&(t.flags|=128,r=!0,Ys(a,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=ua(l))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Ys(a,!0),null===a.tail&&"hidden"===a.tailMode&&!l.alternate&&!io)return Ks(t),null}else 2*Xe()-a.renderingStartTime>Wl&&1073741824!==n&&(t.flags|=128,r=!0,Ys(a,!1),t.lanes=4194304);a.isBackwards?(l.sibling=t.child,t.child=l):(null!==(n=a.last)?n.sibling=l:t.child=l,a.last=l)}return null!==a.tail?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=Xe(),t.sibling=null,n=la.current,Ti(la,r?1&n|2:1&n),t):(Ks(t),null);case 22:case 23:return fu(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Bl)&&(Ks(t),6&t.subtreeFlags&&(t.flags|=8192)):Ks(t),null;case 24:case 25:return null}throw Error(o(156,t.tag))}function qs(e,t){switch(to(t),t.tag){case 1:return Mi(t.type)&&Bi(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return oa(),Ii(Ci),Ii(Ai),da(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return sa(t),null;case 13:if(Ii(la),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(o(340));po()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Ii(la),null;case 4:return oa(),null;case 10:return xo(t.type._context),null;case 22:case 23:return fu(),null;default:return null}}Ms=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Bs=function(){},Ls=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,ra(ea.current);var o,a=null;switch(n){case"input":i=q(e,i),r=q(e,r),a=[];break;case"select":i=P({},i,{value:void 0}),r=P({},r,{value:void 0}),a=[];break;case"textarea":i=re(e,i),r=re(e,r),a=[];break;default:"function"!==typeof i.onClick&&"function"===typeof r.onClick&&(e.onclick=Zr)}for(c in ge(n,r),n=null,i)if(!r.hasOwnProperty(c)&&i.hasOwnProperty(c)&&null!=i[c])if("style"===c){var l=i[c];for(o in l)l.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(s.hasOwnProperty(c)?a||(a=[]):(a=a||[]).push(c,null));for(c in r){var u=r[c];if(l=null!=i?i[c]:void 0,r.hasOwnProperty(c)&&u!==l&&(null!=u||null!=l))if("style"===c)if(l){for(o in l)!l.hasOwnProperty(o)||u&&u.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in u)u.hasOwnProperty(o)&&l[o]!==u[o]&&(n||(n={}),n[o]=u[o])}else n||(a||(a=[]),a.push(c,n)),n=u;else"dangerouslySetInnerHTML"===c?(u=u?u.__html:void 0,l=l?l.__html:void 0,null!=u&&l!==u&&(a=a||[]).push(c,u)):"children"===c?"string"!==typeof u&&"number"!==typeof u||(a=a||[]).push(c,""+u):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(s.hasOwnProperty(c)?(null!=u&&"onScroll"===c&&zr("scroll",e),a||l===u||(a=[])):(a=a||[]).push(c,u))}n&&(a=a||[]).push("style",n);var c=a;(t.updateQueue=c)&&(t.flags|=4)}},Ds=function(e,t,n,r){n!==r&&(t.flags|=4)};var Gs=!1,Xs=!1,Js="function"===typeof WeakSet?WeakSet:Set,Zs=null;function el(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){Iu(e,t,r)}else n.current=null}function tl(e,t,n){try{n()}catch(r){Iu(e,t,r)}}var nl=!1;function rl(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,void 0!==o&&tl(t,n,o)}i=i.next}while(i!==r)}}function il(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ol(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function al(e){var t=e.alternate;null!==t&&(e.alternate=null,al(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[fi],delete t[hi],delete t[yi],delete t[mi],delete t[bi])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function sl(e){return 5===e.tag||3===e.tag||4===e.tag}function ll(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||sl(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ul(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Zr));else if(4!==r&&null!==(e=e.child))for(ul(e,t,n),e=e.sibling;null!==e;)ul(e,t,n),e=e.sibling}function cl(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(cl(e,t,n),e=e.sibling;null!==e;)cl(e,t,n),e=e.sibling}var dl=null,fl=!1;function hl(e,t,n){for(n=n.child;null!==n;)pl(e,t,n),n=n.sibling}function pl(e,t,n){if(ot&&"function"===typeof ot.onCommitFiberUnmount)try{ot.onCommitFiberUnmount(it,n)}catch(s){}switch(n.tag){case 5:Xs||el(n,t);case 6:var r=dl,i=fl;dl=null,hl(e,t,n),fl=i,null!==(dl=r)&&(fl?(e=dl,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):dl.removeChild(n.stateNode));break;case 18:null!==dl&&(fl?(e=dl,n=n.stateNode,8===e.nodeType?li(e.parentNode,n):1===e.nodeType&&li(e,n),Vt(e)):li(dl,n.stateNode));break;case 4:r=dl,i=fl,dl=n.stateNode.containerInfo,fl=!0,hl(e,t,n),dl=r,fl=i;break;case 0:case 11:case 14:case 15:if(!Xs&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){i=r=r.next;do{var o=i,a=o.destroy;o=o.tag,void 0!==a&&(0!==(2&o)||0!==(4&o))&&tl(n,t,a),i=i.next}while(i!==r)}hl(e,t,n);break;case 1:if(!Xs&&(el(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){Iu(n,t,s)}hl(e,t,n);break;case 21:hl(e,t,n);break;case 22:1&n.mode?(Xs=(r=Xs)||null!==n.memoizedState,hl(e,t,n),Xs=r):hl(e,t,n);break;default:hl(e,t,n)}}function yl(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Js),t.forEach((function(t){var r=Cu.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function ml(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var i=n[r];try{var a=e,s=t,l=s;e:for(;null!==l;){switch(l.tag){case 5:dl=l.stateNode,fl=!1;break e;case 3:case 4:dl=l.stateNode.containerInfo,fl=!0;break e}l=l.return}if(null===dl)throw Error(o(160));pl(a,s,i),dl=null,fl=!1;var u=i.alternate;null!==u&&(u.return=null),i.return=null}catch(c){Iu(i,t,c)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)bl(t,e),t=t.sibling}function bl(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ml(t,e),gl(e),4&r){try{rl(3,e,e.return),il(3,e)}catch(m){Iu(e,e.return,m)}try{rl(5,e,e.return)}catch(m){Iu(e,e.return,m)}}break;case 1:ml(t,e),gl(e),512&r&&null!==n&&el(n,n.return);break;case 5:if(ml(t,e),gl(e),512&r&&null!==n&&el(n,n.return),32&e.flags){var i=e.stateNode;try{fe(i,"")}catch(m){Iu(e,e.return,m)}}if(4&r&&null!=(i=e.stateNode)){var a=e.memoizedProps,s=null!==n?n.memoizedProps:a,l=e.type,u=e.updateQueue;if(e.updateQueue=null,null!==u)try{"input"===l&&"radio"===a.type&&null!=a.name&&X(i,a),ve(l,s);var c=ve(l,a);for(s=0;s<u.length;s+=2){var d=u[s],f=u[s+1];"style"===d?me(i,f):"dangerouslySetInnerHTML"===d?de(i,f):"children"===d?fe(i,f):v(i,d,f,c)}switch(l){case"input":J(i,a);break;case"textarea":oe(i,a);break;case"select":var h=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!a.multiple;var p=a.value;null!=p?ne(i,!!a.multiple,p,!1):h!==!!a.multiple&&(null!=a.defaultValue?ne(i,!!a.multiple,a.defaultValue,!0):ne(i,!!a.multiple,a.multiple?[]:"",!1))}i[hi]=a}catch(m){Iu(e,e.return,m)}}break;case 6:if(ml(t,e),gl(e),4&r){if(null===e.stateNode)throw Error(o(162));i=e.stateNode,a=e.memoizedProps;try{i.nodeValue=a}catch(m){Iu(e,e.return,m)}}break;case 3:if(ml(t,e),gl(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Vt(t.containerInfo)}catch(m){Iu(e,e.return,m)}break;case 4:default:ml(t,e),gl(e);break;case 13:ml(t,e),gl(e),8192&(i=e.child).flags&&(a=null!==i.memoizedState,i.stateNode.isHidden=a,!a||null!==i.alternate&&null!==i.alternate.memoizedState||(Vl=Xe())),4&r&&yl(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Xs=(c=Xs)||d,ml(t,e),Xs=c):ml(t,e),gl(e),8192&r){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!d&&0!==(1&e.mode))for(Zs=e,d=e.child;null!==d;){for(f=Zs=d;null!==Zs;){switch(p=(h=Zs).child,h.tag){case 0:case 11:case 14:case 15:rl(4,h,h.return);break;case 1:el(h,h.return);var y=h.stateNode;if("function"===typeof y.componentWillUnmount){r=h,n=h.return;try{t=r,y.props=t.memoizedProps,y.state=t.memoizedState,y.componentWillUnmount()}catch(m){Iu(r,n,m)}}break;case 5:el(h,h.return);break;case 22:if(null!==h.memoizedState){Sl(f);continue}}null!==p?(p.return=h,Zs=p):Sl(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{i=f.stateNode,c?"function"===typeof(a=i.style).setProperty?a.setProperty("display","none","important"):a.display="none":(l=f.stateNode,s=void 0!==(u=f.memoizedProps.style)&&null!==u&&u.hasOwnProperty("display")?u.display:null,l.style.display=ye("display",s))}catch(m){Iu(e,e.return,m)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(m){Iu(e,e.return,m)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:ml(t,e),gl(e),4&r&&yl(e);case 21:}}function gl(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(sl(n)){var r=n;break e}n=n.return}throw Error(o(160))}switch(r.tag){case 5:var i=r.stateNode;32&r.flags&&(fe(i,""),r.flags&=-33),cl(e,ll(e),i);break;case 3:case 4:var a=r.stateNode.containerInfo;ul(e,ll(e),a);break;default:throw Error(o(161))}}catch(s){Iu(e,e.return,s)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function vl(e,t,n){Zs=e,wl(e,t,n)}function wl(e,t,n){for(var r=0!==(1&e.mode);null!==Zs;){var i=Zs,o=i.child;if(22===i.tag&&r){var a=null!==i.memoizedState||Gs;if(!a){var s=i.alternate,l=null!==s&&null!==s.memoizedState||Xs;s=Gs;var u=Xs;if(Gs=a,(Xs=l)&&!u)for(Zs=i;null!==Zs;)l=(a=Zs).child,22===a.tag&&null!==a.memoizedState?xl(i):null!==l?(l.return=a,Zs=l):xl(i);for(;null!==o;)Zs=o,wl(o,t,n),o=o.sibling;Zs=i,Gs=s,Xs=u}_l(e)}else 0!==(8772&i.subtreeFlags)&&null!==o?(o.return=i,Zs=o):_l(e)}}function _l(e){for(;null!==Zs;){var t=Zs;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Xs||il(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Xs)if(null===n)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:bo(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var a=t.updateQueue;null!==a&&zo(t,a,r);break;case 3:var s=t.updateQueue;if(null!==s){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}zo(t,s,n)}break;case 5:var l=t.stateNode;if(null===n&&4&t.flags){n=l;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var d=c.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Vt(f)}}}break;default:throw Error(o(163))}Xs||512&t.flags&&ol(t)}catch(h){Iu(t,t.return,h)}}if(t===e){Zs=null;break}if(null!==(n=t.sibling)){n.return=t.return,Zs=n;break}Zs=t.return}}function Sl(e){for(;null!==Zs;){var t=Zs;if(t===e){Zs=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Zs=n;break}Zs=t.return}}function xl(e){for(;null!==Zs;){var t=Zs;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{il(4,t)}catch(l){Iu(t,n,l)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var i=t.return;try{r.componentDidMount()}catch(l){Iu(t,i,l)}}var o=t.return;try{ol(t)}catch(l){Iu(t,o,l)}break;case 5:var a=t.return;try{ol(t)}catch(l){Iu(t,a,l)}}}catch(l){Iu(t,t.return,l)}if(t===e){Zs=null;break}var s=t.sibling;if(null!==s){s.return=t.return,Zs=s;break}Zs=t.return}}var kl,Il=Math.ceil,Tl=w.ReactCurrentDispatcher,Ol=w.ReactCurrentOwner,Al=w.ReactCurrentBatchConfig,Cl=0,El=null,Nl=null,Ml=0,Bl=0,Ll=ki(0),Dl=0,Fl=null,Pl=0,Rl=0,zl=0,Ul=null,jl=null,Vl=0,Wl=1/0,$l=null,Hl=!1,Yl=null,Kl=null,Ql=!1,ql=null,Gl=0,Xl=0,Jl=null,Zl=-1,eu=0;function tu(){return 0!==(6&Cl)?Xe():-1!==Zl?Zl:Zl=Xe()}function nu(e){return 0===(1&e.mode)?1:0!==(2&Cl)&&0!==Ml?Ml&-Ml:null!==mo.transition?(0===eu&&(eu=yt()),eu):0!==(e=vt)?e:e=void 0===(e=window.event)?16:Gt(e.type)}function ru(e,t,n,r){if(50<Xl)throw Xl=0,Jl=null,Error(o(185));bt(e,n,r),0!==(2&Cl)&&e===El||(e===El&&(0===(2&Cl)&&(Rl|=n),4===Dl&&lu(e,Ml)),iu(e,r),1===n&&0===Cl&&0===(1&t.mode)&&(Wl=Xe()+500,zi&&Vi()))}function iu(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var a=31-at(o),s=1<<a,l=i[a];-1===l?0!==(s&n)&&0===(s&r)||(i[a]=ht(s,t)):l<=t&&(e.expiredLanes|=s),o&=~s}}(e,t);var r=ft(e,e===El?Ml:0);if(0===r)null!==n&&Qe(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Qe(n),1===t)0===e.tag?function(e){zi=!0,ji(e)}(uu.bind(null,e)):ji(uu.bind(null,e)),ai((function(){0===(6&Cl)&&Vi()})),n=null;else{switch(wt(r)){case 1:n=Ze;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Eu(n,ou.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ou(e,t){if(Zl=-1,eu=0,0!==(6&Cl))throw Error(o(327));var n=e.callbackNode;if(xu()&&e.callbackNode!==n)return null;var r=ft(e,e===El?Ml:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=bu(e,r);else{t=r;var i=Cl;Cl|=2;var a=yu();for(El===e&&Ml===t||($l=null,Wl=Xe()+500,hu(e,t));;)try{vu();break}catch(l){pu(e,l)}So(),Tl.current=a,Cl=i,null!==Nl?t=0:(El=null,Ml=0,t=Dl)}if(0!==t){if(2===t&&(0!==(i=pt(e))&&(r=i,t=au(e,i))),1===t)throw n=Fl,hu(e,0),lu(e,r),iu(e,Xe()),n;if(6===t)lu(e,r);else{if(i=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!sr(o(),i))return!1}catch(s){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(i)&&(2===(t=bu(e,r))&&(0!==(a=pt(e))&&(r=a,t=au(e,a))),1===t))throw n=Fl,hu(e,0),lu(e,r),iu(e,Xe()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(o(345));case 2:case 5:Su(e,jl,$l);break;case 3:if(lu(e,r),(130023424&r)===r&&10<(t=Vl+500-Xe())){if(0!==ft(e,0))break;if(((i=e.suspendedLanes)&r)!==r){tu(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=ri(Su.bind(null,e,jl,$l),t);break}Su(e,jl,$l);break;case 4:if(lu(e,r),(4194240&r)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-at(r);a=1<<s,(s=t[s])>i&&(i=s),r&=~a}if(r=i,10<(r=(120>(r=Xe()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Il(r/1960))-r)){e.timeoutHandle=ri(Su.bind(null,e,jl,$l),r);break}Su(e,jl,$l);break;default:throw Error(o(329))}}}return iu(e,Xe()),e.callbackNode===n?ou.bind(null,e):null}function au(e,t){var n=Ul;return e.current.memoizedState.isDehydrated&&(hu(e,t).flags|=256),2!==(e=bu(e,t))&&(t=jl,jl=n,null!==t&&su(t)),e}function su(e){null===jl?jl=e:jl.push.apply(jl,e)}function lu(e,t){for(t&=~zl,t&=~Rl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-at(t),r=1<<n;e[n]=-1,t&=~r}}function uu(e){if(0!==(6&Cl))throw Error(o(327));xu();var t=ft(e,0);if(0===(1&t))return iu(e,Xe()),null;var n=bu(e,t);if(0!==e.tag&&2===n){var r=pt(e);0!==r&&(t=r,n=au(e,r))}if(1===n)throw n=Fl,hu(e,0),lu(e,t),iu(e,Xe()),n;if(6===n)throw Error(o(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Su(e,jl,$l),iu(e,Xe()),null}function cu(e,t){var n=Cl;Cl|=1;try{return e(t)}finally{0===(Cl=n)&&(Wl=Xe()+500,zi&&Vi())}}function du(e){null!==ql&&0===ql.tag&&0===(6&Cl)&&xu();var t=Cl;Cl|=1;var n=Al.transition,r=vt;try{if(Al.transition=null,vt=1,e)return e()}finally{vt=r,Al.transition=n,0===(6&(Cl=t))&&Vi()}}function fu(){Bl=Ll.current,Ii(Ll)}function hu(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,ii(n)),null!==Nl)for(n=Nl.return;null!==n;){var r=n;switch(to(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&Bi();break;case 3:oa(),Ii(Ci),Ii(Ai),da();break;case 5:sa(r);break;case 4:oa();break;case 13:case 19:Ii(la);break;case 10:xo(r.type._context);break;case 22:case 23:fu()}n=n.return}if(El=e,Nl=e=Lu(e.current,null),Ml=Bl=t,Dl=0,Fl=null,zl=Rl=Pl=0,jl=Ul=null,null!==Oo){for(t=0;t<Oo.length;t++)if(null!==(r=(n=Oo[t]).interleaved)){n.interleaved=null;var i=r.next,o=n.pending;if(null!==o){var a=o.next;o.next=i,r.next=a}n.pending=r}Oo=null}return e}function pu(e,t){for(;;){var n=Nl;try{if(So(),fa.current=as,ga){for(var r=ya.memoizedState;null!==r;){var i=r.queue;null!==i&&(i.pending=null),r=r.next}ga=!1}if(pa=0,ba=ma=ya=null,va=!1,wa=0,Ol.current=null,null===n||null===n.return){Dl=1,Fl=t,Nl=null;break}e:{var a=e,s=n.return,l=n,u=t;if(t=Ml,l.flags|=32768,null!==u&&"object"===typeof u&&"function"===typeof u.then){var c=u,d=l,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var h=d.alternate;h?(d.updateQueue=h.updateQueue,d.memoizedState=h.memoizedState,d.lanes=h.lanes):(d.updateQueue=null,d.memoizedState=null)}var p=bs(s);if(null!==p){p.flags&=-257,gs(p,s,l,0,t),1&p.mode&&ms(a,c,t),u=c;var y=(t=p).updateQueue;if(null===y){var m=new Set;m.add(u),t.updateQueue=m}else y.add(u);break e}if(0===(1&t)){ms(a,c,t),mu();break e}u=Error(o(426))}else if(io&&1&l.mode){var b=bs(s);if(null!==b){0===(65536&b.flags)&&(b.flags|=256),gs(b,s,l,0,t),yo(cs(u,l));break e}}a=u=cs(u,l),4!==Dl&&(Dl=2),null===Ul?Ul=[a]:Ul.push(a),a=s;do{switch(a.tag){case 3:a.flags|=65536,t&=-t,a.lanes|=t,Po(a,ps(0,u,t));break e;case 1:l=u;var g=a.type,v=a.stateNode;if(0===(128&a.flags)&&("function"===typeof g.getDerivedStateFromError||null!==v&&"function"===typeof v.componentDidCatch&&(null===Kl||!Kl.has(v)))){a.flags|=65536,t&=-t,a.lanes|=t,Po(a,ys(a,l,t));break e}}a=a.return}while(null!==a)}_u(n)}catch(w){t=w,Nl===n&&null!==n&&(Nl=n=n.return);continue}break}}function yu(){var e=Tl.current;return Tl.current=as,null===e?as:e}function mu(){0!==Dl&&3!==Dl&&2!==Dl||(Dl=4),null===El||0===(268435455&Pl)&&0===(268435455&Rl)||lu(El,Ml)}function bu(e,t){var n=Cl;Cl|=2;var r=yu();for(El===e&&Ml===t||($l=null,hu(e,t));;)try{gu();break}catch(i){pu(e,i)}if(So(),Cl=n,Tl.current=r,null!==Nl)throw Error(o(261));return El=null,Ml=0,Dl}function gu(){for(;null!==Nl;)wu(Nl)}function vu(){for(;null!==Nl&&!qe();)wu(Nl)}function wu(e){var t=kl(e.alternate,e,Bl);e.memoizedProps=e.pendingProps,null===t?_u(e):Nl=t,Ol.current=null}function _u(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=Qs(n,t,Bl)))return void(Nl=n)}else{if(null!==(n=qs(n,t)))return n.flags&=32767,void(Nl=n);if(null===e)return Dl=6,void(Nl=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Nl=t);Nl=t=e}while(null!==t);0===Dl&&(Dl=5)}function Su(e,t,n){var r=vt,i=Al.transition;try{Al.transition=null,vt=1,function(e,t,n,r){do{xu()}while(null!==ql);if(0!==(6&Cl))throw Error(o(327));n=e.finishedWork;var i=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(o(177));e.callbackNode=null,e.callbackPriority=0;var a=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-at(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}(e,a),e===El&&(Nl=El=null,Ml=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||Ql||(Ql=!0,Eu(tt,(function(){return xu(),null}))),a=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||a){a=Al.transition,Al.transition=null;var s=vt;vt=1;var l=Cl;Cl|=4,Ol.current=null,function(e,t){if(ei=$t,hr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var i=r.anchorOffset,a=r.focusNode;r=r.focusOffset;try{n.nodeType,a.nodeType}catch(_){n=null;break e}var s=0,l=-1,u=-1,c=0,d=0,f=e,h=null;t:for(;;){for(var p;f!==n||0!==i&&3!==f.nodeType||(l=s+i),f!==a||0!==r&&3!==f.nodeType||(u=s+r),3===f.nodeType&&(s+=f.nodeValue.length),null!==(p=f.firstChild);)h=f,f=p;for(;;){if(f===e)break t;if(h===n&&++c===i&&(l=s),h===a&&++d===r&&(u=s),null!==(p=f.nextSibling))break;h=(f=h).parentNode}f=p}n=-1===l||-1===u?null:{start:l,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(ti={focusedElem:e,selectionRange:n},$t=!1,Zs=t;null!==Zs;)if(e=(t=Zs).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Zs=e;else for(;null!==Zs;){t=Zs;try{var y=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==y){var m=y.memoizedProps,b=y.memoizedState,g=t.stateNode,v=g.getSnapshotBeforeUpdate(t.elementType===t.type?m:bo(t.type,m),b);g.__reactInternalSnapshotBeforeUpdate=v}break;case 3:var w=t.stateNode.containerInfo;1===w.nodeType?w.textContent="":9===w.nodeType&&w.documentElement&&w.removeChild(w.documentElement);break;default:throw Error(o(163))}}catch(_){Iu(t,t.return,_)}if(null!==(e=t.sibling)){e.return=t.return,Zs=e;break}Zs=t.return}y=nl,nl=!1}(e,n),bl(n,e),pr(ti),$t=!!ei,ti=ei=null,e.current=n,vl(n,e,i),Ge(),Cl=l,vt=s,Al.transition=a}else e.current=n;if(Ql&&(Ql=!1,ql=e,Gl=i),a=e.pendingLanes,0===a&&(Kl=null),function(e){if(ot&&"function"===typeof ot.onCommitFiberRoot)try{ot.onCommitFiberRoot(it,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),iu(e,Xe()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(Hl)throw Hl=!1,e=Yl,Yl=null,e;0!==(1&Gl)&&0!==e.tag&&xu(),a=e.pendingLanes,0!==(1&a)?e===Jl?Xl++:(Xl=0,Jl=e):Xl=0,Vi()}(e,t,n,r)}finally{Al.transition=i,vt=r}return null}function xu(){if(null!==ql){var e=wt(Gl),t=Al.transition,n=vt;try{if(Al.transition=null,vt=16>e?16:e,null===ql)var r=!1;else{if(e=ql,ql=null,Gl=0,0!==(6&Cl))throw Error(o(331));var i=Cl;for(Cl|=4,Zs=e.current;null!==Zs;){var a=Zs,s=a.child;if(0!==(16&Zs.flags)){var l=a.deletions;if(null!==l){for(var u=0;u<l.length;u++){var c=l[u];for(Zs=c;null!==Zs;){var d=Zs;switch(d.tag){case 0:case 11:case 15:rl(8,d,a)}var f=d.child;if(null!==f)f.return=d,Zs=f;else for(;null!==Zs;){var h=(d=Zs).sibling,p=d.return;if(al(d),d===c){Zs=null;break}if(null!==h){h.return=p,Zs=h;break}Zs=p}}}var y=a.alternate;if(null!==y){var m=y.child;if(null!==m){y.child=null;do{var b=m.sibling;m.sibling=null,m=b}while(null!==m)}}Zs=a}}if(0!==(2064&a.subtreeFlags)&&null!==s)s.return=a,Zs=s;else e:for(;null!==Zs;){if(0!==(2048&(a=Zs).flags))switch(a.tag){case 0:case 11:case 15:rl(9,a,a.return)}var g=a.sibling;if(null!==g){g.return=a.return,Zs=g;break e}Zs=a.return}}var v=e.current;for(Zs=v;null!==Zs;){var w=(s=Zs).child;if(0!==(2064&s.subtreeFlags)&&null!==w)w.return=s,Zs=w;else e:for(s=v;null!==Zs;){if(0!==(2048&(l=Zs).flags))try{switch(l.tag){case 0:case 11:case 15:il(9,l)}}catch(S){Iu(l,l.return,S)}if(l===s){Zs=null;break e}var _=l.sibling;if(null!==_){_.return=l.return,Zs=_;break e}Zs=l.return}}if(Cl=i,Vi(),ot&&"function"===typeof ot.onPostCommitFiberRoot)try{ot.onPostCommitFiberRoot(it,e)}catch(S){}r=!0}return r}finally{vt=n,Al.transition=t}}return!1}function ku(e,t,n){e=Do(e,t=ps(0,t=cs(n,t),1),1),t=tu(),null!==e&&(bt(e,1,t),iu(e,t))}function Iu(e,t,n){if(3===e.tag)ku(e,e,n);else for(;null!==t;){if(3===t.tag){ku(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Kl||!Kl.has(r))){t=Do(t,e=ys(t,e=cs(n,e),1),1),e=tu(),null!==t&&(bt(t,1,e),iu(t,e));break}}t=t.return}}function Tu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=tu(),e.pingedLanes|=e.suspendedLanes&n,El===e&&(Ml&n)===n&&(4===Dl||3===Dl&&(130023424&Ml)===Ml&&500>Xe()-Vl?hu(e,0):zl|=n),iu(e,t)}function Ou(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ct,0===(130023424&(ct<<=1))&&(ct=4194304)));var n=tu();null!==(e=Eo(e,t))&&(bt(e,t,n),iu(e,n))}function Au(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Ou(e,n)}function Cu(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;null!==i&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(o(314))}null!==r&&r.delete(t),Ou(e,n)}function Eu(e,t){return Ke(e,t)}function Nu(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Mu(e,t,n,r){return new Nu(e,t,n,r)}function Bu(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Lu(e,t){var n=e.alternate;return null===n?((n=Mu(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Du(e,t,n,r,i,a){var s=2;if(r=e,"function"===typeof e)Bu(e)&&(s=1);else if("string"===typeof e)s=5;else e:switch(e){case x:return Fu(n.children,i,a,t);case k:s=8,i|=8;break;case I:return(e=Mu(12,n,t,2|i)).elementType=I,e.lanes=a,e;case C:return(e=Mu(13,n,t,i)).elementType=C,e.lanes=a,e;case E:return(e=Mu(19,n,t,i)).elementType=E,e.lanes=a,e;case B:return Pu(n,i,a,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case T:s=10;break e;case O:s=9;break e;case A:s=11;break e;case N:s=14;break e;case M:s=16,r=null;break e}throw Error(o(130,null==e?e:typeof e,""))}return(t=Mu(s,n,t,i)).elementType=e,t.type=r,t.lanes=a,t}function Fu(e,t,n,r){return(e=Mu(7,e,r,t)).lanes=n,e}function Pu(e,t,n,r){return(e=Mu(22,e,r,t)).elementType=B,e.lanes=n,e.stateNode={isHidden:!1},e}function Ru(e,t,n){return(e=Mu(6,e,null,t)).lanes=n,e}function zu(e,t,n){return(t=Mu(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Uu(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=mt(0),this.expirationTimes=mt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=mt(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function ju(e,t,n,r,i,o,a,s,l){return e=new Uu(e,t,n,s,l),1===t?(t=1,!0===o&&(t|=8)):t=0,o=Mu(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Mo(o),e}function Vu(e){if(!e)return Oi;e:{if(Ve(e=e._reactInternals)!==e||1!==e.tag)throw Error(o(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Mi(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(o(171))}if(1===e.tag){var n=e.type;if(Mi(n))return Di(e,n,t)}return t}function Wu(e,t,n,r,i,o,a,s,l){return(e=ju(n,r,!0,e,0,o,0,s,l)).context=Vu(null),n=e.current,(o=Lo(r=tu(),i=nu(n))).callback=void 0!==t&&null!==t?t:null,Do(n,o,i),e.current.lanes=i,bt(e,i,r),iu(e,r),e}function $u(e,t,n,r){var i=t.current,o=tu(),a=nu(i);return n=Vu(n),null===t.context?t.context=n:t.pendingContext=n,(t=Lo(o,a)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Do(i,t,a))&&(ru(e,i,a,o),Fo(e,i,a)),a}function Hu(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Yu(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Ku(e,t){Yu(e,t),(e=e.alternate)&&Yu(e,t)}kl=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Ci.current)ws=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return ws=!1,function(e,t,n){switch(t.tag){case 3:Es(t),po();break;case 5:aa(t);break;case 1:Mi(t.type)&&Fi(t);break;case 4:ia(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;Ti(go,r._currentValue),r._currentValue=i;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Ti(la,1&la.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Rs(e,t,n):(Ti(la,1&la.current),null!==(e=Hs(e,t,n))?e.sibling:null);Ti(la,1&la.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Ws(e,t,n);t.flags|=128}if(null!==(i=t.memoizedState)&&(i.rendering=null,i.tail=null,i.lastEffect=null),Ti(la,la.current),r)break;return null;case 22:case 23:return t.lanes=0,Is(e,t,n)}return Hs(e,t,n)}(e,t,n);ws=0!==(131072&e.flags)}else ws=!1,io&&0!==(1048576&t.flags)&&Zi(t,Yi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;$s(e,t),e=t.pendingProps;var i=Ni(t,Ai.current);Io(t,n),i=ka(null,t,r,e,i,n);var a=Ia();return t.flags|=1,"object"===typeof i&&null!==i&&"function"===typeof i.render&&void 0===i.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Mi(r)?(a=!0,Fi(t)):a=!1,t.memoizedState=null!==i.state&&void 0!==i.state?i.state:null,Mo(t),i.updater=Vo,t.stateNode=i,i._reactInternals=t,Yo(t,r,e,n),t=Cs(null,t,r,!0,a,n)):(t.tag=0,io&&a&&eo(t),_s(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch($s(e,t),e=t.pendingProps,r=(i=r._init)(r._payload),t.type=r,i=t.tag=function(e){if("function"===typeof e)return Bu(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===A)return 11;if(e===N)return 14}return 2}(r),e=bo(r,e),i){case 0:t=Os(null,t,r,e,n);break e;case 1:t=As(null,t,r,e,n);break e;case 11:t=Ss(null,t,r,e,n);break e;case 14:t=xs(null,t,r,bo(r.type,e),n);break e}throw Error(o(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,Os(e,t,r,i=t.elementType===r?i:bo(r,i),n);case 1:return r=t.type,i=t.pendingProps,As(e,t,r,i=t.elementType===r?i:bo(r,i),n);case 3:e:{if(Es(t),null===e)throw Error(o(387));r=t.pendingProps,i=(a=t.memoizedState).element,Bo(e,t),Ro(t,r,null,n);var s=t.memoizedState;if(r=s.element,a.isDehydrated){if(a={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=a,t.memoizedState=a,256&t.flags){t=Ns(e,t,r,n,i=cs(Error(o(423)),t));break e}if(r!==i){t=Ns(e,t,r,n,i=cs(Error(o(424)),t));break e}for(ro=ui(t.stateNode.containerInfo.firstChild),no=t,io=!0,oo=null,n=Jo(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(po(),r===i){t=Hs(e,t,n);break e}_s(e,t,r,n)}t=t.child}return t;case 5:return aa(t),null===e&&uo(t),r=t.type,i=t.pendingProps,a=null!==e?e.memoizedProps:null,s=i.children,ni(r,i)?s=null:null!==a&&ni(r,a)&&(t.flags|=32),Ts(e,t),_s(e,t,s,n),t.child;case 6:return null===e&&uo(t),null;case 13:return Rs(e,t,n);case 4:return ia(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=Xo(t,null,r,n):_s(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,Ss(e,t,r,i=t.elementType===r?i:bo(r,i),n);case 7:return _s(e,t,t.pendingProps,n),t.child;case 8:case 12:return _s(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,a=t.memoizedProps,s=i.value,Ti(go,r._currentValue),r._currentValue=s,null!==a)if(sr(a.value,s)){if(a.children===i.children&&!Ci.current){t=Hs(e,t,n);break e}}else for(null!==(a=t.child)&&(a.return=t);null!==a;){var l=a.dependencies;if(null!==l){s=a.child;for(var u=l.firstContext;null!==u;){if(u.context===r){if(1===a.tag){(u=Lo(-1,n&-n)).tag=2;var c=a.updateQueue;if(null!==c){var d=(c=c.shared).pending;null===d?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}a.lanes|=n,null!==(u=a.alternate)&&(u.lanes|=n),ko(a.return,n,t),l.lanes|=n;break}u=u.next}}else if(10===a.tag)s=a.type===t.type?null:a.child;else if(18===a.tag){if(null===(s=a.return))throw Error(o(341));s.lanes|=n,null!==(l=s.alternate)&&(l.lanes|=n),ko(s,n,t),s=a.sibling}else s=a.child;if(null!==s)s.return=a;else for(s=a;null!==s;){if(s===t){s=null;break}if(null!==(a=s.sibling)){a.return=s.return,s=a;break}s=s.return}a=s}_s(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Io(t,n),r=r(i=To(i)),t.flags|=1,_s(e,t,r,n),t.child;case 14:return i=bo(r=t.type,t.pendingProps),xs(e,t,r,i=bo(r.type,i),n);case 15:return ks(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:bo(r,i),$s(e,t),t.tag=1,Mi(r)?(e=!0,Fi(t)):e=!1,Io(t,n),$o(t,r,i),Yo(t,r,i,n),Cs(null,t,r,!0,e,n);case 19:return Ws(e,t,n);case 22:return Is(e,t,n)}throw Error(o(156,t.tag))};var Qu="function"===typeof reportError?reportError:function(e){console.error(e)};function qu(e){this._internalRoot=e}function Gu(e){this._internalRoot=e}function Xu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Ju(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Zu(){}function ec(e,t,n,r,i){var o=n._reactRootContainer;if(o){var a=o;if("function"===typeof i){var s=i;i=function(){var e=Hu(a);s.call(e)}}$u(t,a,e,i)}else a=function(e,t,n,r,i){if(i){if("function"===typeof r){var o=r;r=function(){var e=Hu(a);o.call(e)}}var a=Wu(t,r,e,0,null,!1,0,"",Zu);return e._reactRootContainer=a,e[pi]=a.current,Vr(8===e.nodeType?e.parentNode:e),du(),a}for(;i=e.lastChild;)e.removeChild(i);if("function"===typeof r){var s=r;r=function(){var e=Hu(l);s.call(e)}}var l=ju(e,0,!1,null,0,!1,0,"",Zu);return e._reactRootContainer=l,e[pi]=l.current,Vr(8===e.nodeType?e.parentNode:e),du((function(){$u(t,l,n,r)})),l}(n,t,e,i,r);return Hu(a)}Gu.prototype.render=qu.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(o(409));$u(e,t,null,null)},Gu.prototype.unmount=qu.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;du((function(){$u(null,e,null,null)})),t[pi]=null}},Gu.prototype.unstable_scheduleHydration=function(e){if(e){var t=kt();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Bt.length&&0!==t&&t<Bt[n].priority;n++);Bt.splice(n,0,e),0===n&&Pt(e)}},_t=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(gt(t,1|n),iu(t,Xe()),0===(6&Cl)&&(Wl=Xe()+500,Vi()))}break;case 13:du((function(){var t=Eo(e,1);if(null!==t){var n=tu();ru(t,e,1,n)}})),Ku(e,1)}},St=function(e){if(13===e.tag){var t=Eo(e,134217728);if(null!==t)ru(t,e,134217728,tu());Ku(e,134217728)}},xt=function(e){if(13===e.tag){var t=nu(e),n=Eo(e,t);if(null!==n)ru(n,e,t,tu());Ku(e,t)}},kt=function(){return vt},It=function(e,t){var n=vt;try{return vt=e,t()}finally{vt=n}},Se=function(e,t,n){switch(t){case"input":if(J(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=_i(r);if(!i)throw Error(o(90));K(r),J(r,i)}}}break;case"textarea":oe(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Ae=cu,Ce=du;var tc={usingClientEntryPoint:!1,Events:[vi,wi,_i,Te,Oe,cu]},nc={findFiberByHostInstance:gi,bundleType:0,version:"18.2.0",rendererPackageName:"react-dom"},rc={bundleType:nc.bundleType,version:nc.version,rendererPackageName:nc.rendererPackageName,rendererConfig:nc.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:w.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=He(e))?null:e.stateNode},findFiberByHostInstance:nc.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.2.0-next-9e3b772b8-20220608"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var ic=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ic.isDisabled&&ic.supportsFiber)try{it=ic.inject(rc),ot=ic}catch(ce){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=tc,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Xu(t))throw Error(o(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:S,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Xu(e))throw Error(o(299));var n=!1,r="",i=Qu;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(i=t.onRecoverableError)),t=ju(e,1,!1,null,0,n,0,r,i),e[pi]=t.current,Vr(8===e.nodeType?e.parentNode:e),new qu(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(o(188));throw e=Object.keys(e).join(","),Error(o(268,e))}return e=null===(e=He(t))?null:e.stateNode},t.flushSync=function(e){return du(e)},t.hydrate=function(e,t,n){if(!Ju(t))throw Error(o(200));return ec(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Xu(e))throw Error(o(405));var r=null!=n&&n.hydratedSources||null,i=!1,a="",s=Qu;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(i=!0),void 0!==n.identifierPrefix&&(a=n.identifierPrefix),void 0!==n.onRecoverableError&&(s=n.onRecoverableError)),t=Wu(t,null,e,1,null!=n?n:null,i,0,a,s),e[pi]=t.current,Vr(e),r)for(e=0;e<r.length;e++)i=(i=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Gu(t)},t.render=function(e,t,n){if(!Ju(t))throw Error(o(200));return ec(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Ju(e))throw Error(o(40));return!!e._reactRootContainer&&(du((function(){ec(null,null,e,!1,(function(){e._reactRootContainer=null,e[pi]=null}))})),!0)},t.unstable_batchedUpdates=cu,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ju(n))throw Error(o(200));if(null==e||void 0===e._reactInternals)throw Error(o(38));return ec(e,t,n,!1,r)},t.version="18.2.0-next-9e3b772b8-20220608"},164:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(463)},372:(e,t)=>{var n="function"===typeof Symbol&&Symbol.for,r=n?Symbol.for("react.element"):60103,i=n?Symbol.for("react.portal"):60106,o=n?Symbol.for("react.fragment"):60107,a=n?Symbol.for("react.strict_mode"):60108,s=n?Symbol.for("react.profiler"):60114,l=n?Symbol.for("react.provider"):60109,u=n?Symbol.for("react.context"):60110,c=n?Symbol.for("react.async_mode"):60111,d=n?Symbol.for("react.concurrent_mode"):60111,f=n?Symbol.for("react.forward_ref"):60112,h=n?Symbol.for("react.suspense"):60113,p=n?Symbol.for("react.suspense_list"):60120,y=n?Symbol.for("react.memo"):60115,m=n?Symbol.for("react.lazy"):60116,b=n?Symbol.for("react.block"):60121,g=n?Symbol.for("react.fundamental"):60117,v=n?Symbol.for("react.responder"):60118,w=n?Symbol.for("react.scope"):60119;function _(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case c:case d:case o:case s:case a:case h:return e;default:switch(e=e&&e.$$typeof){case u:case f:case m:case y:case l:return e;default:return t}}case i:return t}}}function S(e){return _(e)===d}t.AsyncMode=c,t.ConcurrentMode=d,t.ContextConsumer=u,t.ContextProvider=l,t.Element=r,t.ForwardRef=f,t.Fragment=o,t.Lazy=m,t.Memo=y,t.Portal=i,t.Profiler=s,t.StrictMode=a,t.Suspense=h,t.isAsyncMode=function(e){return S(e)||_(e)===c},t.isConcurrentMode=S,t.isContextConsumer=function(e){return _(e)===u},t.isContextProvider=function(e){return _(e)===l},t.isElement=function(e){return"object"===typeof e&&null!==e&&e.$$typeof===r},t.isForwardRef=function(e){return _(e)===f},t.isFragment=function(e){return _(e)===o},t.isLazy=function(e){return _(e)===m},t.isMemo=function(e){return _(e)===y},t.isPortal=function(e){return _(e)===i},t.isProfiler=function(e){return _(e)===s},t.isStrictMode=function(e){return _(e)===a},t.isSuspense=function(e){return _(e)===h},t.isValidElementType=function(e){return"string"===typeof e||"function"===typeof e||e===o||e===d||e===s||e===a||e===h||e===p||"object"===typeof e&&null!==e&&(e.$$typeof===m||e.$$typeof===y||e.$$typeof===l||e.$$typeof===u||e.$$typeof===f||e.$$typeof===g||e.$$typeof===v||e.$$typeof===w||e.$$typeof===b)},t.typeOf=_},441:(e,t,n)=>{e.exports=n(372)},374:(e,t,n)=>{var r=n(791),i=Symbol.for("react.element"),o=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,s=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,n){var r,o={},u=null,c=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)a.call(t,r)&&!l.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===o[r]&&(o[r]=t[r]);return{$$typeof:i,type:e,key:u,ref:c,props:o,_owner:s.current}}t.jsx=u,t.jsxs=u},117:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),l=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),h=Symbol.iterator;var p={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},y=Object.assign,m={};function b(e,t,n){this.props=e,this.context=t,this.refs=m,this.updater=n||p}function g(){}function v(e,t,n){this.props=e,this.context=t,this.refs=m,this.updater=n||p}b.prototype.isReactComponent={},b.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},b.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},g.prototype=b.prototype;var w=v.prototype=new g;w.constructor=v,y(w,b.prototype),w.isPureReactComponent=!0;var _=Array.isArray,S=Object.prototype.hasOwnProperty,x={current:null},k={key:!0,ref:!0,__self:!0,__source:!0};function I(e,t,r){var i,o={},a=null,s=null;if(null!=t)for(i in void 0!==t.ref&&(s=t.ref),void 0!==t.key&&(a=""+t.key),t)S.call(t,i)&&!k.hasOwnProperty(i)&&(o[i]=t[i]);var l=arguments.length-2;if(1===l)o.children=r;else if(1<l){for(var u=Array(l),c=0;c<l;c++)u[c]=arguments[c+2];o.children=u}if(e&&e.defaultProps)for(i in l=e.defaultProps)void 0===o[i]&&(o[i]=l[i]);return{$$typeof:n,type:e,key:a,ref:s,props:o,_owner:x.current}}function T(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var O=/\/+/g;function A(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function C(e,t,i,o,a){var s=typeof e;"undefined"!==s&&"boolean"!==s||(e=null);var l=!1;if(null===e)l=!0;else switch(s){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case n:case r:l=!0}}if(l)return a=a(l=e),e=""===o?"."+A(l,0):o,_(a)?(i="",null!=e&&(i=e.replace(O,"$&/")+"/"),C(a,t,i,"",(function(e){return e}))):null!=a&&(T(a)&&(a=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(a,i+(!a.key||l&&l.key===a.key?"":(""+a.key).replace(O,"$&/")+"/")+e)),t.push(a)),1;if(l=0,o=""===o?".":o+":",_(e))for(var u=0;u<e.length;u++){var c=o+A(s=e[u],u);l+=C(s,t,i,c,a)}else if(c=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=h&&e[h]||e["@@iterator"])?e:null}(e),"function"===typeof c)for(e=c.call(e),u=0;!(s=e.next()).done;)l+=C(s=s.value,t,i,c=o+A(s,u++),a);else if("object"===s)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function E(e,t,n){if(null==e)return e;var r=[],i=0;return C(e,r,"","",(function(e){return t.call(n,e,i++)})),r}function N(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var M={current:null},B={transition:null},L={ReactCurrentDispatcher:M,ReactCurrentBatchConfig:B,ReactCurrentOwner:x};t.Children={map:E,forEach:function(e,t,n){E(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return E(e,(function(){t++})),t},toArray:function(e){return E(e,(function(e){return e}))||[]},only:function(e){if(!T(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=b,t.Fragment=i,t.Profiler=a,t.PureComponent=v,t.StrictMode=o,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=L,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var i=y({},e.props),o=e.key,a=e.ref,s=e._owner;if(null!=t){if(void 0!==t.ref&&(a=t.ref,s=x.current),void 0!==t.key&&(o=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(u in t)S.call(t,u)&&!k.hasOwnProperty(u)&&(i[u]=void 0===t[u]&&void 0!==l?l[u]:t[u])}var u=arguments.length-2;if(1===u)i.children=r;else if(1<u){l=Array(u);for(var c=0;c<u;c++)l[c]=arguments[c+2];i.children=l}return{$$typeof:n,type:e.type,key:o,ref:a,props:i,_owner:s}},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:s,_context:e},e.Consumer=e},t.createElement=I,t.createFactory=function(e){var t=I.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=T,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:N}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=B.transition;B.transition={};try{e()}finally{B.transition=t}},t.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")},t.useCallback=function(e,t){return M.current.useCallback(e,t)},t.useContext=function(e){return M.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return M.current.useDeferredValue(e)},t.useEffect=function(e,t){return M.current.useEffect(e,t)},t.useId=function(){return M.current.useId()},t.useImperativeHandle=function(e,t,n){return M.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return M.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return M.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return M.current.useMemo(e,t)},t.useReducer=function(e,t,n){return M.current.useReducer(e,t,n)},t.useRef=function(e){return M.current.useRef(e)},t.useState=function(e){return M.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return M.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return M.current.useTransition()},t.version="18.2.0"},791:(e,t,n)=>{e.exports=n(117)},184:(e,t,n)=>{e.exports=n(374)},813:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,i=e[r];if(!(0<o(i,t)))break e;e[r]=t,e[n]=i,n=r}}function r(e){return 0===e.length?null:e[0]}function i(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,i=e.length,a=i>>>1;r<a;){var s=2*(r+1)-1,l=e[s],u=s+1,c=e[u];if(0>o(l,n))u<i&&0>o(c,l)?(e[r]=c,e[u]=n,r=u):(e[r]=l,e[s]=n,r=s);else{if(!(u<i&&0>o(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function o(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var a=performance;t.unstable_now=function(){return a.now()}}else{var s=Date,l=s.now();t.unstable_now=function(){return s.now()-l}}var u=[],c=[],d=1,f=null,h=3,p=!1,y=!1,m=!1,b="function"===typeof setTimeout?setTimeout:null,g="function"===typeof clearTimeout?clearTimeout:null,v="undefined"!==typeof setImmediate?setImmediate:null;function w(e){for(var t=r(c);null!==t;){if(null===t.callback)i(c);else{if(!(t.startTime<=e))break;i(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function _(e){if(m=!1,w(e),!y)if(null!==r(u))y=!0,B(S);else{var t=r(c);null!==t&&L(_,t.startTime-e)}}function S(e,n){y=!1,m&&(m=!1,g(T),T=-1),p=!0;var o=h;try{for(w(n),f=r(u);null!==f&&(!(f.expirationTime>n)||e&&!C());){var a=f.callback;if("function"===typeof a){f.callback=null,h=f.priorityLevel;var s=a(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof s?f.callback=s:f===r(u)&&i(u),w(n)}else i(u);f=r(u)}if(null!==f)var l=!0;else{var d=r(c);null!==d&&L(_,d.startTime-n),l=!1}return l}finally{f=null,h=o,p=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var x,k=!1,I=null,T=-1,O=5,A=-1;function C(){return!(t.unstable_now()-A<O)}function E(){if(null!==I){var e=t.unstable_now();A=e;var n=!0;try{n=I(!0,e)}finally{n?x():(k=!1,I=null)}}else k=!1}if("function"===typeof v)x=function(){v(E)};else if("undefined"!==typeof MessageChannel){var N=new MessageChannel,M=N.port2;N.port1.onmessage=E,x=function(){M.postMessage(null)}}else x=function(){b(E,0)};function B(e){I=e,k||(k=!0,x())}function L(e,n){T=b((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){y||p||(y=!0,B(S))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):O=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return h},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(h){case 1:case 2:case 3:var t=3;break;default:t=h}var n=h;h=t;try{return e()}finally{h=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=h;h=e;try{return t()}finally{h=n}},t.unstable_scheduleCallback=function(e,i,o){var a=t.unstable_now();switch("object"===typeof o&&null!==o?o="number"===typeof(o=o.delay)&&0<o?a+o:a:o=a,e){case 1:var s=-1;break;case 2:s=250;break;case 5:s=1073741823;break;case 4:s=1e4;break;default:s=5e3}return e={id:d++,callback:i,priorityLevel:e,startTime:o,expirationTime:s=o+s,sortIndex:-1},o>a?(e.sortIndex=o,n(c,e),null===r(u)&&e===r(c)&&(m?(g(T),T=-1):m=!0,L(_,o-a))):(e.sortIndex=s,n(u,e),y||p||(y=!0,B(S))),e},t.unstable_shouldYield=C,t.unstable_wrapCallback=function(e){var t=h;return function(){var n=h;h=t;try{return e.apply(this,arguments)}finally{h=n}}}},296:(e,t,n)=>{e.exports=n(813)},500:(e,t,n)=>{var r=n(725),i="function"===typeof Symbol&&Symbol.for,o=i?Symbol.for("react.element"):60103,a=i?Symbol.for("react.portal"):60106,s=i?Symbol.for("react.fragment"):60107,l=i?Symbol.for("react.strict_mode"):60108,u=i?Symbol.for("react.profiler"):60114,c=i?Symbol.for("react.provider"):60109,d=i?Symbol.for("react.context"):60110,f=i?Symbol.for("react.forward_ref"):60112,h=i?Symbol.for("react.suspense"):60113,p=i?Symbol.for("react.memo"):60115,y=i?Symbol.for("react.lazy"):60116,m="function"===typeof Symbol&&Symbol.iterator;function b(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var g={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},v={};function w(e,t,n){this.props=e,this.context=t,this.refs=v,this.updater=n||g}function _(){}function S(e,t,n){this.props=e,this.context=t,this.refs=v,this.updater=n||g}w.prototype.isReactComponent={},w.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error(b(85));this.updater.enqueueSetState(this,e,t,"setState")},w.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},_.prototype=w.prototype;var x=S.prototype=new _;x.constructor=S,r(x,w.prototype),x.isPureReactComponent=!0;var k={current:null},I=Object.prototype.hasOwnProperty,T={key:!0,ref:!0,__self:!0,__source:!0};function O(e,t,n){var r,i={},a=null,s=null;if(null!=t)for(r in void 0!==t.ref&&(s=t.ref),void 0!==t.key&&(a=""+t.key),t)I.call(t,r)&&!T.hasOwnProperty(r)&&(i[r]=t[r]);var l=arguments.length-2;if(1===l)i.children=n;else if(1<l){for(var u=Array(l),c=0;c<l;c++)u[c]=arguments[c+2];i.children=u}if(e&&e.defaultProps)for(r in l=e.defaultProps)void 0===i[r]&&(i[r]=l[r]);return{$$typeof:o,type:e,key:a,ref:s,props:i,_owner:k.current}}function A(e){return"object"===typeof e&&null!==e&&e.$$typeof===o}var C=/\/+/g,E=[];function N(e,t,n,r){if(E.length){var i=E.pop();return i.result=e,i.keyPrefix=t,i.func=n,i.context=r,i.count=0,i}return{result:e,keyPrefix:t,func:n,context:r,count:0}}function M(e){e.result=null,e.keyPrefix=null,e.func=null,e.context=null,e.count=0,10>E.length&&E.push(e)}function B(e,t,n,r){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var s=!1;if(null===e)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case o:case a:s=!0}}if(s)return n(r,e,""===t?"."+D(e,0):t),1;if(s=0,t=""===t?".":t+":",Array.isArray(e))for(var l=0;l<e.length;l++){var u=t+D(i=e[l],l);s+=B(i,u,n,r)}else if(null===e||"object"!==typeof e?u=null:u="function"===typeof(u=m&&e[m]||e["@@iterator"])?u:null,"function"===typeof u)for(e=u.call(e),l=0;!(i=e.next()).done;)s+=B(i=i.value,u=t+D(i,l++),n,r);else if("object"===i)throw n=""+e,Error(b(31,"[object Object]"===n?"object with keys {"+Object.keys(e).join(", ")+"}":n,""));return s}function L(e,t,n){return null==e?0:B(e,"",t,n)}function D(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+(""+e).replace(/[=:]/g,(function(e){return t[e]}))}(e.key):t.toString(36)}function F(e,t){e.func.call(e.context,t,e.count++)}function P(e,t,n){var r=e.result,i=e.keyPrefix;e=e.func.call(e.context,t,e.count++),Array.isArray(e)?R(e,r,n,(function(e){return e})):null!=e&&(A(e)&&(e=function(e,t){return{$$typeof:o,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(e,i+(!e.key||t&&t.key===e.key?"":(""+e.key).replace(C,"$&/")+"/")+n)),r.push(e))}function R(e,t,n,r,i){var o="";null!=n&&(o=(""+n).replace(C,"$&/")+"/"),L(e,P,t=N(t,o,r,i)),M(t)}var z={current:null};function U(){var e=z.current;if(null===e)throw Error(b(321));return e}var j={ReactCurrentDispatcher:z,ReactCurrentBatchConfig:{suspense:null},ReactCurrentOwner:k,IsSomeRendererActing:{current:!1},assign:r};t.Children={map:function(e,t,n){if(null==e)return e;var r=[];return R(e,r,null,t,n),r},forEach:function(e,t,n){if(null==e)return e;L(e,F,t=N(null,null,t,n)),M(t)},count:function(e){return L(e,(function(){return null}),null)},toArray:function(e){var t=[];return R(e,t,null,(function(e){return e})),t},only:function(e){if(!A(e))throw Error(b(143));return e}},t.Component=w,t.Fragment=s,t.Profiler=u,t.PureComponent=S,t.StrictMode=l,t.Suspense=h,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=j,t.cloneElement=function(e,t,n){if(null===e||void 0===e)throw Error(b(267,e));var i=r({},e.props),a=e.key,s=e.ref,l=e._owner;if(null!=t){if(void 0!==t.ref&&(s=t.ref,l=k.current),void 0!==t.key&&(a=""+t.key),e.type&&e.type.defaultProps)var u=e.type.defaultProps;for(c in t)I.call(t,c)&&!T.hasOwnProperty(c)&&(i[c]=void 0===t[c]&&void 0!==u?u[c]:t[c])}var c=arguments.length-2;if(1===c)i.children=n;else if(1<c){u=Array(c);for(var d=0;d<c;d++)u[d]=arguments[d+2];i.children=u}return{$$typeof:o,type:e.type,key:a,ref:s,props:i,_owner:l}},t.createContext=function(e,t){return void 0===t&&(t=null),(e={$$typeof:d,_calculateChangedBits:t,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider={$$typeof:c,_context:e},e.Consumer=e},t.createElement=O,t.createFactory=function(e){var t=O.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:f,render:e}},t.isValidElement=A,t.lazy=function(e){return{$$typeof:y,_ctor:e,_status:-1,_result:null}},t.memo=function(e,t){return{$$typeof:p,type:e,compare:void 0===t?null:t}},t.useCallback=function(e,t){return U().useCallback(e,t)},t.useContext=function(e,t){return U().useContext(e,t)},t.useDebugValue=function(){},t.useEffect=function(e,t){return U().useEffect(e,t)},t.useImperativeHandle=function(e,t,n){return U().useImperativeHandle(e,t,n)},t.useLayoutEffect=function(e,t){return U().useLayoutEffect(e,t)},t.useMemo=function(e,t){return U().useMemo(e,t)},t.useReducer=function(e,t,n){return U().useReducer(e,t,n)},t.useRef=function(e){return U().useRef(e)},t.useState=function(e){return U().useState(e)},t.version="16.14.0"},291:(e,t,n)=>{e.exports=n(500)}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,i){if(1&i&&(r=this(r)),8&i)return r;if("object"===typeof r&&r){if(4&i&&r.__esModule)return r;if(16&i&&"function"===typeof r.then)return r}var o=Object.create(null);n.r(o);var a={};e=e||[null,t({}),t([]),t(t)];for(var s=2&i&&r;"object"==typeof s&&!~e.indexOf(s);s=t(s))Object.getOwnPropertyNames(s).forEach((e=>a[e]=()=>r[e]));return a.default=()=>r,n.d(o,a),o}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e,t,r,i,o,a,s,l,u,c=n(791),d=n.t(c,2),f=n(164),h=n(110),p=n.n(h),y=n(291);!function(e){e[e.V1=0]="V1",e[e.V2=1]="V2",e[e.V3=2]="V3",e[e.V4=3]="V4",e[e.V5=4]="V5"}(e||(e={})),function(e){e[e.Sparse=0]="Sparse",e[e.Dense=1]="Dense"}(t||(t={})),function(e){e[e.HALF=0]="HALF",e[e.SINGLE=1]="SINGLE",e[e.DOUBLE=2]="DOUBLE"}(r||(r={})),function(e){e[e.DAY=0]="DAY",e[e.MILLISECOND=1]="MILLISECOND"}(i||(i={})),function(e){e[e.SECOND=0]="SECOND",e[e.MILLISECOND=1]="MILLISECOND",e[e.MICROSECOND=2]="MICROSECOND",e[e.NANOSECOND=3]="NANOSECOND"}(o||(o={})),function(e){e[e.YEAR_MONTH=0]="YEAR_MONTH",e[e.DAY_TIME=1]="DAY_TIME",e[e.MONTH_DAY_NANO=2]="MONTH_DAY_NANO"}(a||(a={})),function(e){e[e.NONE=0]="NONE",e[e.Schema=1]="Schema",e[e.DictionaryBatch=2]="DictionaryBatch",e[e.RecordBatch=3]="RecordBatch",e[e.Tensor=4]="Tensor",e[e.SparseTensor=5]="SparseTensor"}(s||(s={})),function(e){e[e.NONE=0]="NONE",e[e.Null=1]="Null",e[e.Int=2]="Int",e[e.Float=3]="Float",e[e.Binary=4]="Binary",e[e.Utf8=5]="Utf8",e[e.Bool=6]="Bool",e[e.Decimal=7]="Decimal",e[e.Date=8]="Date",e[e.Time=9]="Time",e[e.Timestamp=10]="Timestamp",e[e.Interval=11]="Interval",e[e.List=12]="List",e[e.Struct=13]="Struct",e[e.Union=14]="Union",e[e.FixedSizeBinary=15]="FixedSizeBinary",e[e.FixedSizeList=16]="FixedSizeList",e[e.Map=17]="Map",e[e.Dictionary=-1]="Dictionary",e[e.Int8=-2]="Int8",e[e.Int16=-3]="Int16",e[e.Int32=-4]="Int32",e[e.Int64=-5]="Int64",e[e.Uint8=-6]="Uint8",e[e.Uint16=-7]="Uint16",e[e.Uint32=-8]="Uint32",e[e.Uint64=-9]="Uint64",e[e.Float16=-10]="Float16",e[e.Float32=-11]="Float32",e[e.Float64=-12]="Float64",e[e.DateDay=-13]="DateDay",e[e.DateMillisecond=-14]="DateMillisecond",e[e.TimestampSecond=-15]="TimestampSecond",e[e.TimestampMillisecond=-16]="TimestampMillisecond",e[e.TimestampMicrosecond=-17]="TimestampMicrosecond",e[e.TimestampNanosecond=-18]="TimestampNanosecond",e[e.TimeSecond=-19]="TimeSecond",e[e.TimeMillisecond=-20]="TimeMillisecond",e[e.TimeMicrosecond=-21]="TimeMicrosecond",e[e.TimeNanosecond=-22]="TimeNanosecond",e[e.DenseUnion=-23]="DenseUnion",e[e.SparseUnion=-24]="SparseUnion",e[e.IntervalDayTime=-25]="IntervalDayTime",e[e.IntervalYearMonth=-26]="IntervalYearMonth"}(l||(l={})),function(e){e[e.OFFSET=0]="OFFSET",e[e.DATA=1]="DATA",e[e.VALIDITY=2]="VALIDITY",e[e.TYPE=3]="TYPE"}(u||(u={}));const[m,b]=(()=>{const e=()=>{throw new Error("BigInt is not available in this environment")};function t(){throw e()}return t.asIntN=()=>{throw e()},t.asUintN=()=>{throw e()},"undefined"!==typeof BigInt?[BigInt,!0]:[t,!1]})(),[g,v]=(()=>{const e=()=>{throw new Error("BigInt64Array is not available in this environment")};return"undefined"!==typeof BigInt64Array?[BigInt64Array,!0]:[class{static get BYTES_PER_ELEMENT(){return 8}static of(){throw e()}static from(){throw e()}constructor(){throw e()}},!1]})(),[w,_]=(()=>{const e=()=>{throw new Error("BigUint64Array is not available in this environment")};return"undefined"!==typeof BigUint64Array?[BigUint64Array,!0]:[class{static get BYTES_PER_ELEMENT(){return 8}static of(){throw e()}static from(){throw e()}constructor(){throw e()}},!1]})(),S=e=>"number"===typeof e,x=e=>"boolean"===typeof e,k=e=>"function"===typeof e,I=e=>null!=e&&Object(e)===e,T=e=>I(e)&&k(e.then),O=e=>I(e)&&k(e[Symbol.iterator]),A=e=>I(e)&&k(e[Symbol.asyncIterator]),C=e=>I(e)&&I(e.schema),E=e=>I(e)&&"done"in e&&"value"in e,N=e=>I(e)&&k(e.stat)&&S(e.fd),M=e=>I(e)&&L(e.body),B=e=>"_getDOMStream"in e&&"_getNodeStream"in e,L=e=>I(e)&&k(e.cancel)&&k(e.getReader)&&!B(e),D=e=>I(e)&&k(e.read)&&k(e.pipe)&&x(e.readable)&&!B(e),F=e=>I(e)&&k(e.clear)&&k(e.bytes)&&k(e.position)&&k(e.setPosition)&&k(e.capacity)&&k(e.getBufferIdentifier)&&k(e.createLong);function P(e){if(null===e)return"null";if(undefined===e)return"undefined";switch(typeof e){case"number":case"bigint":return"".concat(e);case"string":return'"'.concat(e,'"')}return"function"===typeof e[Symbol.toPrimitive]?e[Symbol.toPrimitive]("string"):ArrayBuffer.isView(e)?"[".concat(e instanceof g||e instanceof w?[...e].map((e=>P(e))):e,"]"):ArrayBuffer.isView(e)?"[".concat(e,"]"):JSON.stringify(e,((e,t)=>"bigint"===typeof t?"".concat(t):t))}function R(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{l(r.next(e))}catch(t){o(t)}}function s(e){try{l(r.throw(e))}catch(t){o(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}l((r=r.apply(e,t||[])).next())}))}Object.create;function z(e){var t="function"===typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"===typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function U(e){return this instanceof U?(this.v=e,this):new U(e)}function j(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,i=n.apply(e,t||[]),o=[];return r={},a("next"),a("throw"),a("return"),r[Symbol.asyncIterator]=function(){return this},r;function a(e){i[e]&&(r[e]=function(t){return new Promise((function(n,r){o.push([e,t,n,r])>1||s(e,t)}))})}function s(e,t){try{!function(e){e.value instanceof U?Promise.resolve(e.value.v).then(l,u):c(o[0][2],e)}(i[e](t))}catch(n){c(o[0][3],n)}}function l(e){s("next",e)}function u(e){s("throw",e)}function c(e,t){e(t),o.shift(),o.length&&s(o[0][0],o[0][1])}}function V(e){var t,n;return t={},r("next"),r("throw",(function(e){throw e})),r("return"),t[Symbol.iterator]=function(){return this},t;function r(r,i){t[r]=e[r]?function(t){return(n=!n)?{value:U(e[r](t)),done:!1}:i?i(t):t}:i}}function W(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=z(e),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(n){t[n]=e[n]&&function(t){return new Promise((function(r,i){(function(e,t,n,r){Promise.resolve(r).then((function(t){e({value:t,done:n})}),t)})(r,i,(t=e[n](t)).done,t.value)}))}}}Object.create;"function"===typeof SuppressedError&&SuppressedError;const $=new TextDecoder("utf-8"),H=e=>$.decode(e),Y=new TextEncoder,K=e=>Y.encode(e),Q="undefined"!==typeof SharedArrayBuffer?SharedArrayBuffer:ArrayBuffer;function q(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:t.byteLength;const i=e.byteLength,o=new Uint8Array(e.buffer,e.byteOffset,i),a=new Uint8Array(t.buffer,t.byteOffset,Math.min(r,i));return o.set(a,n),e}function G(e,t){const n=function(e){const t=e[0]?[e[0]]:[];let n,r,i,o;for(let a,s,l=0,u=0,c=e.length;++l<c;)a=t[u],s=e[l],!a||!s||a.buffer!==s.buffer||s.byteOffset<a.byteOffset?s&&(t[++u]=s):(({byteOffset:n,byteLength:i}=a),({byteOffset:r,byteLength:o}=s),n+i<r||r+o<n?s&&(t[++u]=s):t[u]=new Uint8Array(a.buffer,n,r-n+o));return t}(e),r=n.reduce(((e,t)=>e+t.byteLength),0);let i,o,a,s=0,l=-1;const u=Math.min(t||Number.POSITIVE_INFINITY,r);for(const c=n.length;++l<c;){if(i=n[l],o=i.subarray(0,Math.min(i.length,u-s)),u<=s+o.length){o.length<i.length?n[l]=i.subarray(o.length):o.length===i.length&&l++,a?q(a,o,s):a=o;break}q(a||(a=new Uint8Array(u)),o,s),s+=o.length}return[a||new Uint8Array(0),n.slice(l),r-(a?a.byteLength:0)]}function X(e,t){let n=E(t)?t.value:t;return n instanceof e?e===Uint8Array?new e(n.buffer,n.byteOffset,n.byteLength):n:n?("string"===typeof n&&(n=K(n)),n instanceof ArrayBuffer||n instanceof Q?new e(n):F(n)?X(e,n.bytes()):ArrayBuffer.isView(n)?n.byteLength<=0?new e(0):new e(n.buffer,n.byteOffset,n.byteLength/e.BYTES_PER_ELEMENT):e.from(n)):new e(0)}const J=e=>X(Int32Array,e),Z=e=>X(Uint8Array,e),ee=e=>(e.next(),e);function*te(e,t){const n=function*(e){yield e},r="string"===typeof t||ArrayBuffer.isView(t)||t instanceof ArrayBuffer||t instanceof Q?n(t):O(t)?t:n(t);return yield*ee(function*(t){let n=null;do{n=t.next(yield X(e,n))}while(!n.done)}(r[Symbol.iterator]())),new e}function ne(e,t){return j(this,arguments,(function*(){if(T(t))return yield U(yield U(yield*V(W(ne(e,yield U(t))))));const n=function(e){return j(this,arguments,(function*(){yield yield U(yield U(e))}))},r="string"===typeof t||ArrayBuffer.isView(t)||t instanceof ArrayBuffer||t instanceof Q?n(t):O(t)?function(e){return j(this,arguments,(function*(){yield U(yield*V(W(ee(function*(e){let t=null;do{t=e.next(yield null===t||void 0===t?void 0:t.value)}while(!t.done)}(e[Symbol.iterator]())))))}))}(t):A(t)?t:n(t);return yield U(yield*V(W(ee(function(t){return j(this,arguments,(function*(){let n=null;do{n=yield U(t.next(yield yield U(X(e,n))))}while(!n.done)}))}(r[Symbol.asyncIterator]()))))),yield U(new e)}))}function re(e,t,n){if(0!==e){n=n.slice(0,t+1);for(let r=-1;++r<=t;)n[r]+=e}return n}const ie=Symbol.for("isArrowBigNum");function oe(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return 0===n.length?Object.setPrototypeOf(X(this.TypedArray,e),this.constructor.prototype):Object.setPrototypeOf(new this.TypedArray(e,...n),this.constructor.prototype)}function ae(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return oe.apply(this,t)}function se(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return oe.apply(this,t)}function le(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return oe.apply(this,t)}function ue(e){const{buffer:t,byteOffset:n,length:r,signed:i}=e,o=new w(t,n,r),a=i&&o[o.length-1]&BigInt(1)<<BigInt(63);let s=a?BigInt(1):BigInt(0),l=BigInt(0);if(a){for(const e of o)s+=~e*(BigInt(1)<<BigInt(32)*l++);s*=BigInt(-1)}else for(const u of o)s+=u*(BigInt(1)<<BigInt(32)*l++);return s}let ce,de;function fe(e){let t="";const n=new Uint32Array(2);let r=new Uint16Array(e.buffer,e.byteOffset,e.byteLength/2);const i=new Uint32Array((r=new Uint16Array(r).reverse()).buffer);let o=-1;const a=r.length-1;do{for(n[0]=r[o=0];o<a;)r[o++]=n[1]=n[0]/10,n[0]=(n[0]-10*n[1]<<16)+r[o];r[o]=n[1]=n[0]/10,n[0]=n[0]-10*n[1],t="".concat(n[0]).concat(t)}while(i[0]||i[1]||i[2]||i[3]);return null!==t&&void 0!==t?t:"0"}oe.prototype[ie]=!0,oe.prototype.toJSON=function(){return'"'.concat(ce(this),'"')},oe.prototype.valueOf=function(){return ue(this)},oe.prototype.toString=function(){return ce(this)},oe.prototype[Symbol.toPrimitive]=function(){switch(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default"){case"number":return ue(this);case"string":return ce(this);case"default":return de(this)}return ce(this)},Object.setPrototypeOf(ae.prototype,Object.create(Int32Array.prototype)),Object.setPrototypeOf(se.prototype,Object.create(Uint32Array.prototype)),Object.setPrototypeOf(le.prototype,Object.create(Uint32Array.prototype)),Object.assign(ae.prototype,oe.prototype,{constructor:ae,signed:!0,TypedArray:Int32Array,BigIntArray:g}),Object.assign(se.prototype,oe.prototype,{constructor:se,signed:!1,TypedArray:Uint32Array,BigIntArray:w}),Object.assign(le.prototype,oe.prototype,{constructor:le,signed:!0,TypedArray:Uint32Array,BigIntArray:w}),b?(de=e=>8===e.byteLength?new e.BigIntArray(e.buffer,e.byteOffset,1)[0]:fe(e),ce=e=>8===e.byteLength?"".concat(new e.BigIntArray(e.buffer,e.byteOffset,1)[0]):fe(e)):(ce=fe,de=ce);class he{static new(e,t){switch(t){case!0:return new ae(e);case!1:return new se(e)}switch(e.constructor){case Int8Array:case Int16Array:case Int32Array:case g:return new ae(e)}return 16===e.byteLength?new le(e):new se(e)}static signed(e){return new ae(e)}static unsigned(e){return new se(e)}static decimal(e){return new le(e)}constructor(e,t){return he.new(e,t)}}var pe,ye,me,be,ge,ve,we,_e,Se,xe,ke,Ie,Te,Oe,Ae,Ce,Ee,Ne,Me,Be;class Le{static isNull(e){return(null===e||void 0===e?void 0:e.typeId)===l.Null}static isInt(e){return(null===e||void 0===e?void 0:e.typeId)===l.Int}static isFloat(e){return(null===e||void 0===e?void 0:e.typeId)===l.Float}static isBinary(e){return(null===e||void 0===e?void 0:e.typeId)===l.Binary}static isUtf8(e){return(null===e||void 0===e?void 0:e.typeId)===l.Utf8}static isBool(e){return(null===e||void 0===e?void 0:e.typeId)===l.Bool}static isDecimal(e){return(null===e||void 0===e?void 0:e.typeId)===l.Decimal}static isDate(e){return(null===e||void 0===e?void 0:e.typeId)===l.Date}static isTime(e){return(null===e||void 0===e?void 0:e.typeId)===l.Time}static isTimestamp(e){return(null===e||void 0===e?void 0:e.typeId)===l.Timestamp}static isInterval(e){return(null===e||void 0===e?void 0:e.typeId)===l.Interval}static isList(e){return(null===e||void 0===e?void 0:e.typeId)===l.List}static isStruct(e){return(null===e||void 0===e?void 0:e.typeId)===l.Struct}static isUnion(e){return(null===e||void 0===e?void 0:e.typeId)===l.Union}static isFixedSizeBinary(e){return(null===e||void 0===e?void 0:e.typeId)===l.FixedSizeBinary}static isFixedSizeList(e){return(null===e||void 0===e?void 0:e.typeId)===l.FixedSizeList}static isMap(e){return(null===e||void 0===e?void 0:e.typeId)===l.Map}static isDictionary(e){return(null===e||void 0===e?void 0:e.typeId)===l.Dictionary}static isDenseUnion(e){return Le.isUnion(e)&&e.mode===t.Dense}static isSparseUnion(e){return Le.isUnion(e)&&e.mode===t.Sparse}get typeId(){return l.NONE}}pe=Symbol.toStringTag,Le[pe]=((Be=Le.prototype).children=null,Be.ArrayType=Array,Be[Symbol.toStringTag]="DataType");class De extends Le{toString(){return"Null"}get typeId(){return l.Null}}ye=Symbol.toStringTag,De[ye]=(e=>e[Symbol.toStringTag]="Null")(De.prototype);class Fe extends Le{constructor(e,t){super(),this.isSigned=e,this.bitWidth=t}get typeId(){return l.Int}get ArrayType(){switch(this.bitWidth){case 8:return this.isSigned?Int8Array:Uint8Array;case 16:return this.isSigned?Int16Array:Uint16Array;case 32:return this.isSigned?Int32Array:Uint32Array;case 64:return this.isSigned?g:w}throw new Error("Unrecognized ".concat(this[Symbol.toStringTag]," type"))}toString(){return"".concat(this.isSigned?"I":"Ui","nt").concat(this.bitWidth)}}me=Symbol.toStringTag,Fe[me]=(e=>(e.isSigned=null,e.bitWidth=null,e[Symbol.toStringTag]="Int"))(Fe.prototype);class Pe extends Fe{constructor(){super(!0,32)}get ArrayType(){return Int32Array}}Object.defineProperty(class extends Fe{constructor(){super(!0,8)}get ArrayType(){return Int8Array}}.prototype,"ArrayType",{value:Int8Array}),Object.defineProperty(class extends Fe{constructor(){super(!0,16)}get ArrayType(){return Int16Array}}.prototype,"ArrayType",{value:Int16Array}),Object.defineProperty(Pe.prototype,"ArrayType",{value:Int32Array}),Object.defineProperty(class extends Fe{constructor(){super(!0,64)}get ArrayType(){return g}}.prototype,"ArrayType",{value:g}),Object.defineProperty(class extends Fe{constructor(){super(!1,8)}get ArrayType(){return Uint8Array}}.prototype,"ArrayType",{value:Uint8Array}),Object.defineProperty(class extends Fe{constructor(){super(!1,16)}get ArrayType(){return Uint16Array}}.prototype,"ArrayType",{value:Uint16Array}),Object.defineProperty(class extends Fe{constructor(){super(!1,32)}get ArrayType(){return Uint32Array}}.prototype,"ArrayType",{value:Uint32Array}),Object.defineProperty(class extends Fe{constructor(){super(!1,64)}get ArrayType(){return w}}.prototype,"ArrayType",{value:w});class Re extends Le{constructor(e){super(),this.precision=e}get typeId(){return l.Float}get ArrayType(){switch(this.precision){case r.HALF:return Uint16Array;case r.SINGLE:return Float32Array;case r.DOUBLE:return Float64Array}throw new Error("Unrecognized ".concat(this[Symbol.toStringTag]," type"))}toString(){return"Float".concat(this.precision<<5||16)}}be=Symbol.toStringTag,Re[be]=(e=>(e.precision=null,e[Symbol.toStringTag]="Float"))(Re.prototype);Object.defineProperty(class extends Re{constructor(){super(r.HALF)}}.prototype,"ArrayType",{value:Uint16Array}),Object.defineProperty(class extends Re{constructor(){super(r.SINGLE)}}.prototype,"ArrayType",{value:Float32Array}),Object.defineProperty(class extends Re{constructor(){super(r.DOUBLE)}}.prototype,"ArrayType",{value:Float64Array});class ze extends Le{constructor(){super()}get typeId(){return l.Binary}toString(){return"Binary"}}ge=Symbol.toStringTag,ze[ge]=(e=>(e.ArrayType=Uint8Array,e[Symbol.toStringTag]="Binary"))(ze.prototype);class Ue extends Le{constructor(){super()}get typeId(){return l.Utf8}toString(){return"Utf8"}}ve=Symbol.toStringTag,Ue[ve]=(e=>(e.ArrayType=Uint8Array,e[Symbol.toStringTag]="Utf8"))(Ue.prototype);class je extends Le{constructor(){super()}get typeId(){return l.Bool}toString(){return"Bool"}}we=Symbol.toStringTag,je[we]=(e=>(e.ArrayType=Uint8Array,e[Symbol.toStringTag]="Bool"))(je.prototype);class Ve extends Le{constructor(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:128;super(),this.scale=e,this.precision=t,this.bitWidth=n}get typeId(){return l.Decimal}toString(){return"Decimal[".concat(this.precision,"e").concat(this.scale>0?"+":"").concat(this.scale,"]")}}_e=Symbol.toStringTag,Ve[_e]=(e=>(e.scale=null,e.precision=null,e.ArrayType=Uint32Array,e[Symbol.toStringTag]="Decimal"))(Ve.prototype);class We extends Le{constructor(e){super(),this.unit=e}get typeId(){return l.Date}toString(){return"Date".concat(32*(this.unit+1),"<").concat(i[this.unit],">")}}Se=Symbol.toStringTag,We[Se]=(e=>(e.unit=null,e.ArrayType=Int32Array,e[Symbol.toStringTag]="Date"))(We.prototype);class $e extends Le{constructor(e,t){super(),this.unit=e,this.bitWidth=t}get typeId(){return l.Time}toString(){return"Time".concat(this.bitWidth,"<").concat(o[this.unit],">")}get ArrayType(){switch(this.bitWidth){case 32:return Int32Array;case 64:return g}throw new Error("Unrecognized ".concat(this[Symbol.toStringTag]," type"))}}xe=Symbol.toStringTag,$e[xe]=(e=>(e.unit=null,e.bitWidth=null,e[Symbol.toStringTag]="Time"))($e.prototype);class He extends Le{constructor(e,t){super(),this.unit=e,this.timezone=t}get typeId(){return l.Timestamp}toString(){return"Timestamp<".concat(o[this.unit]).concat(this.timezone?", ".concat(this.timezone):"",">")}}ke=Symbol.toStringTag,He[ke]=(e=>(e.unit=null,e.timezone=null,e.ArrayType=Int32Array,e[Symbol.toStringTag]="Timestamp"))(He.prototype);class Ye extends Le{constructor(e){super(),this.unit=e}get typeId(){return l.Interval}toString(){return"Interval<".concat(a[this.unit],">")}}Ie=Symbol.toStringTag,Ye[Ie]=(e=>(e.unit=null,e.ArrayType=Int32Array,e[Symbol.toStringTag]="Interval"))(Ye.prototype);class Ke extends Le{constructor(e){super(),this.children=[e]}get typeId(){return l.List}toString(){return"List<".concat(this.valueType,">")}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get ArrayType(){return this.valueType.ArrayType}}Te=Symbol.toStringTag,Ke[Te]=(e=>(e.children=null,e[Symbol.toStringTag]="List"))(Ke.prototype);class Qe extends Le{constructor(e){super(),this.children=e}get typeId(){return l.Struct}toString(){return"Struct<{".concat(this.children.map((e=>"".concat(e.name,":").concat(e.type))).join(", "),"}>")}}Oe=Symbol.toStringTag,Qe[Oe]=(e=>(e.children=null,e[Symbol.toStringTag]="Struct"))(Qe.prototype);class qe extends Le{constructor(e,t,n){super(),this.mode=e,this.children=n,this.typeIds=t=Int32Array.from(t),this.typeIdToChildIndex=t.reduce(((e,t,n)=>(e[t]=n)&&e||e),Object.create(null))}get typeId(){return l.Union}toString(){return"".concat(this[Symbol.toStringTag],"<").concat(this.children.map((e=>"".concat(e.type))).join(" | "),">")}}Ae=Symbol.toStringTag,qe[Ae]=(e=>(e.mode=null,e.typeIds=null,e.children=null,e.typeIdToChildIndex=null,e.ArrayType=Int8Array,e[Symbol.toStringTag]="Union"))(qe.prototype);class Ge extends Le{constructor(e){super(),this.byteWidth=e}get typeId(){return l.FixedSizeBinary}toString(){return"FixedSizeBinary[".concat(this.byteWidth,"]")}}Ce=Symbol.toStringTag,Ge[Ce]=(e=>(e.byteWidth=null,e.ArrayType=Uint8Array,e[Symbol.toStringTag]="FixedSizeBinary"))(Ge.prototype);class Xe extends Le{constructor(e,t){super(),this.listSize=e,this.children=[t]}get typeId(){return l.FixedSizeList}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get ArrayType(){return this.valueType.ArrayType}toString(){return"FixedSizeList[".concat(this.listSize,"]<").concat(this.valueType,">")}}Ee=Symbol.toStringTag,Xe[Ee]=(e=>(e.children=null,e.listSize=null,e[Symbol.toStringTag]="FixedSizeList"))(Xe.prototype);class Je extends Le{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];super(),this.children=[e],this.keysSorted=t}get typeId(){return l.Map}get keyType(){return this.children[0].type.children[0].type}get valueType(){return this.children[0].type.children[1].type}get childType(){return this.children[0].type}toString(){return"Map<{".concat(this.children[0].type.children.map((e=>"".concat(e.name,":").concat(e.type))).join(", "),"}>")}}Ne=Symbol.toStringTag,Je[Ne]=(e=>(e.children=null,e.keysSorted=null,e[Symbol.toStringTag]="Map_"))(Je.prototype);const Ze=(et=-1,()=>++et);var et;class tt extends Le{constructor(e,t,n,r){super(),this.indices=t,this.dictionary=e,this.isOrdered=r||!1,this.id=null==n?Ze():"number"===typeof n?n:n.low}get typeId(){return l.Dictionary}get children(){return this.dictionary.children}get valueType(){return this.dictionary}get ArrayType(){return this.dictionary.ArrayType}toString(){return"Dictionary<".concat(this.indices,", ").concat(this.dictionary,">")}}function nt(e){const t=e;switch(e.typeId){case l.Decimal:return e.bitWidth/32;case l.Timestamp:return 2;case l.Date:case l.Interval:return 1+t.unit;case l.FixedSizeList:return t.listSize;case l.FixedSizeBinary:return t.byteWidth;default:return 1}}Me=Symbol.toStringTag,tt[Me]=(e=>(e.id=null,e.indices=null,e.isOrdered=null,e.dictionary=null,e[Symbol.toStringTag]="Dictionary"))(tt.prototype);class rt{visitMany(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return e.map(((e,t)=>this.visit(e,...n.map((e=>e[t])))))}visit(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.getVisitFn(t[0],!1).apply(this,t)}getVisitFn(e){return function(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if("number"===typeof t)return it(e,t,n);if("string"===typeof t&&t in l)return it(e,l[t],n);if(t&&t instanceof Le)return it(e,ot(t),n);if((null===t||void 0===t?void 0:t.type)&&t.type instanceof Le)return it(e,ot(t.type),n);return it(e,l.NONE,n)}(this,e,!(arguments.length>1&&void 0!==arguments[1])||arguments[1])}getVisitFnByTypeId(e){return it(this,e,!(arguments.length>1&&void 0!==arguments[1])||arguments[1])}visitNull(e){return null}visitBool(e){return null}visitInt(e){return null}visitFloat(e){return null}visitUtf8(e){return null}visitBinary(e){return null}visitFixedSizeBinary(e){return null}visitDate(e){return null}visitTimestamp(e){return null}visitTime(e){return null}visitDecimal(e){return null}visitList(e){return null}visitStruct(e){return null}visitUnion(e){return null}visitDictionary(e){return null}visitInterval(e){return null}visitFixedSizeList(e){return null}visitMap(e){return null}}function it(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=null;switch(t){case l.Null:r=e.visitNull;break;case l.Bool:r=e.visitBool;break;case l.Int:r=e.visitInt;break;case l.Int8:r=e.visitInt8||e.visitInt;break;case l.Int16:r=e.visitInt16||e.visitInt;break;case l.Int32:r=e.visitInt32||e.visitInt;break;case l.Int64:r=e.visitInt64||e.visitInt;break;case l.Uint8:r=e.visitUint8||e.visitInt;break;case l.Uint16:r=e.visitUint16||e.visitInt;break;case l.Uint32:r=e.visitUint32||e.visitInt;break;case l.Uint64:r=e.visitUint64||e.visitInt;break;case l.Float:r=e.visitFloat;break;case l.Float16:r=e.visitFloat16||e.visitFloat;break;case l.Float32:r=e.visitFloat32||e.visitFloat;break;case l.Float64:r=e.visitFloat64||e.visitFloat;break;case l.Utf8:r=e.visitUtf8;break;case l.Binary:r=e.visitBinary;break;case l.FixedSizeBinary:r=e.visitFixedSizeBinary;break;case l.Date:r=e.visitDate;break;case l.DateDay:r=e.visitDateDay||e.visitDate;break;case l.DateMillisecond:r=e.visitDateMillisecond||e.visitDate;break;case l.Timestamp:r=e.visitTimestamp;break;case l.TimestampSecond:r=e.visitTimestampSecond||e.visitTimestamp;break;case l.TimestampMillisecond:r=e.visitTimestampMillisecond||e.visitTimestamp;break;case l.TimestampMicrosecond:r=e.visitTimestampMicrosecond||e.visitTimestamp;break;case l.TimestampNanosecond:r=e.visitTimestampNanosecond||e.visitTimestamp;break;case l.Time:r=e.visitTime;break;case l.TimeSecond:r=e.visitTimeSecond||e.visitTime;break;case l.TimeMillisecond:r=e.visitTimeMillisecond||e.visitTime;break;case l.TimeMicrosecond:r=e.visitTimeMicrosecond||e.visitTime;break;case l.TimeNanosecond:r=e.visitTimeNanosecond||e.visitTime;break;case l.Decimal:r=e.visitDecimal;break;case l.List:r=e.visitList;break;case l.Struct:r=e.visitStruct;break;case l.Union:r=e.visitUnion;break;case l.DenseUnion:r=e.visitDenseUnion||e.visitUnion;break;case l.SparseUnion:r=e.visitSparseUnion||e.visitUnion;break;case l.Dictionary:r=e.visitDictionary;break;case l.Interval:r=e.visitInterval;break;case l.IntervalDayTime:r=e.visitIntervalDayTime||e.visitInterval;break;case l.IntervalYearMonth:r=e.visitIntervalYearMonth||e.visitInterval;break;case l.FixedSizeList:r=e.visitFixedSizeList;break;case l.Map:r=e.visitMap}if("function"===typeof r)return r;if(!n)return()=>null;throw new Error("Unrecognized type '".concat(l[t],"'"))}function ot(e){switch(e.typeId){case l.Null:return l.Null;case l.Int:{const{bitWidth:t,isSigned:n}=e;switch(t){case 8:return n?l.Int8:l.Uint8;case 16:return n?l.Int16:l.Uint16;case 32:return n?l.Int32:l.Uint32;case 64:return n?l.Int64:l.Uint64}return l.Int}case l.Float:switch(e.precision){case r.HALF:return l.Float16;case r.SINGLE:return l.Float32;case r.DOUBLE:return l.Float64}return l.Float;case l.Binary:return l.Binary;case l.Utf8:return l.Utf8;case l.Bool:return l.Bool;case l.Decimal:return l.Decimal;case l.Time:switch(e.unit){case o.SECOND:return l.TimeSecond;case o.MILLISECOND:return l.TimeMillisecond;case o.MICROSECOND:return l.TimeMicrosecond;case o.NANOSECOND:return l.TimeNanosecond}return l.Time;case l.Timestamp:switch(e.unit){case o.SECOND:return l.TimestampSecond;case o.MILLISECOND:return l.TimestampMillisecond;case o.MICROSECOND:return l.TimestampMicrosecond;case o.NANOSECOND:return l.TimestampNanosecond}return l.Timestamp;case l.Date:switch(e.unit){case i.DAY:return l.DateDay;case i.MILLISECOND:return l.DateMillisecond}return l.Date;case l.Interval:switch(e.unit){case a.DAY_TIME:return l.IntervalDayTime;case a.YEAR_MONTH:return l.IntervalYearMonth}return l.Interval;case l.Map:return l.Map;case l.List:return l.List;case l.Struct:return l.Struct;case l.Union:switch(e.mode){case t.Dense:return l.DenseUnion;case t.Sparse:return l.SparseUnion}return l.Union;case l.FixedSizeBinary:return l.FixedSizeBinary;case l.FixedSizeList:return l.FixedSizeList;case l.Dictionary:return l.Dictionary}throw new Error("Unrecognized type '".concat(l[e.typeId],"'"))}rt.prototype.visitInt8=null,rt.prototype.visitInt16=null,rt.prototype.visitInt32=null,rt.prototype.visitInt64=null,rt.prototype.visitUint8=null,rt.prototype.visitUint16=null,rt.prototype.visitUint32=null,rt.prototype.visitUint64=null,rt.prototype.visitFloat16=null,rt.prototype.visitFloat32=null,rt.prototype.visitFloat64=null,rt.prototype.visitDateDay=null,rt.prototype.visitDateMillisecond=null,rt.prototype.visitTimestampSecond=null,rt.prototype.visitTimestampMillisecond=null,rt.prototype.visitTimestampMicrosecond=null,rt.prototype.visitTimestampNanosecond=null,rt.prototype.visitTimeSecond=null,rt.prototype.visitTimeMillisecond=null,rt.prototype.visitTimeMicrosecond=null,rt.prototype.visitTimeNanosecond=null,rt.prototype.visitDenseUnion=null,rt.prototype.visitSparseUnion=null,rt.prototype.visitIntervalDayTime=null,rt.prototype.visitIntervalYearMonth=null;const at=new Float64Array(1),st=new Uint32Array(at.buffer);function lt(e){const t=(31744&e)>>10,n=(1023&e)/1024,r=Math.pow(-1,(32768&e)>>15);switch(t){case 31:return r*(n?Number.NaN:1/0);case 0:return r*(n?6103515625e-14*n:0)}return r*Math.pow(2,t-15)*(1+n)}class ut extends rt{}function ct(e){return(t,n,r)=>{if(t.setValid(n,null!=r))return e(t,n,r)}}const dt=(e,t,n)=>{e[t]=Math.trunc(n%4294967296),e[t+1]=Math.trunc(n/4294967296)},ft=(e,t,n,r)=>{if(n+1<t.length){const{[n]:i,[n+1]:o}=t;e.set(r.subarray(0,o-i),i)}},ht=(e,t,n)=>{let{values:r}=e;r[t]=n},pt=(e,t,n)=>{let{values:r}=e;r[t]=n},yt=(e,t,n)=>{let{values:r}=e;r[t]=function(e){if(e!==e)return 32256;at[0]=e;const t=(2147483648&st[1])>>16&65535;let n=2146435072&st[1],r=0;return n>=1089470464?st[0]>0?n=31744:(n=(2080374784&n)>>16,r=(1048575&st[1])>>10):n<=1056964608?(r=1048576+(1048575&st[1]),r=1048576+(r<<(n>>20)-998)>>21,n=0):(n=n-1056964608>>10,r=512+(1048575&st[1])>>10),t|n|65535&r}(n)},mt=(e,t,n)=>{let{values:r}=e;((e,t,n)=>{e[t]=Math.trunc(n/864e5)})(r,t,n.valueOf())},bt=(e,t,n)=>{let{values:r}=e;dt(r,2*t,n.valueOf())},gt=(e,t,n)=>{let{values:r}=e;return dt(r,2*t,n/1e3)},vt=(e,t,n)=>{let{values:r}=e;return dt(r,2*t,n)},wt=(e,t,n)=>{let{values:r}=e;return((e,t,n)=>{e[t]=Math.trunc(1e3*n%4294967296),e[t+1]=Math.trunc(1e3*n/4294967296)})(r,2*t,n)},_t=(e,t,n)=>{let{values:r}=e;return((e,t,n)=>{e[t]=Math.trunc(1e6*n%4294967296),e[t+1]=Math.trunc(1e6*n/4294967296)})(r,2*t,n)},St=(e,t,n)=>{let{values:r}=e;r[t]=n},xt=(e,t,n)=>{let{values:r}=e;r[t]=n},kt=(e,t,n)=>{let{values:r}=e;r[t]=n},It=(e,t,n)=>{let{values:r}=e;r[t]=n},Tt=(e,t,n)=>{const r=e.type.typeIdToChildIndex[e.typeIds[t]],i=e.children[r];Et.visit(i,e.valueOffsets[t],n)},Ot=(e,t,n)=>{const r=e.type.typeIdToChildIndex[e.typeIds[t]],i=e.children[r];Et.visit(i,t,n)},At=(e,t,n)=>{let{values:r}=e;r.set(n.subarray(0,2),2*t)},Ct=(e,t,n)=>{let{values:r}=e;r[t]=12*n[0]+n[1]%12};ut.prototype.visitBool=ct(((e,t,n)=>{let{offset:r,values:i}=e;const o=r+t;n?i[o>>3]|=1<<o%8:i[o>>3]&=~(1<<o%8)})),ut.prototype.visitInt=ct(ht),ut.prototype.visitInt8=ct(ht),ut.prototype.visitInt16=ct(ht),ut.prototype.visitInt32=ct(ht),ut.prototype.visitInt64=ct(ht),ut.prototype.visitUint8=ct(ht),ut.prototype.visitUint16=ct(ht),ut.prototype.visitUint32=ct(ht),ut.prototype.visitUint64=ct(ht),ut.prototype.visitFloat=ct(((e,t,n)=>{switch(e.type.precision){case r.HALF:return yt(e,t,n);case r.SINGLE:case r.DOUBLE:return pt(e,t,n)}})),ut.prototype.visitFloat16=ct(yt),ut.prototype.visitFloat32=ct(pt),ut.prototype.visitFloat64=ct(pt),ut.prototype.visitUtf8=ct(((e,t,n)=>{let{values:r,valueOffsets:i}=e;ft(r,i,t,K(n))})),ut.prototype.visitBinary=ct(((e,t,n)=>{let{values:r,valueOffsets:i}=e;return ft(r,i,t,n)})),ut.prototype.visitFixedSizeBinary=ct(((e,t,n)=>{let{stride:r,values:i}=e;i.set(n.subarray(0,r),r*t)})),ut.prototype.visitDate=ct(((e,t,n)=>{e.type.unit===i.DAY?mt(e,t,n):bt(e,t,n)})),ut.prototype.visitDateDay=ct(mt),ut.prototype.visitDateMillisecond=ct(bt),ut.prototype.visitTimestamp=ct(((e,t,n)=>{switch(e.type.unit){case o.SECOND:return gt(e,t,n);case o.MILLISECOND:return vt(e,t,n);case o.MICROSECOND:return wt(e,t,n);case o.NANOSECOND:return _t(e,t,n)}})),ut.prototype.visitTimestampSecond=ct(gt),ut.prototype.visitTimestampMillisecond=ct(vt),ut.prototype.visitTimestampMicrosecond=ct(wt),ut.prototype.visitTimestampNanosecond=ct(_t),ut.prototype.visitTime=ct(((e,t,n)=>{switch(e.type.unit){case o.SECOND:return St(e,t,n);case o.MILLISECOND:return xt(e,t,n);case o.MICROSECOND:return kt(e,t,n);case o.NANOSECOND:return It(e,t,n)}})),ut.prototype.visitTimeSecond=ct(St),ut.prototype.visitTimeMillisecond=ct(xt),ut.prototype.visitTimeMicrosecond=ct(kt),ut.prototype.visitTimeNanosecond=ct(It),ut.prototype.visitDecimal=ct(((e,t,n)=>{let{values:r,stride:i}=e;r.set(n.subarray(0,i),i*t)})),ut.prototype.visitList=ct(((e,t,n)=>{const r=e.children[0],i=e.valueOffsets,o=Et.getVisitFn(r);if(Array.isArray(n))for(let a=-1,s=i[t],l=i[t+1];s<l;)o(r,s++,n[++a]);else for(let a=-1,s=i[t],l=i[t+1];s<l;)o(r,s++,n.get(++a))})),ut.prototype.visitStruct=ct(((e,t,n)=>{const r=e.type.children.map((e=>Et.getVisitFn(e.type))),i=n instanceof Map?(o=t,a=n,(e,t,n,r)=>t&&e(t,o,a.get(n.name))):n instanceof Qn?((e,t)=>(n,r,i,o)=>r&&n(r,e,t.get(o)))(t,n):Array.isArray(n)?((e,t)=>(n,r,i,o)=>r&&n(r,e,t[o]))(t,n):((e,t)=>(n,r,i,o)=>r&&n(r,e,t[i.name]))(t,n);var o,a;e.type.children.forEach(((t,n)=>i(r[n],e.children[n],t,n)))})),ut.prototype.visitUnion=ct(((e,n,r)=>{e.type.mode===t.Dense?Tt(e,n,r):Ot(e,n,r)})),ut.prototype.visitDenseUnion=ct(Tt),ut.prototype.visitSparseUnion=ct(Ot),ut.prototype.visitDictionary=ct(((e,t,n)=>{var r;null===(r=e.dictionary)||void 0===r||r.set(e.values[t],n)})),ut.prototype.visitInterval=ct(((e,t,n)=>{e.type.unit===a.DAY_TIME?At(e,t,n):Ct(e,t,n)})),ut.prototype.visitIntervalDayTime=ct(At),ut.prototype.visitIntervalYearMonth=ct(Ct),ut.prototype.visitFixedSizeList=ct(((e,t,n)=>{const{stride:r}=e,i=e.children[0],o=Et.getVisitFn(i);if(Array.isArray(n))for(let a=-1,s=t*r;++a<r;)o(i,s+a,n[a]);else for(let a=-1,s=t*r;++a<r;)o(i,s+a,n.get(a))})),ut.prototype.visitMap=ct(((e,t,n)=>{const r=e.children[0],{valueOffsets:i}=e,o=Et.getVisitFn(r);let{[t]:a,[t+1]:s}=i;const l=n instanceof Map?n.entries():Object.entries(n);for(const u of l)if(o(r,a,u),++a>=s)break}));const Et=new ut,Nt=Symbol.for("parent"),Mt=Symbol.for("rowIndex");class Bt{constructor(e,t){return this[Nt]=e,this[Mt]=t,new Proxy(this,new Dt)}toArray(){return Object.values(this.toJSON())}toJSON(){const e=this[Mt],t=this[Nt],n=t.type.children,r={};for(let i=-1,o=n.length;++i<o;)r[n[i].name]=rn.visit(t.children[i],e);return r}toString(){return"{".concat([...this].map((e=>{let[t,n]=e;return"".concat(P(t),": ").concat(P(n))})).join(", "),"}")}[Symbol.for("nodejs.util.inspect.custom")](){return this.toString()}[Symbol.iterator](){return new Lt(this[Nt],this[Mt])}}class Lt{constructor(e,t){this.childIndex=0,this.children=e.children,this.rowIndex=t,this.childFields=e.type.children,this.numChildren=this.childFields.length}[Symbol.iterator](){return this}next(){const e=this.childIndex;return e<this.numChildren?(this.childIndex=e+1,{done:!1,value:[this.childFields[e].name,rn.visit(this.children[e],this.rowIndex)]}):{done:!0,value:null}}}Object.defineProperties(Bt.prototype,{[Symbol.toStringTag]:{enumerable:!1,configurable:!1,value:"Row"},[Nt]:{writable:!0,enumerable:!1,configurable:!1,value:null},[Mt]:{writable:!0,enumerable:!1,configurable:!1,value:-1}});class Dt{isExtensible(){return!1}deleteProperty(){return!1}preventExtensions(){return!0}ownKeys(e){return e[Nt].type.children.map((e=>e.name))}has(e,t){return-1!==e[Nt].type.children.findIndex((e=>e.name===t))}getOwnPropertyDescriptor(e,t){if(-1!==e[Nt].type.children.findIndex((e=>e.name===t)))return{writable:!0,enumerable:!0,configurable:!0}}get(e,t){if(Reflect.has(e,t))return e[t];const n=e[Nt].type.children.findIndex((e=>e.name===t));if(-1!==n){const r=rn.visit(e[Nt].children[n],e[Mt]);return Reflect.set(e,t,r),r}}set(e,t,n){const r=e[Nt].type.children.findIndex((e=>e.name===t));return-1!==r?(Et.visit(e[Nt].children[r],e[Mt],n),Reflect.set(e,t,n)):!(!Reflect.has(e,t)&&"symbol"!==typeof t)&&Reflect.set(e,t,n)}}class Ft extends rt{}function Pt(e){return(t,n)=>t.getValid(n)?e(t,n):null}const Rt=(e,t)=>4294967296*e[t+1]+(e[t]>>>0),zt=e=>new Date(e),Ut=(e,t,n)=>{if(n+1>=t.length)return null;const r=t[n],i=t[n+1];return e.subarray(r,i)},jt=(e,t)=>{let{values:n}=e;return((e,t)=>zt(((e,t)=>864e5*e[t])(e,t)))(n,t)},Vt=(e,t)=>{let{values:n}=e;return((e,t)=>zt(Rt(e,t)))(n,2*t)},Wt=(e,t)=>{let{stride:n,values:r}=e;return r[n*t]},$t=(e,t)=>{let{values:n}=e;return n[t]},Ht=(e,t)=>{let{values:n}=e;return 1e3*Rt(n,2*t)},Yt=(e,t)=>{let{values:n}=e;return Rt(n,2*t)},Kt=(e,t)=>{let{values:n}=e;return((e,t)=>e[t+1]/1e3*4294967296+(e[t]>>>0)/1e3)(n,2*t)},Qt=(e,t)=>{let{values:n}=e;return((e,t)=>e[t+1]/1e6*4294967296+(e[t]>>>0)/1e6)(n,2*t)},qt=(e,t)=>{let{values:n}=e;return n[t]},Gt=(e,t)=>{let{values:n}=e;return n[t]},Xt=(e,t)=>{let{values:n}=e;return n[t]},Jt=(e,t)=>{let{values:n}=e;return n[t]},Zt=(e,t)=>{const n=e.type.typeIdToChildIndex[e.typeIds[t]],r=e.children[n];return rn.visit(r,e.valueOffsets[t])},en=(e,t)=>{const n=e.type.typeIdToChildIndex[e.typeIds[t]],r=e.children[n];return rn.visit(r,t)},tn=(e,t)=>{let{values:n}=e;return n.subarray(2*t,2*(t+1))},nn=(e,t)=>{let{values:n}=e;const r=n[t],i=new Int32Array(2);return i[0]=Math.trunc(r/12),i[1]=Math.trunc(r%12),i};Ft.prototype.visitNull=Pt(((e,t)=>null)),Ft.prototype.visitBool=Pt(((e,t)=>{let{offset:n,values:r}=e;const i=n+t;return 0!==(r[i>>3]&1<<i%8)})),Ft.prototype.visitInt=Pt(((e,t)=>{let{values:n}=e;return n[t]})),Ft.prototype.visitInt8=Pt(Wt),Ft.prototype.visitInt16=Pt(Wt),Ft.prototype.visitInt32=Pt(Wt),Ft.prototype.visitInt64=Pt($t),Ft.prototype.visitUint8=Pt(Wt),Ft.prototype.visitUint16=Pt(Wt),Ft.prototype.visitUint32=Pt(Wt),Ft.prototype.visitUint64=Pt($t),Ft.prototype.visitFloat=Pt(((e,t)=>{let{type:n,values:i}=e;return n.precision!==r.HALF?i[t]:lt(i[t])})),Ft.prototype.visitFloat16=Pt(((e,t)=>{let{stride:n,values:r}=e;return lt(r[n*t])})),Ft.prototype.visitFloat32=Pt(Wt),Ft.prototype.visitFloat64=Pt(Wt),Ft.prototype.visitUtf8=Pt(((e,t)=>{let{values:n,valueOffsets:r}=e;const i=Ut(n,r,t);return null!==i?H(i):null})),Ft.prototype.visitBinary=Pt(((e,t)=>{let{values:n,valueOffsets:r}=e;return Ut(n,r,t)})),Ft.prototype.visitFixedSizeBinary=Pt(((e,t)=>{let{stride:n,values:r}=e;return r.subarray(n*t,n*(t+1))})),Ft.prototype.visitDate=Pt(((e,t)=>e.type.unit===i.DAY?jt(e,t):Vt(e,t))),Ft.prototype.visitDateDay=Pt(jt),Ft.prototype.visitDateMillisecond=Pt(Vt),Ft.prototype.visitTimestamp=Pt(((e,t)=>{switch(e.type.unit){case o.SECOND:return Ht(e,t);case o.MILLISECOND:return Yt(e,t);case o.MICROSECOND:return Kt(e,t);case o.NANOSECOND:return Qt(e,t)}})),Ft.prototype.visitTimestampSecond=Pt(Ht),Ft.prototype.visitTimestampMillisecond=Pt(Yt),Ft.prototype.visitTimestampMicrosecond=Pt(Kt),Ft.prototype.visitTimestampNanosecond=Pt(Qt),Ft.prototype.visitTime=Pt(((e,t)=>{switch(e.type.unit){case o.SECOND:return qt(e,t);case o.MILLISECOND:return Gt(e,t);case o.MICROSECOND:return Xt(e,t);case o.NANOSECOND:return Jt(e,t)}})),Ft.prototype.visitTimeSecond=Pt(qt),Ft.prototype.visitTimeMillisecond=Pt(Gt),Ft.prototype.visitTimeMicrosecond=Pt(Xt),Ft.prototype.visitTimeNanosecond=Pt(Jt),Ft.prototype.visitDecimal=Pt(((e,t)=>{let{values:n,stride:r}=e;return he.decimal(n.subarray(r*t,r*(t+1)))})),Ft.prototype.visitList=Pt(((e,t)=>{const{valueOffsets:n,stride:r,children:i}=e,{[t*r]:o,[t*r+1]:a}=n,s=i[0].slice(o,a-o);return new Qn([s])})),Ft.prototype.visitStruct=Pt(((e,t)=>new Bt(e,t))),Ft.prototype.visitUnion=Pt(((e,n)=>e.type.mode===t.Dense?Zt(e,n):en(e,n))),Ft.prototype.visitDenseUnion=Pt(Zt),Ft.prototype.visitSparseUnion=Pt(en),Ft.prototype.visitDictionary=Pt(((e,t)=>{var n;return null===(n=e.dictionary)||void 0===n?void 0:n.get(e.values[t])})),Ft.prototype.visitInterval=Pt(((e,t)=>e.type.unit===a.DAY_TIME?tn(e,t):nn(e,t))),Ft.prototype.visitIntervalDayTime=Pt(tn),Ft.prototype.visitIntervalYearMonth=Pt(nn),Ft.prototype.visitFixedSizeList=Pt(((e,t)=>{const{stride:n,children:r}=e,i=r[0].slice(t*n,n);return new Qn([i])})),Ft.prototype.visitMap=Pt(((e,t)=>{const{valueOffsets:n,children:r}=e,{[t]:i,[t+1]:o}=n,a=r[0];return new sn(a.slice(i,o-i))}));const rn=new Ft,on=Symbol.for("keys"),an=Symbol.for("vals");class sn{constructor(e){return this[on]=new Qn([e.children[0]]).memoize(),this[an]=e.children[1],new Proxy(this,new un)}[Symbol.iterator](){return new ln(this[on],this[an])}get size(){return this[on].length}toArray(){return Object.values(this.toJSON())}toJSON(){const e=this[on],t=this[an],n={};for(let r=-1,i=e.length;++r<i;)n[e.get(r)]=rn.visit(t,r);return n}toString(){return"{".concat([...this].map((e=>{let[t,n]=e;return"".concat(P(t),": ").concat(P(n))})).join(", "),"}")}[Symbol.for("nodejs.util.inspect.custom")](){return this.toString()}}class ln{constructor(e,t){this.keys=e,this.vals=t,this.keyIndex=0,this.numKeys=e.length}[Symbol.iterator](){return this}next(){const e=this.keyIndex;return e===this.numKeys?{done:!0,value:null}:(this.keyIndex++,{done:!1,value:[this.keys.get(e),rn.visit(this.vals,e)]})}}class un{isExtensible(){return!1}deleteProperty(){return!1}preventExtensions(){return!0}ownKeys(e){return e[on].toArray().map(String)}has(e,t){return e[on].includes(t)}getOwnPropertyDescriptor(e,t){if(-1!==e[on].indexOf(t))return{writable:!0,enumerable:!0,configurable:!0}}get(e,t){if(Reflect.has(e,t))return e[t];const n=e[on].indexOf(t);if(-1!==n){const r=rn.visit(Reflect.get(e,an),n);return Reflect.set(e,t,r),r}}set(e,t,n){const r=e[on].indexOf(t);return-1!==r?(Et.visit(Reflect.get(e,an),r,n),Reflect.set(e,t,n)):!!Reflect.has(e,t)&&Reflect.set(e,t,n)}}let cn;function dn(e,t,n,r){const{length:i=0}=e;let o="number"!==typeof t?0:t,a="number"!==typeof n?i:n;return o<0&&(o=(o%i+i)%i),a<0&&(a=(a%i+i)%i),a<o&&(cn=o,o=a,a=cn),a>i&&(a=i),r?r(e,o,a):[o,a]}Object.defineProperties(sn.prototype,{[Symbol.toStringTag]:{enumerable:!1,configurable:!1,value:"Row"},[on]:{writable:!0,enumerable:!1,configurable:!1,value:null},[an]:{writable:!0,enumerable:!1,configurable:!1,value:null}});const fn=e=>e!==e;function hn(e){if("object"!==typeof e||null===e)return fn(e)?fn:t=>t===e;if(e instanceof Date){const t=e.valueOf();return e=>e instanceof Date&&e.valueOf()===t}return ArrayBuffer.isView(e)?t=>!!t&&function(e,t){let n=0;const r=e.length;if(r!==t.length)return!1;if(r>0)do{if(e[n]!==t[n])return!1}while(++n<r);return!0}(e,t):e instanceof Map?function(e){let t=-1;const n=[];for(const r of e.values())n[++t]=hn(r);return pn(n)}(e):Array.isArray(e)?function(e){const t=[];for(let n=-1,r=e.length;++n<r;)t[n]=hn(e[n]);return pn(t)}(e):e instanceof Qn?function(e){const t=[];for(let n=-1,r=e.length;++n<r;)t[n]=hn(e.get(n));return pn(t)}(e):function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n=Object.keys(e);if(!t&&0===n.length)return()=>!1;const r=[];for(let i=-1,o=n.length;++i<o;)r[i]=hn(e[n[i]]);return pn(r,n)}(e,!0)}function pn(e,t){return n=>{if(!n||"object"!==typeof n)return!1;switch(n.constructor){case Array:return function(e,t){const n=e.length;if(t.length!==n)return!1;for(let r=-1;++r<n;)if(!e[r](t[r]))return!1;return!0}(e,n);case Map:return yn(e,n,n.keys());case sn:case Bt:case Object:case void 0:return yn(e,n,t||Object.keys(n))}return n instanceof Qn&&function(e,t){const n=e.length;if(t.length!==n)return!1;for(let r=-1;++r<n;)if(!e[r](t.get(r)))return!1;return!0}(e,n)}}function yn(e,t,n){const r=n[Symbol.iterator](),i=t instanceof Map?t.keys():Object.keys(t)[Symbol.iterator](),o=t instanceof Map?t.values():Object.values(t)[Symbol.iterator]();let a=0;const s=e.length;let l=o.next(),u=r.next(),c=i.next();for(;a<s&&!u.done&&!c.done&&!l.done&&(u.value===c.value&&e[a](l.value));++a,u=r.next(),c=i.next(),l=o.next());return!!(a===s&&u.done&&c.done&&l.done)||(r.return&&r.return(),i.return&&i.return(),o.return&&o.return(),!1)}class mn{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1?arguments[1]:void 0;this.numChunks=e,this.getChunkIterator=t,this.chunkIndex=0,this.chunkIterator=this.getChunkIterator(0)}next(){for(;this.chunkIndex<this.numChunks;){const e=this.chunkIterator.next();if(!e.done)return e;++this.chunkIndex<this.numChunks&&(this.chunkIterator=this.getChunkIterator(this.chunkIndex))}return{done:!0,value:null}}[Symbol.iterator](){return this}}function bn(e){return e.reduce(((e,t)=>e+t.nullCount),0)}function gn(e){return e.reduce(((e,t,n)=>(e[n+1]=e[n]+t.length,e)),new Uint32Array(e.length+1))}function vn(e,t,n,r){const i=[];for(let o=-1,a=e.length;++o<a;){const a=e[o],s=t[o],{length:l}=a;if(s>=r)break;if(n>=s+l)continue;if(s>=n&&s+l<=r){i.push(a);continue}const u=Math.max(0,n-s),c=Math.min(r-s,l);i.push(a.slice(u,c-u))}return 0===i.length&&i.push(e[0].slice(0,0)),i}function wn(e,t,n,r){let i=0,o=0,a=t.length-1;do{if(i>=a-1)return n<t[a]?r(e,i,n-t[i]):null;o=i+Math.trunc(.5*(a-i)),n<t[o]?a=o:i=o}while(i<a)}function _n(e,t){return e.getValid(t)}function Sn(e){function t(t,n,r){return e(t[n],r)}return function(e){return wn(this.data,this._offsets,e,t)}}function xn(e){let t;function n(n,r,i){return e(n[r],i,t)}return function(e,r){const i=this.data;t=r;const o=wn(i,this._offsets,e,n);return t=void 0,o}}function kn(e){let t;function n(n,r,i){let o=i,a=0,s=0;for(let l=r-1,u=n.length;++l<u;){const r=n[l];if(~(a=e(r,t,o)))return s+a;o=0,s+=r.length}return-1}return function(e,r){t=e;const i=this.data,o="number"!==typeof r?n(i,0,0):wn(i,this._offsets,r,n);return t=void 0,o}}function In(e,t,n,r){return 0!==(n&1<<r)}function Tn(e,t,n,r){return(n&1<<r)>>r}function On(e,t,n){const r=n.byteLength+7&-8;if(e>0||n.byteLength<r){const i=new Uint8Array(r);return i.set(e%8===0?n.subarray(e>>3):An(new Cn(n,e,t,null,In)).subarray(0,r)),i}return n}function An(e){const t=[];let n=0,r=0,i=0;for(const a of e)a&&(i|=1<<r),8===++r&&(t[n++]=i,i=r=0);(0===n||r>0)&&(t[n++]=i);const o=new Uint8Array(t.length+7&-8);return o.set(t),o}class Cn{constructor(e,t,n,r,i){this.bytes=e,this.length=n,this.context=r,this.get=i,this.bit=t%8,this.byteIndex=t>>3,this.byte=e[this.byteIndex++],this.index=0}next(){return this.index<this.length?(8===this.bit&&(this.bit=0,this.byte=this.bytes[this.byteIndex++]),{value:this.get(this.context,this.index++,this.byte,this.bit++)}):{done:!0,value:null}}[Symbol.iterator](){return this}}function En(e,t,n){if(n-t<=0)return 0;if(n-t<8){let r=0;for(const i of new Cn(e,t,n-t,e,Tn))r+=i;return r}const r=n>>3<<3,i=t+(t%8===0?0:8-t%8);return En(e,t,i)+En(e,r,n)+function(e,t,n){let r=0,i=Math.trunc(t);const o=new DataView(e.buffer,e.byteOffset,e.byteLength),a=void 0===n?e.byteLength:i+n;for(;a-i>=4;)r+=Nn(o.getUint32(i)),i+=4;for(;a-i>=2;)r+=Nn(o.getUint16(i)),i+=2;for(;a-i>=1;)r+=Nn(o.getUint8(i)),i+=1;return r}(e,i>>3,r-i>>3)}function Nn(e){let t=Math.trunc(e);return t-=t>>>1&1431655765,t=(*********&t)+(t>>>2&*********),16843009*(t+(t>>>4)&*********)>>>24}class Mn extends rt{}function Bn(e,t,n){if(void 0===t)return-1;if(null===t)return function(e,t){const{nullBitmap:n}=e;if(!n||e.nullCount<=0)return-1;let r=0;for(const i of new Cn(n,e.offset+(t||0),e.length,n,In)){if(!i)return r;++r}return-1}(e,n);const r=rn.getVisitFn(e),i=hn(t);for(let o=(n||0)-1,a=e.length;++o<a;)if(i(r(e,o)))return o;return-1}function Ln(e,t,n){const r=rn.getVisitFn(e),i=hn(t);for(let o=(n||0)-1,a=e.length;++o<a;)if(i(r(e,o)))return o;return-1}Mn.prototype.visitNull=function(e,t){return null===t&&e.length>0?0:-1},Mn.prototype.visitBool=Bn,Mn.prototype.visitInt=Bn,Mn.prototype.visitInt8=Bn,Mn.prototype.visitInt16=Bn,Mn.prototype.visitInt32=Bn,Mn.prototype.visitInt64=Bn,Mn.prototype.visitUint8=Bn,Mn.prototype.visitUint16=Bn,Mn.prototype.visitUint32=Bn,Mn.prototype.visitUint64=Bn,Mn.prototype.visitFloat=Bn,Mn.prototype.visitFloat16=Bn,Mn.prototype.visitFloat32=Bn,Mn.prototype.visitFloat64=Bn,Mn.prototype.visitUtf8=Bn,Mn.prototype.visitBinary=Bn,Mn.prototype.visitFixedSizeBinary=Bn,Mn.prototype.visitDate=Bn,Mn.prototype.visitDateDay=Bn,Mn.prototype.visitDateMillisecond=Bn,Mn.prototype.visitTimestamp=Bn,Mn.prototype.visitTimestampSecond=Bn,Mn.prototype.visitTimestampMillisecond=Bn,Mn.prototype.visitTimestampMicrosecond=Bn,Mn.prototype.visitTimestampNanosecond=Bn,Mn.prototype.visitTime=Bn,Mn.prototype.visitTimeSecond=Bn,Mn.prototype.visitTimeMillisecond=Bn,Mn.prototype.visitTimeMicrosecond=Bn,Mn.prototype.visitTimeNanosecond=Bn,Mn.prototype.visitDecimal=Bn,Mn.prototype.visitList=Bn,Mn.prototype.visitStruct=Bn,Mn.prototype.visitUnion=Bn,Mn.prototype.visitDenseUnion=Ln,Mn.prototype.visitSparseUnion=Ln,Mn.prototype.visitDictionary=Bn,Mn.prototype.visitInterval=Bn,Mn.prototype.visitIntervalDayTime=Bn,Mn.prototype.visitIntervalYearMonth=Bn,Mn.prototype.visitFixedSizeList=Bn,Mn.prototype.visitMap=Bn;const Dn=new Mn;class Fn extends rt{}function Pn(e){const{type:t}=e;if(0===e.nullCount&&1===e.stride&&(t.typeId===l.Timestamp||t instanceof Fe&&64!==t.bitWidth||t instanceof $e&&64!==t.bitWidth||t instanceof Re&&t.precision!==r.HALF))return new mn(e.data.length,(t=>{const n=e.data[t];return n.values.subarray(0,n.length)[Symbol.iterator]()}));let n=0;return new mn(e.data.length,(t=>{const r=e.data[t].length,i=e.slice(n,n+r);return n+=r,new Rn(i)}))}class Rn{constructor(e){this.vector=e,this.index=0}next(){return this.index<this.vector.length?{value:this.vector.get(this.index++)}:{done:!0,value:null}}[Symbol.iterator](){return this}}Fn.prototype.visitNull=Pn,Fn.prototype.visitBool=Pn,Fn.prototype.visitInt=Pn,Fn.prototype.visitInt8=Pn,Fn.prototype.visitInt16=Pn,Fn.prototype.visitInt32=Pn,Fn.prototype.visitInt64=Pn,Fn.prototype.visitUint8=Pn,Fn.prototype.visitUint16=Pn,Fn.prototype.visitUint32=Pn,Fn.prototype.visitUint64=Pn,Fn.prototype.visitFloat=Pn,Fn.prototype.visitFloat16=Pn,Fn.prototype.visitFloat32=Pn,Fn.prototype.visitFloat64=Pn,Fn.prototype.visitUtf8=Pn,Fn.prototype.visitBinary=Pn,Fn.prototype.visitFixedSizeBinary=Pn,Fn.prototype.visitDate=Pn,Fn.prototype.visitDateDay=Pn,Fn.prototype.visitDateMillisecond=Pn,Fn.prototype.visitTimestamp=Pn,Fn.prototype.visitTimestampSecond=Pn,Fn.prototype.visitTimestampMillisecond=Pn,Fn.prototype.visitTimestampMicrosecond=Pn,Fn.prototype.visitTimestampNanosecond=Pn,Fn.prototype.visitTime=Pn,Fn.prototype.visitTimeSecond=Pn,Fn.prototype.visitTimeMillisecond=Pn,Fn.prototype.visitTimeMicrosecond=Pn,Fn.prototype.visitTimeNanosecond=Pn,Fn.prototype.visitDecimal=Pn,Fn.prototype.visitList=Pn,Fn.prototype.visitStruct=Pn,Fn.prototype.visitUnion=Pn,Fn.prototype.visitDenseUnion=Pn,Fn.prototype.visitSparseUnion=Pn,Fn.prototype.visitDictionary=Pn,Fn.prototype.visitInterval=Pn,Fn.prototype.visitIntervalDayTime=Pn,Fn.prototype.visitIntervalYearMonth=Pn,Fn.prototype.visitFixedSizeList=Pn,Fn.prototype.visitMap=Pn;const zn=new Fn,Un=(e,t)=>e+t;class jn extends rt{visitNull(e,t){return 0}visitInt(e,t){return e.type.bitWidth/8}visitFloat(e,t){return e.type.ArrayType.BYTES_PER_ELEMENT}visitBool(e,t){return 1/8}visitDecimal(e,t){return e.type.bitWidth/8}visitDate(e,t){return 4*(e.type.unit+1)}visitTime(e,t){return e.type.bitWidth/8}visitTimestamp(e,t){return e.type.unit===o.SECOND?4:8}visitInterval(e,t){return 4*(e.type.unit+1)}visitStruct(e,t){return e.children.reduce(((e,n)=>e+$n.visit(n,t)),0)}visitFixedSizeBinary(e,t){return e.type.byteWidth}visitMap(e,t){return 8+e.children.reduce(((e,n)=>e+$n.visit(n,t)),0)}visitDictionary(e,t){var n;return e.type.indices.bitWidth/8+((null===(n=e.dictionary)||void 0===n?void 0:n.getByteLength(e.values[t]))||0)}}const Vn=(e,t)=>{let{type:n,children:r,typeIds:i,valueOffsets:o}=e;const a=n.typeIdToChildIndex[i[t]];return 8+$n.visit(r[a],o[t])},Wn=(e,t)=>{let{children:n}=e;return 4+$n.visitMany(n,n.map((()=>t))).reduce(Un,0)};jn.prototype.visitUtf8=(e,t)=>{let{valueOffsets:n}=e;return n[t+1]-n[t]+8},jn.prototype.visitBinary=(e,t)=>{let{valueOffsets:n}=e;return n[t+1]-n[t]+8},jn.prototype.visitList=(e,t)=>{let{valueOffsets:n,stride:r,children:i}=e;const o=i[0],{[t*r]:a}=n,{[t*r+1]:s}=n,l=$n.getVisitFn(o.type),u=o.slice(a,s-a);let c=8;for(let d=-1,f=s-a;++d<f;)c+=l(u,d);return c},jn.prototype.visitFixedSizeList=(e,t)=>{let{stride:n,children:r}=e;const i=r[0],o=i.slice(t*n,n),a=$n.getVisitFn(i.type);let s=0;for(let l=-1,u=o.length;++l<u;)s+=a(o,l);return s},jn.prototype.visitUnion=(e,n)=>e.type.mode===t.Dense?Vn(e,n):Wn(e,n),jn.prototype.visitDenseUnion=Vn,jn.prototype.visitSparseUnion=Wn;const $n=new jn;var Hn;const Yn={},Kn={};class Qn{constructor(e){var t,n,r;const i=e[0]instanceof Qn?e.flatMap((e=>e.data)):e;if(0===i.length||i.some((e=>!(e instanceof Gn))))throw new TypeError("Vector constructor expects an Array of Data instances.");const o=null===(t=i[0])||void 0===t?void 0:t.type;switch(i.length){case 0:this._offsets=[0];break;case 1:{const{get:e,set:t,indexOf:n,byteLength:r}=Yn[o.typeId],a=i[0];this.isValid=e=>_n(a,e),this.get=t=>e(a,t),this.set=(e,n)=>t(a,e,n),this.indexOf=e=>n(a,e),this.getByteLength=e=>r(a,e),this._offsets=[0,a.length];break}default:Object.setPrototypeOf(this,Kn[o.typeId]),this._offsets=gn(i)}this.data=i,this.type=o,this.stride=nt(o),this.numChildren=null!==(r=null===(n=o.children)||void 0===n?void 0:n.length)&&void 0!==r?r:0,this.length=this._offsets[this._offsets.length-1]}get byteLength(){return-1===this._byteLength&&(this._byteLength=this.data.reduce(((e,t)=>e+t.byteLength),0)),this._byteLength}get nullCount(){return-1===this._nullCount&&(this._nullCount=bn(this.data)),this._nullCount}get ArrayType(){return this.type.ArrayType}get[Symbol.toStringTag](){return"".concat(this.VectorName,"<").concat(this.type[Symbol.toStringTag],">")}get VectorName(){return"".concat(l[this.type.typeId],"Vector")}isValid(e){return!1}get(e){return null}set(e,t){}indexOf(e,t){return-1}includes(e,t){return this.indexOf(e,t)>0}getByteLength(e){return 0}[Symbol.iterator](){return zn.visit(this)}concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return new Qn(this.data.concat(t.flatMap((e=>e.data)).flat(Number.POSITIVE_INFINITY)))}slice(e,t){return new Qn(dn(this,e,t,((e,t,n)=>{let{data:r,_offsets:i}=e;return vn(r,i,t,n)})))}toJSON(){return[...this]}toArray(){const{type:e,data:t,length:n,stride:r,ArrayType:i}=this;switch(e.typeId){case l.Int:case l.Float:case l.Decimal:case l.Time:case l.Timestamp:switch(t.length){case 0:return new i;case 1:return t[0].values.subarray(0,n*r);default:return t.reduce(((e,t)=>{let{values:n,length:i}=t;return e.array.set(n.subarray(0,i*r),e.offset),e.offset+=i*r,e}),{array:new i(n*r),offset:0}).array}}return[...this]}toString(){return"[".concat([...this].join(","),"]")}getChild(e){var t;return this.getChildAt(null===(t=this.type.children)||void 0===t?void 0:t.findIndex((t=>t.name===e)))}getChildAt(e){return e>-1&&e<this.numChildren?new Qn(this.data.map((t=>{let{children:n}=t;return n[e]}))):null}get isMemoized(){return!!Le.isDictionary(this.type)&&this.data[0].dictionary.isMemoized}memoize(){if(Le.isDictionary(this.type)){const e=new qn(this.data[0].dictionary),t=this.data.map((t=>{const n=t.clone();return n.dictionary=e,n}));return new Qn(t)}return new qn(this)}unmemoize(){if(Le.isDictionary(this.type)&&this.isMemoized){const e=this.data[0].dictionary.unmemoize(),t=this.data.map((t=>{const n=t.clone();return n.dictionary=e,n}));return new Qn(t)}return this}}Hn=Symbol.toStringTag,Qn[Hn]=(e=>{e.type=Le.prototype,e.data=[],e.length=0,e.stride=1,e.numChildren=0,e._nullCount=-1,e._byteLength=-1,e._offsets=new Uint32Array([0]),e[Symbol.isConcatSpreadable]=!0;const t=Object.keys(l).map((e=>l[e])).filter((e=>"number"===typeof e&&e!==l.NONE));for(const n of t){const t=rn.getVisitFnByTypeId(n),r=Et.getVisitFnByTypeId(n),i=Dn.getVisitFnByTypeId(n),o=$n.getVisitFnByTypeId(n);Yn[n]={get:t,set:r,indexOf:i,byteLength:o},Kn[n]=Object.create(e,{isValid:{value:Sn(_n)},get:{value:Sn(rn.getVisitFnByTypeId(n))},set:{value:xn(Et.getVisitFnByTypeId(n))},indexOf:{value:kn(Dn.getVisitFnByTypeId(n))},getByteLength:{value:Sn($n.getVisitFnByTypeId(n))}})}return"Vector"})(Qn.prototype);class qn extends Qn{constructor(e){super(e.data);const t=this.get,n=this.set,r=this.slice,i=new Array(this.length);Object.defineProperty(this,"get",{value(e){const n=i[e];if(void 0!==n)return n;const r=t.call(this,e);return i[e]=r,r}}),Object.defineProperty(this,"set",{value(e,t){n.call(this,e,t),i[e]=t}}),Object.defineProperty(this,"slice",{value:(e,t)=>new qn(r.call(this,e,t))}),Object.defineProperty(this,"isMemoized",{value:!0}),Object.defineProperty(this,"unmemoize",{value:()=>new Qn(this.data)}),Object.defineProperty(this,"memoize",{value:()=>this})}}class Gn{constructor(e,t,n,r,i){let o,a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:[],s=arguments.length>6?arguments[6]:void 0;this.type=e,this.children=a,this.dictionary=s,this.offset=Math.floor(Math.max(t||0,0)),this.length=Math.floor(Math.max(n||0,0)),this._nullCount=Math.floor(Math.max(r||0,-1)),i instanceof Gn?(this.stride=i.stride,this.values=i.values,this.typeIds=i.typeIds,this.nullBitmap=i.nullBitmap,this.valueOffsets=i.valueOffsets):(this.stride=nt(e),i&&((o=i[0])&&(this.valueOffsets=o),(o=i[1])&&(this.values=o),(o=i[2])&&(this.nullBitmap=o),(o=i[3])&&(this.typeIds=o))),this.nullable=0!==this._nullCount&&this.nullBitmap&&this.nullBitmap.byteLength>0}get typeId(){return this.type.typeId}get ArrayType(){return this.type.ArrayType}get buffers(){return[this.valueOffsets,this.values,this.nullBitmap,this.typeIds]}get byteLength(){let e=0;const{valueOffsets:t,values:n,nullBitmap:r,typeIds:i}=this;return t&&(e+=t.byteLength),n&&(e+=n.byteLength),r&&(e+=r.byteLength),i&&(e+=i.byteLength),this.children.reduce(((e,t)=>e+t.byteLength),e)}get nullCount(){let e,t=this._nullCount;return t<=-1&&(e=this.nullBitmap)&&(this._nullCount=t=this.length-En(e,this.offset,this.offset+this.length)),t}getValid(e){if(this.nullable&&this.nullCount>0){const t=this.offset+e;return 0!==(this.nullBitmap[t>>3]&1<<t%8)}return!0}setValid(e,t){if(!this.nullable)return t;if(!this.nullBitmap||this.nullBitmap.byteLength<=e>>3){const{nullBitmap:e}=this._changeLengthAndBackfillNullBitmap(this.length);Object.assign(this,{nullBitmap:e,_nullCount:0})}const{nullBitmap:n,offset:r}=this,i=r+e>>3,o=(r+e)%8,a=n[i]>>o&1;return t?0===a&&(n[i]|=1<<o,this._nullCount=this.nullCount+1):1===a&&(n[i]&=~(1<<o),this._nullCount=this.nullCount-1),t}clone(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.type,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.offset,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.length,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:this._nullCount,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:this,o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:this.children;return new Gn(e,t,n,r,i,o,this.dictionary)}slice(e,t){const{stride:n,typeId:r,children:i}=this,o=+(0===this._nullCount)-1,a=16===r?n:1,s=this._sliceBuffers(e,t,n,r);return this.clone(this.type,this.offset+e,t,o,s,0===i.length||this.valueOffsets?i:this._sliceChildren(i,a*e,a*t))}_changeLengthAndBackfillNullBitmap(e){if(this.typeId===l.Null)return this.clone(this.type,0,e,0);const{length:t,nullCount:n}=this,r=new Uint8Array((e+63&-64)>>3).fill(255,0,t>>3);r[t>>3]=(1<<t-(-8&t))-1,n>0&&r.set(On(this.offset,t,this.nullBitmap),0);const i=this.buffers;return i[u.VALIDITY]=r,this.clone(this.type,0,e,n+(e-t),i)}_sliceBuffers(e,t,n,r){let i;const{buffers:o}=this;return(i=o[u.TYPE])&&(o[u.TYPE]=i.subarray(e,e+t)),(i=o[u.OFFSET])&&(o[u.OFFSET]=i.subarray(e,e+t+1))||(i=o[u.DATA])&&(o[u.DATA]=6===r?i:i.subarray(n*e,n*(e+t))),o}_sliceChildren(e,t,n){return e.map((e=>e.slice(t,n)))}}Gn.prototype.children=Object.freeze([]);class Xn extends rt{visit(e){return this.getVisitFn(e.type).call(this,e)}visitNull(e){const{type:t,offset:n=0,length:r=0}=e;return new Gn(t,n,r,0)}visitBool(e){const{type:t,offset:n=0}=e,r=Z(e.nullBitmap),i=X(t.ArrayType,e.data),{length:o=i.length>>3,nullCount:a=(e.nullBitmap?-1:0)}=e;return new Gn(t,n,o,a,[void 0,i,r])}visitInt(e){const{type:t,offset:n=0}=e,r=Z(e.nullBitmap),i=X(t.ArrayType,e.data),{length:o=i.length,nullCount:a=(e.nullBitmap?-1:0)}=e;return new Gn(t,n,o,a,[void 0,i,r])}visitFloat(e){const{type:t,offset:n=0}=e,r=Z(e.nullBitmap),i=X(t.ArrayType,e.data),{length:o=i.length,nullCount:a=(e.nullBitmap?-1:0)}=e;return new Gn(t,n,o,a,[void 0,i,r])}visitUtf8(e){const{type:t,offset:n=0}=e,r=Z(e.data),i=Z(e.nullBitmap),o=J(e.valueOffsets),{length:a=o.length-1,nullCount:s=(e.nullBitmap?-1:0)}=e;return new Gn(t,n,a,s,[o,r,i])}visitBinary(e){const{type:t,offset:n=0}=e,r=Z(e.data),i=Z(e.nullBitmap),o=J(e.valueOffsets),{length:a=o.length-1,nullCount:s=(e.nullBitmap?-1:0)}=e;return new Gn(t,n,a,s,[o,r,i])}visitFixedSizeBinary(e){const{type:t,offset:n=0}=e,r=Z(e.nullBitmap),i=X(t.ArrayType,e.data),{length:o=i.length/nt(t),nullCount:a=(e.nullBitmap?-1:0)}=e;return new Gn(t,n,o,a,[void 0,i,r])}visitDate(e){const{type:t,offset:n=0}=e,r=Z(e.nullBitmap),i=X(t.ArrayType,e.data),{length:o=i.length/nt(t),nullCount:a=(e.nullBitmap?-1:0)}=e;return new Gn(t,n,o,a,[void 0,i,r])}visitTimestamp(e){const{type:t,offset:n=0}=e,r=Z(e.nullBitmap),i=X(t.ArrayType,e.data),{length:o=i.length/nt(t),nullCount:a=(e.nullBitmap?-1:0)}=e;return new Gn(t,n,o,a,[void 0,i,r])}visitTime(e){const{type:t,offset:n=0}=e,r=Z(e.nullBitmap),i=X(t.ArrayType,e.data),{length:o=i.length/nt(t),nullCount:a=(e.nullBitmap?-1:0)}=e;return new Gn(t,n,o,a,[void 0,i,r])}visitDecimal(e){const{type:t,offset:n=0}=e,r=Z(e.nullBitmap),i=X(t.ArrayType,e.data),{length:o=i.length/nt(t),nullCount:a=(e.nullBitmap?-1:0)}=e;return new Gn(t,n,o,a,[void 0,i,r])}visitList(e){const{type:t,offset:n=0,child:r}=e,i=Z(e.nullBitmap),o=J(e.valueOffsets),{length:a=o.length-1,nullCount:s=(e.nullBitmap?-1:0)}=e;return new Gn(t,n,a,s,[o,void 0,i],[r])}visitStruct(e){const{type:t,offset:n=0,children:r=[]}=e,i=Z(e.nullBitmap),{length:o=r.reduce(((e,t)=>{let{length:n}=t;return Math.max(e,n)}),0),nullCount:a=(e.nullBitmap?-1:0)}=e;return new Gn(t,n,o,a,[void 0,void 0,i],r)}visitUnion(e){const{type:t,offset:n=0,children:r=[]}=e,i=Z(e.nullBitmap),o=X(t.ArrayType,e.typeIds),{length:a=o.length,nullCount:s=(e.nullBitmap?-1:0)}=e;if(Le.isSparseUnion(t))return new Gn(t,n,a,s,[void 0,void 0,i,o],r);const l=J(e.valueOffsets);return new Gn(t,n,a,s,[l,void 0,i,o],r)}visitDictionary(e){const{type:t,offset:n=0}=e,r=Z(e.nullBitmap),i=X(t.indices.ArrayType,e.data),{dictionary:o=new Qn([(new Xn).visit({type:t.dictionary})])}=e,{length:a=i.length,nullCount:s=(e.nullBitmap?-1:0)}=e;return new Gn(t,n,a,s,[void 0,i,r],[],o)}visitInterval(e){const{type:t,offset:n=0}=e,r=Z(e.nullBitmap),i=X(t.ArrayType,e.data),{length:o=i.length/nt(t),nullCount:a=(e.nullBitmap?-1:0)}=e;return new Gn(t,n,o,a,[void 0,i,r])}visitFixedSizeList(e){const{type:t,offset:n=0,child:r=(new Xn).visit({type:t.valueType})}=e,i=Z(e.nullBitmap),{length:o=r.length/nt(t),nullCount:a=(e.nullBitmap?-1:0)}=e;return new Gn(t,n,o,a,[void 0,void 0,i],[r])}visitMap(e){const{type:t,offset:n=0,child:r=(new Xn).visit({type:t.childType})}=e,i=Z(e.nullBitmap),o=J(e.valueOffsets),{length:a=o.length-1,nullCount:s=(e.nullBitmap?-1:0)}=e;return new Gn(t,n,a,s,[o,void 0,i],[r])}}function Jn(e){return(new Xn).visit(e)}class Zn{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0;this.fields=e||[],this.metadata=t||new Map,n||(n=nr(e)),this.dictionaries=n}get[Symbol.toStringTag](){return"Schema"}get names(){return this.fields.map((e=>e.name))}toString(){return"Schema<{ ".concat(this.fields.map(((e,t)=>"".concat(t,": ").concat(e))).join(", ")," }>")}select(e){const t=new Set(e),n=this.fields.filter((e=>t.has(e.name)));return new Zn(n,this.metadata)}selectAt(e){const t=e.map((e=>this.fields[e])).filter(Boolean);return new Zn(t,this.metadata)}assign(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];const r=t[0]instanceof Zn?t[0]:Array.isArray(t[0])?new Zn(t[0]):new Zn(t),i=[...this.fields],o=tr(tr(new Map,this.metadata),r.metadata),a=r.fields.filter((e=>{const t=i.findIndex((t=>t.name===e.name));return!~t||(i[t]=e.clone({metadata:tr(tr(new Map,i[t].metadata),e.metadata)}))&&!1})),s=nr(a,new Map);return new Zn([...i,...a],o,new Map([...this.dictionaries,...s]))}}Zn.prototype.fields=null,Zn.prototype.metadata=null,Zn.prototype.dictionaries=null;class er{constructor(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3?arguments[3]:void 0;this.name=e,this.type=t,this.nullable=n,this.metadata=r||new Map}static new(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,i,o,a]=t;return t[0]&&"object"===typeof t[0]&&(({name:r}=t[0]),void 0===i&&(i=t[0].type),void 0===o&&(o=t[0].nullable),void 0===a&&(a=t[0].metadata)),new er("".concat(r),i,o,a)}get typeId(){return this.type.typeId}get[Symbol.toStringTag](){return"Field"}toString(){return"".concat(this.name,": ").concat(this.type)}clone(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,i,o,a]=t;return t[0]&&"object"===typeof t[0]?({name:r=this.name,type:i=this.type,nullable:o=this.nullable,metadata:a=this.metadata}=t[0]):[r=this.name,i=this.type,o=this.nullable,a=this.metadata]=t,er.new(r,i,o,a)}}function tr(e,t){return new Map([...e||new Map,...t||new Map])}function nr(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Map;for(let n=-1,r=e.length;++n<r;){const r=e[n].type;if(Le.isDictionary(r))if(t.has(r.id)){if(t.get(r.id)!==r.dictionary)throw new Error("Cannot create Schema containing two different dictionaries with the same Id")}else t.set(r.id,r.dictionary);r.children&&r.children.length>0&&nr(r.children,t)}return t}er.prototype.type=null,er.prototype.name=null,er.prototype.nullable=null,er.prototype.metadata=null;class rr extends rt{compareSchemas(e,t){return e===t||t instanceof e.constructor&&this.compareManyFields(e.fields,t.fields)}compareManyFields(e,t){return e===t||Array.isArray(e)&&Array.isArray(t)&&e.length===t.length&&e.every(((e,n)=>this.compareFields(e,t[n])))}compareFields(e,t){return e===t||t instanceof e.constructor&&e.name===t.name&&e.nullable===t.nullable&&this.visit(e.type,t.type)}}function ir(e,t){return t instanceof e.constructor}function or(e,t){return e===t||ir(e,t)}function ar(e,t){return e===t||ir(e,t)&&e.bitWidth===t.bitWidth&&e.isSigned===t.isSigned}function sr(e,t){return e===t||ir(e,t)&&e.precision===t.precision}function lr(e,t){return e===t||ir(e,t)&&e.unit===t.unit}function ur(e,t){return e===t||ir(e,t)&&e.unit===t.unit&&e.timezone===t.timezone}function cr(e,t){return e===t||ir(e,t)&&e.unit===t.unit&&e.bitWidth===t.bitWidth}function dr(e,t){return e===t||ir(e,t)&&e.mode===t.mode&&e.typeIds.every(((e,n)=>e===t.typeIds[n]))&&hr.compareManyFields(e.children,t.children)}function fr(e,t){return e===t||ir(e,t)&&e.unit===t.unit}rr.prototype.visitNull=or,rr.prototype.visitBool=or,rr.prototype.visitInt=ar,rr.prototype.visitInt8=ar,rr.prototype.visitInt16=ar,rr.prototype.visitInt32=ar,rr.prototype.visitInt64=ar,rr.prototype.visitUint8=ar,rr.prototype.visitUint16=ar,rr.prototype.visitUint32=ar,rr.prototype.visitUint64=ar,rr.prototype.visitFloat=sr,rr.prototype.visitFloat16=sr,rr.prototype.visitFloat32=sr,rr.prototype.visitFloat64=sr,rr.prototype.visitUtf8=or,rr.prototype.visitBinary=or,rr.prototype.visitFixedSizeBinary=function(e,t){return e===t||ir(e,t)&&e.byteWidth===t.byteWidth},rr.prototype.visitDate=lr,rr.prototype.visitDateDay=lr,rr.prototype.visitDateMillisecond=lr,rr.prototype.visitTimestamp=ur,rr.prototype.visitTimestampSecond=ur,rr.prototype.visitTimestampMillisecond=ur,rr.prototype.visitTimestampMicrosecond=ur,rr.prototype.visitTimestampNanosecond=ur,rr.prototype.visitTime=cr,rr.prototype.visitTimeSecond=cr,rr.prototype.visitTimeMillisecond=cr,rr.prototype.visitTimeMicrosecond=cr,rr.prototype.visitTimeNanosecond=cr,rr.prototype.visitDecimal=or,rr.prototype.visitList=function(e,t){return e===t||ir(e,t)&&e.children.length===t.children.length&&hr.compareManyFields(e.children,t.children)},rr.prototype.visitStruct=function(e,t){return e===t||ir(e,t)&&e.children.length===t.children.length&&hr.compareManyFields(e.children,t.children)},rr.prototype.visitUnion=dr,rr.prototype.visitDenseUnion=dr,rr.prototype.visitSparseUnion=dr,rr.prototype.visitDictionary=function(e,t){return e===t||ir(e,t)&&e.id===t.id&&e.isOrdered===t.isOrdered&&hr.visit(e.indices,t.indices)&&hr.visit(e.dictionary,t.dictionary)},rr.prototype.visitInterval=fr,rr.prototype.visitIntervalDayTime=fr,rr.prototype.visitIntervalYearMonth=fr,rr.prototype.visitFixedSizeList=function(e,t){return e===t||ir(e,t)&&e.listSize===t.listSize&&e.children.length===t.children.length&&hr.compareManyFields(e.children,t.children)},rr.prototype.visitMap=function(e,t){return e===t||ir(e,t)&&e.keysSorted===t.keysSorted&&e.children.length===t.children.length&&hr.compareManyFields(e.children,t.children)};const hr=new rr;function pr(e,t){return hr.compareSchemas(e,t)}var yr,mr;class br{constructor(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];switch(t.length){case 2:if([this.schema]=t,!(this.schema instanceof Zn))throw new TypeError("RecordBatch constructor expects a [Schema, Data] pair.");if([,this.data=Jn({nullCount:0,type:new Qe(this.schema.fields),children:this.schema.fields.map((e=>Jn({type:e.type,nullCount:0})))})]=t,!(this.data instanceof Gn))throw new TypeError("RecordBatch constructor expects a [Schema, Data] pair.");[this.schema,this.data]=gr(this.schema,this.data.children);break;case 1:{const[e]=t,{fields:n,children:r,length:i}=Object.keys(e).reduce(((t,n,r)=>(t.children[r]=e[n],t.length=Math.max(t.length,e[n].length),t.fields[r]=er.new({name:n,type:e[n].type,nullable:!0}),t)),{length:0,fields:new Array,children:new Array}),o=new Zn(n),a=Jn({type:new Qe(n),length:i,children:r,nullCount:0});[this.schema,this.data]=gr(o,a.children,i);break}default:throw new TypeError("RecordBatch constructor expects an Object mapping names to child Data, or a [Schema, Data] pair.")}}get dictionaries(){return this._dictionaries||(this._dictionaries=vr(this.schema.fields,this.data.children))}get numCols(){return this.schema.fields.length}get numRows(){return this.data.length}get nullCount(){return this.data.nullCount}isValid(e){return this.data.getValid(e)}get(e){return rn.visit(this.data,e)}set(e,t){return Et.visit(this.data,e,t)}indexOf(e,t){return Dn.visit(this.data,e,t)}getByteLength(e){return $n.visit(this.data,e)}[Symbol.iterator](){return zn.visit(new Qn([this.data]))}toArray(){return[...this]}concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return new xr(this.schema,[this,...t])}slice(e,t){const[n]=new Qn([this.data]).slice(e,t).data;return new br(this.schema,n)}getChild(e){var t;return this.getChildAt(null===(t=this.schema.fields)||void 0===t?void 0:t.findIndex((t=>t.name===e)))}getChildAt(e){return e>-1&&e<this.schema.fields.length?new Qn([this.data.children[e]]):null}setChild(e,t){var n;return this.setChildAt(null===(n=this.schema.fields)||void 0===n?void 0:n.findIndex((t=>t.name===e)),t)}setChildAt(e,t){let n=this.schema,r=this.data;if(e>-1&&e<this.numCols){t||(t=new Qn([Jn({type:new De,length:this.numRows})]));const i=n.fields.slice(),o=r.children.slice(),a=i[e].clone({type:t.type});[i[e],o[e]]=[a,t.data[0]],n=new Zn(i,new Map(this.schema.metadata)),r=Jn({type:new Qe(i),children:o})}return new br(n,r)}select(e){const t=this.schema.select(e),n=new Qe(t.fields),r=[];for(const i of e){const e=this.schema.fields.findIndex((e=>e.name===i));~e&&(r[e]=this.data.children[e])}return new br(t,Jn({type:n,length:this.numRows,children:r}))}selectAt(e){const t=this.schema.selectAt(e),n=e.map((e=>this.data.children[e])).filter(Boolean),r=Jn({type:new Qe(t.fields),length:this.numRows,children:n});return new br(t,r)}}function gr(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t.reduce(((e,t)=>Math.max(e,t.length)),0);var r;const i=[...e.fields],o=[...t],a=(n+63&-64)>>3;for(const[s,l]of e.fields.entries()){const e=t[s];e&&e.length===n||(i[s]=l.clone({nullable:!0}),o[s]=null!==(r=null===e||void 0===e?void 0:e._changeLengthAndBackfillNullBitmap(n))&&void 0!==r?r:Jn({type:l.type,length:n,nullCount:n,nullBitmap:new Uint8Array(a)}))}return[e.assign(i),Jn({type:new Qe(i),length:n,children:o})]}function vr(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:new Map;for(let r=-1,i=e.length;++r<i;){const i=e[r].type,o=t[r];if(Le.isDictionary(i))if(n.has(i.id)){if(n.get(i.id)!==o.dictionary)throw new Error("Cannot create Schema containing two different dictionaries with the same Id")}else o.dictionary&&n.set(i.id,o.dictionary);i.children&&i.children.length>0&&vr(i.children,o.children,n)}return n}yr=Symbol.toStringTag,br[yr]=(e=>(e._nullCount=-1,e[Symbol.isConcatSpreadable]=!0,"RecordBatch"))(br.prototype);class wr extends br{constructor(e){const t=e.fields.map((e=>Jn({type:e.type})));super(e,Jn({type:new Qe(e.fields),nullCount:0,children:t}))}}function _r(e,t){return function(e,t){const n=[...e.fields],r=[],i={numBatches:t.reduce(((e,t)=>Math.max(e,t.length)),0)};let o=0,a=0,s=-1;const l=t.length;let u,c=[];for(;i.numBatches-- >0;){for(a=Number.POSITIVE_INFINITY,s=-1;++s<l;)c[s]=u=t[s].shift(),a=Math.min(a,u?u.length:a);Number.isFinite(a)&&(c=Sr(n,a,c,t,i),a>0&&(r[o++]=Jn({type:new Qe(n),length:a,nullCount:0,children:c.slice()})))}return[e=e.assign(n),r.map((t=>new br(e,t)))]}(e,t.map((e=>e.data.concat())))}function Sr(e,t,n,r,i){var o;const a=(t+63&-64)>>3;for(let s=-1,l=r.length;++s<l;){const l=n[s],u=null===l||void 0===l?void 0:l.length;if(u>=t)u===t?n[s]=l:(n[s]=l.slice(0,t),i.numBatches=Math.max(i.numBatches,r[s].unshift(l.slice(t,u-t))));else{const r=e[s];e[s]=r.clone({nullable:!0}),n[s]=null!==(o=null===l||void 0===l?void 0:l._changeLengthAndBackfillNullBitmap(t))&&void 0!==o?o:Jn({type:r.type,length:t,nullCount:t,nullBitmap:new Uint8Array(a)})}}return n}class xr{constructor(){for(var e,t,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];if(0===r.length)return this.batches=[],this.schema=new Zn([]),this._offsets=[0],this;let o,a;r[0]instanceof Zn&&(o=r.shift()),r[r.length-1]instanceof Uint32Array&&(a=r.pop());const s=e=>{if(e){if(e instanceof br)return[e];if(e instanceof xr)return e.batches;if(e instanceof Gn){if(e.type instanceof Qe)return[new br(new Zn(e.type.children),e)]}else{if(Array.isArray(e))return e.flatMap((e=>s(e)));if("function"===typeof e[Symbol.iterator])return[...e].flatMap((e=>s(e)));if("object"===typeof e){const t=Object.keys(e),n=t.map((t=>new Qn([e[t]]))),r=new Zn(t.map(((e,t)=>new er(String(e),n[t].type)))),[,i]=_r(r,n);return 0===i.length?[new br(e)]:i}}}return[]},l=r.flatMap((e=>s(e)));if(o=null!==(t=null!==o&&void 0!==o?o:null===(e=l[0])||void 0===e?void 0:e.schema)&&void 0!==t?t:new Zn([]),!(o instanceof Zn))throw new TypeError("Table constructor expects a [Schema, RecordBatch[]] pair.");for(const u of l){if(!(u instanceof br))throw new TypeError("Table constructor expects a [Schema, RecordBatch[]] pair.");if(!pr(o,u.schema))throw new TypeError("Table and inner RecordBatch schemas must be equivalent.")}this.schema=o,this.batches=l,this._offsets=null!==a&&void 0!==a?a:gn(this.data)}get data(){return this.batches.map((e=>{let{data:t}=e;return t}))}get numCols(){return this.schema.fields.length}get numRows(){return this.data.reduce(((e,t)=>e+t.length),0)}get nullCount(){return-1===this._nullCount&&(this._nullCount=bn(this.data)),this._nullCount}isValid(e){return!1}get(e){return null}set(e,t){}indexOf(e,t){return-1}getByteLength(e){return 0}[Symbol.iterator](){return this.batches.length>0?zn.visit(new Qn(this.data)):new Array(0)[Symbol.iterator]()}toArray(){return[...this]}toString(){return"[\n  ".concat(this.toArray().join(",\n  "),"\n]")}concat(){const e=this.schema;for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];const i=this.data.concat(n.flatMap((e=>{let{data:t}=e;return t})));return new xr(e,i.map((t=>new br(e,t))))}slice(e,t){const n=this.schema;[e,t]=dn({length:this.numRows},e,t);const r=vn(this.data,this._offsets,e,t);return new xr(n,r.map((e=>new br(n,e))))}getChild(e){return this.getChildAt(this.schema.fields.findIndex((t=>t.name===e)))}getChildAt(e){if(e>-1&&e<this.schema.fields.length){const t=this.data.map((t=>t.children[e]));if(0===t.length){const{type:n}=this.schema.fields[e],r=Jn({type:n,length:0,nullCount:0});t.push(r._changeLengthAndBackfillNullBitmap(this.numRows))}return new Qn(t)}return null}setChild(e,t){var n;return this.setChildAt(null===(n=this.schema.fields)||void 0===n?void 0:n.findIndex((t=>t.name===e)),t)}setChildAt(e,t){let n=this.schema,r=[...this.batches];if(e>-1&&e<this.numCols){t||(t=new Qn([Jn({type:new De,length:this.numRows})]));const i=n.fields.slice(),o=i[e].clone({type:t.type}),a=this.schema.fields.map(((e,t)=>this.getChildAt(t)));[i[e],a[e]]=[o,t],[n,r]=_r(n,a)}return new xr(n,r)}select(e){const t=this.schema.fields.reduce(((e,t,n)=>e.set(t.name,n)),new Map);return this.selectAt(e.map((e=>t.get(e))).filter((e=>e>-1)))}selectAt(e){const t=this.schema.selectAt(e),n=this.batches.map((t=>t.selectAt(e)));return new xr(t,n)}assign(e){const t=this.schema.fields,[n,r]=e.schema.fields.reduce(((e,n,r)=>{const[i,o]=e,a=t.findIndex((e=>e.name===n.name));return~a?o[a]=r:i.push(r),e}),[[],[]]),i=this.schema.assign(e.schema),o=[...t.map(((e,t)=>[t,r[t]])).map((t=>{let[n,r]=t;return void 0===r?this.getChildAt(n):e.getChildAt(r)})),...n.map((t=>e.getChildAt(t)))].filter(Boolean);return new xr(..._r(i,o))}}mr=Symbol.toStringTag,xr[mr]=(e=>(e.schema=null,e.batches=[],e._offsets=new Uint32Array([0]),e._nullCount=-1,e[Symbol.isConcatSpreadable]=!0,e.isValid=Sn(_n),e.get=Sn(rn.getVisitFn(l.Struct)),e.set=xn(Et.getVisitFn(l.Struct)),e.indexOf=kn(Dn.getVisitFn(l.Struct)),e.getByteLength=Sn($n.getVisitFn(l.Struct)),"Table"))(xr.prototype);class kr{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}offset(){return this.bb.readInt64(this.bb_pos)}metaDataLength(){return this.bb.readInt32(this.bb_pos+8)}bodyLength(){return this.bb.readInt64(this.bb_pos+16)}static sizeOf(){return 24}static createBlock(e,t,n,r){return e.prep(8,24),e.writeInt64(r),e.pad(4),e.writeInt32(n),e.writeInt64(t),e.offset()}}const Ir=new Int32Array(2),Tr=new Float32Array(Ir.buffer),Or=new Float64Array(Ir.buffer),Ar=1===new Uint16Array(new Uint8Array([1,0]).buffer)[0];class Cr{constructor(e,t){this.low=0|e,this.high=0|t}static create(e,t){return 0==e&&0==t?Cr.ZERO:new Cr(e,t)}toFloat64(){return(this.low>>>0)+4294967296*this.high}equals(e){return this.low==e.low&&this.high==e.high}}var Er,Nr,Mr,Br,Lr;Cr.ZERO=new Cr(0,0),function(e){e[e.UTF8_BYTES=1]="UTF8_BYTES",e[e.UTF16_STRING=2]="UTF16_STRING"}(Er||(Er={}));class Dr{constructor(e){this.bytes_=e,this.position_=0}static allocate(e){return new Dr(new Uint8Array(e))}clear(){this.position_=0}bytes(){return this.bytes_}position(){return this.position_}setPosition(e){this.position_=e}capacity(){return this.bytes_.length}readInt8(e){return this.readUint8(e)<<24>>24}readUint8(e){return this.bytes_[e]}readInt16(e){return this.readUint16(e)<<16>>16}readUint16(e){return this.bytes_[e]|this.bytes_[e+1]<<8}readInt32(e){return this.bytes_[e]|this.bytes_[e+1]<<8|this.bytes_[e+2]<<16|this.bytes_[e+3]<<24}readUint32(e){return this.readInt32(e)>>>0}readInt64(e){return new Cr(this.readInt32(e),this.readInt32(e+4))}readUint64(e){return new Cr(this.readUint32(e),this.readUint32(e+4))}readFloat32(e){return Ir[0]=this.readInt32(e),Tr[0]}readFloat64(e){return Ir[Ar?0:1]=this.readInt32(e),Ir[Ar?1:0]=this.readInt32(e+4),Or[0]}writeInt8(e,t){this.bytes_[e]=t}writeUint8(e,t){this.bytes_[e]=t}writeInt16(e,t){this.bytes_[e]=t,this.bytes_[e+1]=t>>8}writeUint16(e,t){this.bytes_[e]=t,this.bytes_[e+1]=t>>8}writeInt32(e,t){this.bytes_[e]=t,this.bytes_[e+1]=t>>8,this.bytes_[e+2]=t>>16,this.bytes_[e+3]=t>>24}writeUint32(e,t){this.bytes_[e]=t,this.bytes_[e+1]=t>>8,this.bytes_[e+2]=t>>16,this.bytes_[e+3]=t>>24}writeInt64(e,t){this.writeInt32(e,t.low),this.writeInt32(e+4,t.high)}writeUint64(e,t){this.writeUint32(e,t.low),this.writeUint32(e+4,t.high)}writeFloat32(e,t){Tr[0]=t,this.writeInt32(e,Ir[0])}writeFloat64(e,t){Or[0]=t,this.writeInt32(e,Ir[Ar?0:1]),this.writeInt32(e+4,Ir[Ar?1:0])}getBufferIdentifier(){if(this.bytes_.length<this.position_+4+4)throw new Error("FlatBuffers: ByteBuffer is too short to contain an identifier.");let e="";for(let t=0;t<4;t++)e+=String.fromCharCode(this.readInt8(this.position_+4+t));return e}__offset(e,t){const n=e-this.readInt32(e);return t<this.readInt16(n)?this.readInt16(n+t):0}__union(e,t){return e.bb_pos=t+this.readInt32(t),e.bb=this,e}__string(e,t){e+=this.readInt32(e);const n=this.readInt32(e);let r="",i=0;if(e+=4,t===Er.UTF8_BYTES)return this.bytes_.subarray(e,e+n);for(;i<n;){let t;const n=this.readUint8(e+i++);if(n<192)t=n;else{const r=this.readUint8(e+i++);if(n<224)t=(31&n)<<6|63&r;else{const o=this.readUint8(e+i++);if(n<240)t=(15&n)<<12|(63&r)<<6|63&o;else{t=(7&n)<<18|(63&r)<<12|(63&o)<<6|63&this.readUint8(e+i++)}}}t<65536?r+=String.fromCharCode(t):(t-=65536,r+=String.fromCharCode(55296+(t>>10),56320+(1023&t)))}return r}__union_with_string(e,t){return"string"===typeof e?this.__string(t):this.__union(e,t)}__indirect(e){return e+this.readInt32(e)}__vector(e){return e+this.readInt32(e)+4}__vector_len(e){return this.readInt32(e+this.readInt32(e))}__has_identifier(e){if(4!=e.length)throw new Error("FlatBuffers: file identifier must be length 4");for(let t=0;t<4;t++)if(e.charCodeAt(t)!=this.readInt8(this.position()+4+t))return!1;return!0}createLong(e,t){return Cr.create(e,t)}createScalarList(e,t){const n=[];for(let r=0;r<t;++r)null!==e(r)&&n.push(e(r));return n}createObjList(e,t){const n=[];for(let r=0;r<t;++r){const t=e(r);null!==t&&n.push(t.unpack())}return n}}class Fr{constructor(e){let t;this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1,this.string_maps=null,t=e||1024,this.bb=Dr.allocate(t),this.space=t}clear(){this.bb.clear(),this.space=this.bb.capacity(),this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1,this.string_maps=null}forceDefaults(e){this.force_defaults=e}dataBuffer(){return this.bb}asUint8Array(){return this.bb.bytes().subarray(this.bb.position(),this.bb.position()+this.offset())}prep(e,t){e>this.minalign&&(this.minalign=e);const n=1+~(this.bb.capacity()-this.space+t)&e-1;for(;this.space<n+e+t;){const e=this.bb.capacity();this.bb=Fr.growByteBuffer(this.bb),this.space+=this.bb.capacity()-e}this.pad(n)}pad(e){for(let t=0;t<e;t++)this.bb.writeInt8(--this.space,0)}writeInt8(e){this.bb.writeInt8(this.space-=1,e)}writeInt16(e){this.bb.writeInt16(this.space-=2,e)}writeInt32(e){this.bb.writeInt32(this.space-=4,e)}writeInt64(e){this.bb.writeInt64(this.space-=8,e)}writeFloat32(e){this.bb.writeFloat32(this.space-=4,e)}writeFloat64(e){this.bb.writeFloat64(this.space-=8,e)}addInt8(e){this.prep(1,0),this.writeInt8(e)}addInt16(e){this.prep(2,0),this.writeInt16(e)}addInt32(e){this.prep(4,0),this.writeInt32(e)}addInt64(e){this.prep(8,0),this.writeInt64(e)}addFloat32(e){this.prep(4,0),this.writeFloat32(e)}addFloat64(e){this.prep(8,0),this.writeFloat64(e)}addFieldInt8(e,t,n){(this.force_defaults||t!=n)&&(this.addInt8(t),this.slot(e))}addFieldInt16(e,t,n){(this.force_defaults||t!=n)&&(this.addInt16(t),this.slot(e))}addFieldInt32(e,t,n){(this.force_defaults||t!=n)&&(this.addInt32(t),this.slot(e))}addFieldInt64(e,t,n){!this.force_defaults&&t.equals(n)||(this.addInt64(t),this.slot(e))}addFieldFloat32(e,t,n){(this.force_defaults||t!=n)&&(this.addFloat32(t),this.slot(e))}addFieldFloat64(e,t,n){(this.force_defaults||t!=n)&&(this.addFloat64(t),this.slot(e))}addFieldOffset(e,t,n){(this.force_defaults||t!=n)&&(this.addOffset(t),this.slot(e))}addFieldStruct(e,t,n){t!=n&&(this.nested(t),this.slot(e))}nested(e){if(e!=this.offset())throw new Error("FlatBuffers: struct must be serialized inline.")}notNested(){if(this.isNested)throw new Error("FlatBuffers: object serialization must not be nested.")}slot(e){null!==this.vtable&&(this.vtable[e]=this.offset())}offset(){return this.bb.capacity()-this.space}static growByteBuffer(e){const t=e.capacity();if(3221225472&t)throw new Error("FlatBuffers: cannot grow buffer beyond 2 gigabytes.");const n=t<<1,r=Dr.allocate(n);return r.setPosition(n-t),r.bytes().set(e.bytes(),n-t),r}addOffset(e){this.prep(4,0),this.writeInt32(this.offset()-e+4)}startObject(e){this.notNested(),null==this.vtable&&(this.vtable=[]),this.vtable_in_use=e;for(let t=0;t<e;t++)this.vtable[t]=0;this.isNested=!0,this.object_start=this.offset()}endObject(){if(null==this.vtable||!this.isNested)throw new Error("FlatBuffers: endObject called without startObject");this.addInt32(0);const e=this.offset();let t=this.vtable_in_use-1;for(;t>=0&&0==this.vtable[t];t--);const n=t+1;for(;t>=0;t--)this.addInt16(0!=this.vtable[t]?e-this.vtable[t]:0);this.addInt16(e-this.object_start);const r=2*(n+2);this.addInt16(r);let i=0;const o=this.space;e:for(t=0;t<this.vtables.length;t++){const e=this.bb.capacity()-this.vtables[t];if(r==this.bb.readInt16(e)){for(let t=2;t<r;t+=2)if(this.bb.readInt16(o+t)!=this.bb.readInt16(e+t))continue e;i=this.vtables[t];break}}return i?(this.space=this.bb.capacity()-e,this.bb.writeInt32(this.space,i-e)):(this.vtables.push(this.offset()),this.bb.writeInt32(this.bb.capacity()-e,this.offset()-e)),this.isNested=!1,e}finish(e,t,n){const r=n?4:0;if(t){const e=t;if(this.prep(this.minalign,8+r),4!=e.length)throw new Error("FlatBuffers: file identifier must be length 4");for(let t=3;t>=0;t--)this.writeInt8(e.charCodeAt(t))}this.prep(this.minalign,4+r),this.addOffset(e),r&&this.addInt32(this.bb.capacity()-this.space),this.bb.setPosition(this.space)}finishSizePrefixed(e,t){this.finish(e,t,!0)}requiredField(e,t){const n=this.bb.capacity()-e,r=n-this.bb.readInt32(n);if(!(0!=this.bb.readInt16(r+t)))throw new Error("FlatBuffers: field "+t+" must be set")}startVector(e,t,n){this.notNested(),this.vector_num_elems=t,this.prep(4,e*t),this.prep(n,e*t)}endVector(){return this.writeInt32(this.vector_num_elems),this.offset()}createSharedString(e){if(!e)return 0;if(this.string_maps||(this.string_maps=new Map),this.string_maps.has(e))return this.string_maps.get(e);const t=this.createString(e);return this.string_maps.set(e,t),t}createString(e){if(!e)return 0;let t;if(e instanceof Uint8Array)t=e;else{t=[];let n=0;for(;n<e.length;){let r;const i=e.charCodeAt(n++);if(i<55296||i>=56320)r=i;else{r=(i<<10)+e.charCodeAt(n++)+-56613888}r<128?t.push(r):(r<2048?t.push(r>>6&31|192):(r<65536?t.push(r>>12&15|224):t.push(r>>18&7|240,r>>12&63|128),t.push(r>>6&63|128)),t.push(63&r|128))}}this.addInt8(0),this.startVector(1,t.length,1),this.bb.setPosition(this.space-=t.length);for(let n=0,r=this.space,i=this.bb.bytes();n<t.length;n++)i[r++]=t[n];return this.endVector()}createLong(e,t){return Cr.create(e,t)}createObjectOffset(e){return null===e?0:"string"===typeof e?this.createString(e):e.pack(this)}createObjectOffsetList(e){const t=[];for(let n=0;n<e.length;++n){const r=e[n];if(null===r)throw new Error("FlatBuffers: Argument for createObjectOffsetList cannot contain null.");t.push(this.createObjectOffset(r))}return t}createStructOffsetList(e,t){return t(this,e.length),this.createObjectOffsetList(e),this.endVector()}}class Pr{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}static getRootAsKeyValue(e,t){return(t||new Pr).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsKeyValue(e,t){return e.setPosition(e.position()+4),(t||new Pr).__init(e.readInt32(e.position())+e.position(),e)}key(e){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.__string(this.bb_pos+t,e):null}value(e){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__string(this.bb_pos+t,e):null}static startKeyValue(e){e.startObject(2)}static addKey(e,t){e.addFieldOffset(0,t,0)}static addValue(e,t){e.addFieldOffset(1,t,0)}static endKeyValue(e){return e.endObject()}static createKeyValue(e,t,n){return Pr.startKeyValue(e),Pr.addKey(e,t),Pr.addValue(e,n),Pr.endKeyValue(e)}}!function(e){e[e.V1=0]="V1",e[e.V2=1]="V2",e[e.V3=2]="V3",e[e.V4=3]="V4",e[e.V5=4]="V5"}(Nr||(Nr={})),function(e){e[e.Little=0]="Little",e[e.Big=1]="Big"}(Mr||(Mr={})),function(e){e[e.DenseArray=0]="DenseArray"}(Br||(Br={}));class Rr{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}static getRootAsInt(e,t){return(t||new Rr).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsInt(e,t){return e.setPosition(e.position()+4),(t||new Rr).__init(e.readInt32(e.position())+e.position(),e)}bitWidth(){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt32(this.bb_pos+e):0}isSigned(){const e=this.bb.__offset(this.bb_pos,6);return!!e&&!!this.bb.readInt8(this.bb_pos+e)}static startInt(e){e.startObject(2)}static addBitWidth(e,t){e.addFieldInt32(0,t,0)}static addIsSigned(e,t){e.addFieldInt8(1,+t,0)}static endInt(e){return e.endObject()}static createInt(e,t,n){return Rr.startInt(e),Rr.addBitWidth(e,t),Rr.addIsSigned(e,n),Rr.endInt(e)}}class zr{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}static getRootAsDictionaryEncoding(e,t){return(t||new zr).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsDictionaryEncoding(e,t){return e.setPosition(e.position()+4),(t||new zr).__init(e.readInt32(e.position())+e.position(),e)}id(){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt64(this.bb_pos+e):this.bb.createLong(0,0)}indexType(e){const t=this.bb.__offset(this.bb_pos,6);return t?(e||new Rr).__init(this.bb.__indirect(this.bb_pos+t),this.bb):null}isOrdered(){const e=this.bb.__offset(this.bb_pos,8);return!!e&&!!this.bb.readInt8(this.bb_pos+e)}dictionaryKind(){const e=this.bb.__offset(this.bb_pos,10);return e?this.bb.readInt16(this.bb_pos+e):Br.DenseArray}static startDictionaryEncoding(e){e.startObject(4)}static addId(e,t){e.addFieldInt64(0,t,e.createLong(0,0))}static addIndexType(e,t){e.addFieldOffset(1,t,0)}static addIsOrdered(e,t){e.addFieldInt8(2,+t,0)}static addDictionaryKind(e,t){e.addFieldInt16(3,t,Br.DenseArray)}static endDictionaryEncoding(e){return e.endObject()}}!function(e){e[e.NONE=0]="NONE",e[e.Null=1]="Null",e[e.Int=2]="Int",e[e.FloatingPoint=3]="FloatingPoint",e[e.Binary=4]="Binary",e[e.Utf8=5]="Utf8",e[e.Bool=6]="Bool",e[e.Decimal=7]="Decimal",e[e.Date=8]="Date",e[e.Time=9]="Time",e[e.Timestamp=10]="Timestamp",e[e.Interval=11]="Interval",e[e.List=12]="List",e[e.Struct_=13]="Struct_",e[e.Union=14]="Union",e[e.FixedSizeBinary=15]="FixedSizeBinary",e[e.FixedSizeList=16]="FixedSizeList",e[e.Map=17]="Map",e[e.Duration=18]="Duration",e[e.LargeBinary=19]="LargeBinary",e[e.LargeUtf8=20]="LargeUtf8",e[e.LargeList=21]="LargeList"}(Lr||(Lr={}));class Ur{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}static getRootAsField(e,t){return(t||new Ur).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsField(e,t){return e.setPosition(e.position()+4),(t||new Ur).__init(e.readInt32(e.position())+e.position(),e)}name(e){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.__string(this.bb_pos+t,e):null}nullable(){const e=this.bb.__offset(this.bb_pos,6);return!!e&&!!this.bb.readInt8(this.bb_pos+e)}typeType(){const e=this.bb.__offset(this.bb_pos,8);return e?this.bb.readUint8(this.bb_pos+e):Lr.NONE}type(e){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.__union(e,this.bb_pos+t):null}dictionary(e){const t=this.bb.__offset(this.bb_pos,12);return t?(e||new zr).__init(this.bb.__indirect(this.bb_pos+t),this.bb):null}children(e,t){const n=this.bb.__offset(this.bb_pos,14);return n?(t||new Ur).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*e),this.bb):null}childrenLength(){const e=this.bb.__offset(this.bb_pos,14);return e?this.bb.__vector_len(this.bb_pos+e):0}customMetadata(e,t){const n=this.bb.__offset(this.bb_pos,16);return n?(t||new Pr).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*e),this.bb):null}customMetadataLength(){const e=this.bb.__offset(this.bb_pos,16);return e?this.bb.__vector_len(this.bb_pos+e):0}static startField(e){e.startObject(7)}static addName(e,t){e.addFieldOffset(0,t,0)}static addNullable(e,t){e.addFieldInt8(1,+t,0)}static addTypeType(e,t){e.addFieldInt8(2,t,Lr.NONE)}static addType(e,t){e.addFieldOffset(3,t,0)}static addDictionary(e,t){e.addFieldOffset(4,t,0)}static addChildren(e,t){e.addFieldOffset(5,t,0)}static createChildrenVector(e,t){e.startVector(4,t.length,4);for(let n=t.length-1;n>=0;n--)e.addOffset(t[n]);return e.endVector()}static startChildrenVector(e,t){e.startVector(4,t,4)}static addCustomMetadata(e,t){e.addFieldOffset(6,t,0)}static createCustomMetadataVector(e,t){e.startVector(4,t.length,4);for(let n=t.length-1;n>=0;n--)e.addOffset(t[n]);return e.endVector()}static startCustomMetadataVector(e,t){e.startVector(4,t,4)}static endField(e){return e.endObject()}}class jr{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}static getRootAsSchema(e,t){return(t||new jr).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsSchema(e,t){return e.setPosition(e.position()+4),(t||new jr).__init(e.readInt32(e.position())+e.position(),e)}endianness(){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt16(this.bb_pos+e):Mr.Little}fields(e,t){const n=this.bb.__offset(this.bb_pos,6);return n?(t||new Ur).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*e),this.bb):null}fieldsLength(){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__vector_len(this.bb_pos+e):0}customMetadata(e,t){const n=this.bb.__offset(this.bb_pos,8);return n?(t||new Pr).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*e),this.bb):null}customMetadataLength(){const e=this.bb.__offset(this.bb_pos,8);return e?this.bb.__vector_len(this.bb_pos+e):0}features(e){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readInt64(this.bb.__vector(this.bb_pos+t)+8*e):this.bb.createLong(0,0)}featuresLength(){const e=this.bb.__offset(this.bb_pos,10);return e?this.bb.__vector_len(this.bb_pos+e):0}static startSchema(e){e.startObject(4)}static addEndianness(e,t){e.addFieldInt16(0,t,Mr.Little)}static addFields(e,t){e.addFieldOffset(1,t,0)}static createFieldsVector(e,t){e.startVector(4,t.length,4);for(let n=t.length-1;n>=0;n--)e.addOffset(t[n]);return e.endVector()}static startFieldsVector(e,t){e.startVector(4,t,4)}static addCustomMetadata(e,t){e.addFieldOffset(2,t,0)}static createCustomMetadataVector(e,t){e.startVector(4,t.length,4);for(let n=t.length-1;n>=0;n--)e.addOffset(t[n]);return e.endVector()}static startCustomMetadataVector(e,t){e.startVector(4,t,4)}static addFeatures(e,t){e.addFieldOffset(3,t,0)}static createFeaturesVector(e,t){e.startVector(8,t.length,8);for(let n=t.length-1;n>=0;n--)e.addInt64(t[n]);return e.endVector()}static startFeaturesVector(e,t){e.startVector(8,t,8)}static endSchema(e){return e.endObject()}static finishSchemaBuffer(e,t){e.finish(t)}static finishSizePrefixedSchemaBuffer(e,t){e.finish(t,void 0,!0)}static createSchema(e,t,n,r,i){return jr.startSchema(e),jr.addEndianness(e,t),jr.addFields(e,n),jr.addCustomMetadata(e,r),jr.addFeatures(e,i),jr.endSchema(e)}}class Vr{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}static getRootAsFooter(e,t){return(t||new Vr).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsFooter(e,t){return e.setPosition(e.position()+4),(t||new Vr).__init(e.readInt32(e.position())+e.position(),e)}version(){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt16(this.bb_pos+e):Nr.V1}schema(e){const t=this.bb.__offset(this.bb_pos,6);return t?(e||new jr).__init(this.bb.__indirect(this.bb_pos+t),this.bb):null}dictionaries(e,t){const n=this.bb.__offset(this.bb_pos,8);return n?(t||new kr).__init(this.bb.__vector(this.bb_pos+n)+24*e,this.bb):null}dictionariesLength(){const e=this.bb.__offset(this.bb_pos,8);return e?this.bb.__vector_len(this.bb_pos+e):0}recordBatches(e,t){const n=this.bb.__offset(this.bb_pos,10);return n?(t||new kr).__init(this.bb.__vector(this.bb_pos+n)+24*e,this.bb):null}recordBatchesLength(){const e=this.bb.__offset(this.bb_pos,10);return e?this.bb.__vector_len(this.bb_pos+e):0}customMetadata(e,t){const n=this.bb.__offset(this.bb_pos,12);return n?(t||new Pr).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*e),this.bb):null}customMetadataLength(){const e=this.bb.__offset(this.bb_pos,12);return e?this.bb.__vector_len(this.bb_pos+e):0}static startFooter(e){e.startObject(5)}static addVersion(e,t){e.addFieldInt16(0,t,Nr.V1)}static addSchema(e,t){e.addFieldOffset(1,t,0)}static addDictionaries(e,t){e.addFieldOffset(2,t,0)}static startDictionariesVector(e,t){e.startVector(24,t,8)}static addRecordBatches(e,t){e.addFieldOffset(3,t,0)}static startRecordBatchesVector(e,t){e.startVector(24,t,8)}static addCustomMetadata(e,t){e.addFieldOffset(4,t,0)}static createCustomMetadataVector(e,t){e.startVector(4,t.length,4);for(let n=t.length-1;n>=0;n--)e.addOffset(t[n]);return e.endVector()}static startCustomMetadataVector(e,t){e.startVector(4,t,4)}static endFooter(e){return e.endObject()}static finishFooterBuffer(e,t){e.finish(t)}static finishSizePrefixedFooterBuffer(e,t){e.finish(t,void 0,!0)}}var Wr=Cr,$r=Fr,Hr=Dr;class Yr{constructor(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.V4,r=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0;this.schema=t,this.version=n,r&&(this._recordBatches=r),i&&(this._dictionaryBatches=i)}static decode(e){e=new Hr(Z(e));const t=Vr.getRootAsFooter(e),n=Zn.decode(t.schema());return new Kr(n,t)}static encode(t){const n=new $r,r=Zn.encode(n,t.schema);Vr.startRecordBatchesVector(n,t.numRecordBatches);for(const e of[...t.recordBatches()].slice().reverse())Qr.encode(n,e);const i=n.endVector();Vr.startDictionariesVector(n,t.numDictionaries);for(const e of[...t.dictionaryBatches()].slice().reverse())Qr.encode(n,e);const o=n.endVector();return Vr.startFooter(n),Vr.addSchema(n,r),Vr.addVersion(n,e.V4),Vr.addRecordBatches(n,i),Vr.addDictionaries(n,o),Vr.finishFooterBuffer(n,Vr.endFooter(n)),n.asUint8Array()}get numRecordBatches(){return this._recordBatches.length}get numDictionaries(){return this._dictionaryBatches.length}*recordBatches(){for(let e,t=-1,n=this.numRecordBatches;++t<n;)(e=this.getRecordBatch(t))&&(yield e)}*dictionaryBatches(){for(let e,t=-1,n=this.numDictionaries;++t<n;)(e=this.getDictionaryBatch(t))&&(yield e)}getRecordBatch(e){return e>=0&&e<this.numRecordBatches&&this._recordBatches[e]||null}getDictionaryBatch(e){return e>=0&&e<this.numDictionaries&&this._dictionaryBatches[e]||null}}class Kr extends Yr{constructor(e,t){super(e,t.version()),this._footer=t}get numRecordBatches(){return this._footer.recordBatchesLength()}get numDictionaries(){return this._footer.dictionariesLength()}getRecordBatch(e){if(e>=0&&e<this.numRecordBatches){const t=this._footer.recordBatches(e);if(t)return Qr.decode(t)}return null}getDictionaryBatch(e){if(e>=0&&e<this.numDictionaries){const t=this._footer.dictionaries(e);if(t)return Qr.decode(t)}return null}}class Qr{constructor(e,t,n){this.metaDataLength=e,this.offset="number"===typeof n?n:n.low,this.bodyLength="number"===typeof t?t:t.low}static decode(e){return new Qr(e.metaDataLength(),e.bodyLength(),e.offset())}static encode(e,t){const{metaDataLength:n}=t,r=new Wr(t.offset,0),i=new Wr(t.bodyLength,0);return kr.createBlock(e,r,n,i)}}const qr={fromIterable:e=>Gr(function*(e){let t,n,r,i,o=!1,a=[],s=0;function l(){return"peek"===r?G(a,i)[0]:([n,a,s]=G(a,i),n)}({cmd:r,size:i}=yield null);const u=(c=e,te(Uint8Array,c))[Symbol.iterator]();var c;try{do{if(({done:t,value:n}=Number.isNaN(i-s)?u.next():u.next(i-s)),!t&&n.byteLength>0&&(a.push(n),s+=n.byteLength),t||i<=s)do{({cmd:r,size:i}=yield l())}while(i<s)}while(!t)}catch(d){(o=!0)&&"function"===typeof u.throw&&u.throw(d)}finally{!1===o&&"function"===typeof u.return&&u.return(null)}return null}(e)),fromAsyncIterable:e=>Gr(function(e){return j(this,arguments,(function*(){let t,n,r,i,o=!1,a=[],s=0;function l(){return"peek"===r?G(a,i)[0]:([n,a,s]=G(a,i),n)}({cmd:r,size:i}=yield yield U(null));const u=(c=e,ne(Uint8Array,c))[Symbol.asyncIterator]();var c;try{do{if(({done:t,value:n}=Number.isNaN(i-s)?yield U(u.next()):yield U(u.next(i-s))),!t&&n.byteLength>0&&(a.push(n),s+=n.byteLength),t||i<=s)do{({cmd:r,size:i}=yield yield U(l()))}while(i<s)}while(!t)}catch(d){(o=!0)&&"function"===typeof u.throw&&(yield U(u.throw(d)))}finally{!1===o&&"function"===typeof u.return&&(yield U(u.return(new Uint8Array(0))))}return yield U(null)}))}(e)),fromDOMStream:e=>Gr(function(e){return j(this,arguments,(function*(){let t,n,r,i=!1,o=!1,a=[],s=0;function l(){return"peek"===n?G(a,r)[0]:([t,a,s]=G(a,r),t)}({cmd:n,size:r}=yield yield U(null));const u=new Xr(e);try{do{if(({done:i,value:t}=Number.isNaN(r-s)?yield U(u.read()):yield U(u.read(r-s))),!i&&t.byteLength>0&&(a.push(Z(t)),s+=t.byteLength),i||r<=s)do{({cmd:n,size:r}=yield yield U(l()))}while(r<s)}while(!i)}catch(c){(o=!0)&&(yield U(u.cancel(c)))}finally{!1===o?yield U(u.cancel()):e.locked&&u.releaseLock()}return yield U(null)}))}(e)),fromNodeStream:e=>Gr(function(e){return j(this,arguments,(function*(){const t=[];let n,r,i,o="error",a=!1,s=null,l=0,u=[];function c(){return"peek"===n?G(u,r)[0]:([i,u,l]=G(u,r),i)}if(({cmd:n,size:r}=yield yield U(null)),e.isTTY)return yield yield U(new Uint8Array(0)),yield U(null);try{t[0]=Jr(e,"end"),t[1]=Jr(e,"error");do{if(t[2]=Jr(e,"readable"),[o,s]=yield U(Promise.race(t.map((e=>e[2])))),"error"===o)break;if((a="end"===o)||(Number.isFinite(r-l)?(i=Z(e.read(r-l)),i.byteLength<r-l&&(i=Z(e.read()))):i=Z(e.read()),i.byteLength>0&&(u.push(i),l+=i.byteLength)),a||r<=l)do{({cmd:n,size:r}=yield yield U(c()))}while(r<l)}while(!a)}finally{yield U(d(t,"error"===o?s:null))}return yield U(null);function d(t,n){return i=u=null,new Promise(((r,i)=>{for(const[n,a]of t)e.off(n,a);try{const t=e.destroy;t&&t.call(e,n),n=void 0}catch(o){n=o||n}finally{null!=n?i(n):r()}}))}}))}(e)),toDOMStream(e,t){throw new Error('"toDOMStream" not available in this environment')},toNodeStream(e,t){throw new Error('"toNodeStream" not available in this environment')}},Gr=e=>(e.next(),e);class Xr{constructor(e){this.source=e,this.reader=null,this.reader=this.source.getReader(),this.reader.closed.catch((()=>{}))}get closed(){return this.reader?this.reader.closed.catch((()=>{})):Promise.resolve()}releaseLock(){this.reader&&this.reader.releaseLock(),this.reader=null}cancel(e){return R(this,void 0,void 0,(function*(){const{reader:t,source:n}=this;t&&(yield t.cancel(e).catch((()=>{}))),n&&n.locked&&this.releaseLock()}))}read(e){return R(this,void 0,void 0,(function*(){if(0===e)return{done:null==this.reader,value:new Uint8Array(0)};const t=yield this.reader.read();return!t.done&&(t.value=Z(t)),t}))}}const Jr=(e,t)=>{const n=e=>r([t,e]);let r;return[t,n,new Promise((i=>(r=i)&&e.once(t,n)))]};const Zr=Object.freeze({done:!0,value:void 0});class ei{constructor(e){this._json=e}get schema(){return this._json.schema}get batches(){return this._json.batches||[]}get dictionaries(){return this._json.dictionaries||[]}}class ti{tee(){return this._getDOMStream().tee()}pipe(e,t){return this._getNodeStream().pipe(e,t)}pipeTo(e,t){return this._getDOMStream().pipeTo(e,t)}pipeThrough(e,t){return this._getDOMStream().pipeThrough(e,t)}_getDOMStream(){return this._DOMStream||(this._DOMStream=this.toDOMStream())}_getNodeStream(){return this._nodeStream||(this._nodeStream=this.toNodeStream())}}class ni extends ti{constructor(){super(),this._values=[],this.resolvers=[],this._closedPromise=new Promise((e=>this._closedPromiseResolve=e))}get closed(){return this._closedPromise}cancel(e){return R(this,void 0,void 0,(function*(){yield this.return(e)}))}write(e){this._ensureOpen()&&(this.resolvers.length<=0?this._values.push(e):this.resolvers.shift().resolve({done:!1,value:e}))}abort(e){this._closedPromiseResolve&&(this.resolvers.length<=0?this._error={error:e}:this.resolvers.shift().reject({done:!0,value:e}))}close(){if(this._closedPromiseResolve){const{resolvers:e}=this;for(;e.length>0;)e.shift().resolve(Zr);this._closedPromiseResolve(),this._closedPromiseResolve=void 0}}[Symbol.asyncIterator](){return this}toDOMStream(e){return qr.toDOMStream(this._closedPromiseResolve||this._error?this:this._values,e)}toNodeStream(e){return qr.toNodeStream(this._closedPromiseResolve||this._error?this:this._values,e)}throw(e){return R(this,void 0,void 0,(function*(){return yield this.abort(e),Zr}))}return(e){return R(this,void 0,void 0,(function*(){return yield this.close(),Zr}))}read(e){return R(this,void 0,void 0,(function*(){return(yield this.next(e,"read")).value}))}peek(e){return R(this,void 0,void 0,(function*(){return(yield this.next(e,"peek")).value}))}next(){return this._values.length>0?Promise.resolve({done:!1,value:this._values.shift()}):this._error?Promise.reject({done:!0,value:this._error.error}):this._closedPromiseResolve?new Promise(((e,t)=>{this.resolvers.push({resolve:e,reject:t})})):Promise.resolve(Zr)}_ensureOpen(){if(this._closedPromiseResolve)return!0;throw new Error("AsyncQueue is closed")}}class ri extends ni{write(e){if((e=Z(e)).byteLength>0)return super.write(e)}toString(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0]?H(this.toUint8Array(!0)):this.toUint8Array(!1).then(H)}toUint8Array(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0]?G(this._values)[0]:(()=>R(this,void 0,void 0,(function*(){var e,t;const n=[];let r=0;try{for(var i,o=W(this);!(i=yield o.next()).done;){const e=i.value;n.push(e),r+=e.byteLength}}catch(a){e={error:a}}finally{try{i&&!i.done&&(t=o.return)&&(yield t.call(o))}finally{if(e)throw e.error}}return G(n,r)[0]})))()}}class ii{constructor(e){e&&(this.source=new ai(qr.fromIterable(e)))}[Symbol.iterator](){return this}next(e){return this.source.next(e)}throw(e){return this.source.throw(e)}return(e){return this.source.return(e)}peek(e){return this.source.peek(e)}read(e){return this.source.read(e)}}class oi{constructor(e){e instanceof oi?this.source=e.source:e instanceof ri?this.source=new si(qr.fromAsyncIterable(e)):D(e)?this.source=new si(qr.fromNodeStream(e)):L(e)?this.source=new si(qr.fromDOMStream(e)):M(e)?this.source=new si(qr.fromDOMStream(e.body)):O(e)?this.source=new si(qr.fromIterable(e)):(T(e)||A(e))&&(this.source=new si(qr.fromAsyncIterable(e)))}[Symbol.asyncIterator](){return this}next(e){return this.source.next(e)}throw(e){return this.source.throw(e)}return(e){return this.source.return(e)}get closed(){return this.source.closed}cancel(e){return this.source.cancel(e)}peek(e){return this.source.peek(e)}read(e){return this.source.read(e)}}class ai{constructor(e){this.source=e}cancel(e){this.return(e)}peek(e){return this.next(e,"peek").value}read(e){return this.next(e,"read").value}next(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"read";return this.source.next({cmd:t,size:e})}throw(e){return Object.create(this.source.throw&&this.source.throw(e)||Zr)}return(e){return Object.create(this.source.return&&this.source.return(e)||Zr)}}class si{constructor(e){this.source=e,this._closedPromise=new Promise((e=>this._closedPromiseResolve=e))}cancel(e){return R(this,void 0,void 0,(function*(){yield this.return(e)}))}get closed(){return this._closedPromise}read(e){return R(this,void 0,void 0,(function*(){return(yield this.next(e,"read")).value}))}peek(e){return R(this,void 0,void 0,(function*(){return(yield this.next(e,"peek")).value}))}next(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"read";return R(this,void 0,void 0,(function*(){return yield this.source.next({cmd:t,size:e})}))}throw(e){return R(this,void 0,void 0,(function*(){const t=this.source.throw&&(yield this.source.throw(e))||Zr;return this._closedPromiseResolve&&this._closedPromiseResolve(),this._closedPromiseResolve=void 0,Object.create(t)}))}return(e){return R(this,void 0,void 0,(function*(){const t=this.source.return&&(yield this.source.return(e))||Zr;return this._closedPromiseResolve&&this._closedPromiseResolve(),this._closedPromiseResolve=void 0,Object.create(t)}))}}class li extends ii{constructor(e,t){super(),this.position=0,this.buffer=Z(e),this.size="undefined"===typeof t?this.buffer.byteLength:t}readInt32(e){const{buffer:t,byteOffset:n}=this.readAt(e,4);return new DataView(t,n).getInt32(0,!0)}seek(e){return this.position=Math.min(e,this.size),e<this.size}read(e){const{buffer:t,size:n,position:r}=this;return t&&r<n?("number"!==typeof e&&(e=Number.POSITIVE_INFINITY),this.position=Math.min(n,r+Math.min(n-r,e)),t.subarray(r,this.position)):null}readAt(e,t){const n=this.buffer,r=Math.min(this.size,e+t);return n?n.subarray(e,r):new Uint8Array(t)}close(){this.buffer&&(this.buffer=null)}throw(e){return this.close(),{done:!0,value:e}}return(e){return this.close(),{done:!0,value:e}}}class ui extends oi{constructor(e,t){super(),this.position=0,this._handle=e,"number"===typeof t?this.size=t:this._pending=(()=>R(this,void 0,void 0,(function*(){this.size=(yield e.stat()).size,delete this._pending})))()}readInt32(e){return R(this,void 0,void 0,(function*(){const{buffer:t,byteOffset:n}=yield this.readAt(e,4);return new DataView(t,n).getInt32(0,!0)}))}seek(e){return R(this,void 0,void 0,(function*(){return this._pending&&(yield this._pending),this.position=Math.min(e,this.size),e<this.size}))}read(e){return R(this,void 0,void 0,(function*(){this._pending&&(yield this._pending);const{_handle:t,size:n,position:r}=this;if(t&&r<n){"number"!==typeof e&&(e=Number.POSITIVE_INFINITY);let i=r,o=0,a=0;const s=Math.min(n,i+Math.min(n-i,e)),l=new Uint8Array(Math.max(0,(this.position=s)-i));for(;(i+=a)<s&&(o+=a)<l.byteLength;)({bytesRead:a}=yield t.read(l,o,l.byteLength-o,i));return l}return null}))}readAt(e,t){return R(this,void 0,void 0,(function*(){this._pending&&(yield this._pending);const{_handle:n,size:r}=this;if(n&&e+t<r){const i=Math.min(r,e+t),o=new Uint8Array(i-e);return(yield n.read(o,0,t,e)).buffer}return new Uint8Array(t)}))}close(){return R(this,void 0,void 0,(function*(){const e=this._handle;this._handle=null,e&&(yield e.close())}))}throw(e){return R(this,void 0,void 0,(function*(){return yield this.close(),{done:!0,value:e}}))}return(e){return R(this,void 0,void 0,(function*(){return yield this.close(),{done:!0,value:e}}))}}function ci(e){return e<0&&(e=4294967295+e+1),"0x".concat(e.toString(16))}const di=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8];class fi{constructor(e){this.buffer=e}high(){return this.buffer[1]}low(){return this.buffer[0]}_times(e){const t=new Uint32Array([this.buffer[1]>>>16,65535&this.buffer[1],this.buffer[0]>>>16,65535&this.buffer[0]]),n=new Uint32Array([e.buffer[1]>>>16,65535&e.buffer[1],e.buffer[0]>>>16,65535&e.buffer[0]]);let r=t[3]*n[3];this.buffer[0]=65535&r;let i=r>>>16;return r=t[2]*n[3],i+=r,r=t[3]*n[2]>>>0,i+=r,this.buffer[0]+=i<<16,this.buffer[1]=i>>>0<r?65536:0,this.buffer[1]+=i>>>16,this.buffer[1]+=t[1]*n[3]+t[2]*n[2]+t[3]*n[1],this.buffer[1]+=t[0]*n[3]+t[1]*n[2]+t[2]*n[1]+t[3]*n[0]<<16,this}_plus(e){const t=this.buffer[0]+e.buffer[0]>>>0;this.buffer[1]+=e.buffer[1],t<this.buffer[0]>>>0&&++this.buffer[1],this.buffer[0]=t}lessThan(e){return this.buffer[1]<e.buffer[1]||this.buffer[1]===e.buffer[1]&&this.buffer[0]<e.buffer[0]}equals(e){return this.buffer[1]===e.buffer[1]&&this.buffer[0]==e.buffer[0]}greaterThan(e){return e.lessThan(this)}hex(){return"".concat(ci(this.buffer[1])," ").concat(ci(this.buffer[0]))}}class hi extends fi{times(e){return this._times(e),this}plus(e){return this._plus(e),this}static from(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2);return hi.fromString("string"===typeof e?e:e.toString(),t)}static fromNumber(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2);return hi.fromString(e.toString(),t)}static fromString(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2);const n=e.length,r=new hi(t);for(let i=0;i<n;){const t=8<n-i?8:n-i,o=new hi(new Uint32Array([Number.parseInt(e.slice(i,i+t),10),0])),a=new hi(new Uint32Array([di[t],0]));r.times(a),r.plus(o),i+=t}return r}static convertArray(e){const t=new Uint32Array(2*e.length);for(let n=-1,r=e.length;++n<r;)hi.from(e[n],new Uint32Array(t.buffer,t.byteOffset+2*n*4,2));return t}static multiply(e,t){return new hi(new Uint32Array(e.buffer)).times(t)}static add(e,t){return new hi(new Uint32Array(e.buffer)).plus(t)}}class pi extends fi{negate(){return this.buffer[0]=1+~this.buffer[0],this.buffer[1]=~this.buffer[1],0==this.buffer[0]&&++this.buffer[1],this}times(e){return this._times(e),this}plus(e){return this._plus(e),this}lessThan(e){const t=this.buffer[1]<<0,n=e.buffer[1]<<0;return t<n||t===n&&this.buffer[0]<e.buffer[0]}static from(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2);return pi.fromString("string"===typeof e?e:e.toString(),t)}static fromNumber(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2);return pi.fromString(e.toString(),t)}static fromString(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2);const n=e.startsWith("-"),r=e.length,i=new pi(t);for(let o=n?1:0;o<r;){const t=8<r-o?8:r-o,n=new pi(new Uint32Array([Number.parseInt(e.slice(o,o+t),10),0])),a=new pi(new Uint32Array([di[t],0]));i.times(a),i.plus(n),o+=t}return n?i.negate():i}static convertArray(e){const t=new Uint32Array(2*e.length);for(let n=-1,r=e.length;++n<r;)pi.from(e[n],new Uint32Array(t.buffer,t.byteOffset+2*n*4,2));return t}static multiply(e,t){return new pi(new Uint32Array(e.buffer)).times(t)}static add(e,t){return new pi(new Uint32Array(e.buffer)).plus(t)}}class yi{constructor(e){this.buffer=e}high(){return new pi(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2))}low(){return new pi(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset,2))}negate(){return this.buffer[0]=1+~this.buffer[0],this.buffer[1]=~this.buffer[1],this.buffer[2]=~this.buffer[2],this.buffer[3]=~this.buffer[3],0==this.buffer[0]&&++this.buffer[1],0==this.buffer[1]&&++this.buffer[2],0==this.buffer[2]&&++this.buffer[3],this}times(e){const t=new hi(new Uint32Array([this.buffer[3],0])),n=new hi(new Uint32Array([this.buffer[2],0])),r=new hi(new Uint32Array([this.buffer[1],0])),i=new hi(new Uint32Array([this.buffer[0],0])),o=new hi(new Uint32Array([e.buffer[3],0])),a=new hi(new Uint32Array([e.buffer[2],0])),s=new hi(new Uint32Array([e.buffer[1],0])),l=new hi(new Uint32Array([e.buffer[0],0]));let u=hi.multiply(i,l);this.buffer[0]=u.low();const c=new hi(new Uint32Array([u.high(),0]));u=hi.multiply(r,l),c.plus(u),u=hi.multiply(i,s),c.plus(u),this.buffer[1]=c.low(),this.buffer[3]=c.lessThan(u)?1:0,this.buffer[2]=c.high();return new hi(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2)).plus(hi.multiply(n,l)).plus(hi.multiply(r,s)).plus(hi.multiply(i,a)),this.buffer[3]+=hi.multiply(t,l).plus(hi.multiply(n,s)).plus(hi.multiply(r,a)).plus(hi.multiply(i,o)).low(),this}plus(e){const t=new Uint32Array(4);return t[3]=this.buffer[3]+e.buffer[3]>>>0,t[2]=this.buffer[2]+e.buffer[2]>>>0,t[1]=this.buffer[1]+e.buffer[1]>>>0,t[0]=this.buffer[0]+e.buffer[0]>>>0,t[0]<this.buffer[0]>>>0&&++t[1],t[1]<this.buffer[1]>>>0&&++t[2],t[2]<this.buffer[2]>>>0&&++t[3],this.buffer[3]=t[3],this.buffer[2]=t[2],this.buffer[1]=t[1],this.buffer[0]=t[0],this}hex(){return"".concat(ci(this.buffer[3])," ").concat(ci(this.buffer[2])," ").concat(ci(this.buffer[1])," ").concat(ci(this.buffer[0]))}static multiply(e,t){return new yi(new Uint32Array(e.buffer)).times(t)}static add(e,t){return new yi(new Uint32Array(e.buffer)).plus(t)}static from(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(4);return yi.fromString("string"===typeof e?e:e.toString(),t)}static fromNumber(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(4);return yi.fromString(e.toString(),t)}static fromString(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(4);const n=e.startsWith("-"),r=e.length,i=new yi(t);for(let o=n?1:0;o<r;){const t=8<r-o?8:r-o,n=new yi(new Uint32Array([Number.parseInt(e.slice(o,o+t),10),0,0,0])),a=new yi(new Uint32Array([di[t],0,0,0]));i.times(a),i.plus(n),o+=t}return n?i.negate():i}static convertArray(e){const t=new Uint32Array(4*e.length);for(let n=-1,r=e.length;++n<r;)yi.from(e[n],new Uint32Array(t.buffer,t.byteOffset+16*n,4));return t}}class mi extends rt{constructor(e,t,n,r){super(),this.nodesIndex=-1,this.buffersIndex=-1,this.bytes=e,this.nodes=t,this.buffers=n,this.dictionaries=r}visit(e){return super.visit(e instanceof er?e.type:e)}visitNull(e){let{length:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Jn({type:e,length:t})}visitBool(e){let{length:t,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Jn({type:e,length:t,nullCount:n,nullBitmap:this.readNullBitmap(e,n),data:this.readData(e)})}visitInt(e){let{length:t,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Jn({type:e,length:t,nullCount:n,nullBitmap:this.readNullBitmap(e,n),data:this.readData(e)})}visitFloat(e){let{length:t,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Jn({type:e,length:t,nullCount:n,nullBitmap:this.readNullBitmap(e,n),data:this.readData(e)})}visitUtf8(e){let{length:t,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Jn({type:e,length:t,nullCount:n,nullBitmap:this.readNullBitmap(e,n),valueOffsets:this.readOffsets(e),data:this.readData(e)})}visitBinary(e){let{length:t,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Jn({type:e,length:t,nullCount:n,nullBitmap:this.readNullBitmap(e,n),valueOffsets:this.readOffsets(e),data:this.readData(e)})}visitFixedSizeBinary(e){let{length:t,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Jn({type:e,length:t,nullCount:n,nullBitmap:this.readNullBitmap(e,n),data:this.readData(e)})}visitDate(e){let{length:t,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Jn({type:e,length:t,nullCount:n,nullBitmap:this.readNullBitmap(e,n),data:this.readData(e)})}visitTimestamp(e){let{length:t,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Jn({type:e,length:t,nullCount:n,nullBitmap:this.readNullBitmap(e,n),data:this.readData(e)})}visitTime(e){let{length:t,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Jn({type:e,length:t,nullCount:n,nullBitmap:this.readNullBitmap(e,n),data:this.readData(e)})}visitDecimal(e){let{length:t,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Jn({type:e,length:t,nullCount:n,nullBitmap:this.readNullBitmap(e,n),data:this.readData(e)})}visitList(e){let{length:t,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Jn({type:e,length:t,nullCount:n,nullBitmap:this.readNullBitmap(e,n),valueOffsets:this.readOffsets(e),child:this.visit(e.children[0])})}visitStruct(e){let{length:t,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Jn({type:e,length:t,nullCount:n,nullBitmap:this.readNullBitmap(e,n),children:this.visitMany(e.children)})}visitUnion(e){return e.mode===t.Sparse?this.visitSparseUnion(e):this.visitDenseUnion(e)}visitDenseUnion(e){let{length:t,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Jn({type:e,length:t,nullCount:n,nullBitmap:this.readNullBitmap(e,n),typeIds:this.readTypeIds(e),valueOffsets:this.readOffsets(e),children:this.visitMany(e.children)})}visitSparseUnion(e){let{length:t,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Jn({type:e,length:t,nullCount:n,nullBitmap:this.readNullBitmap(e,n),typeIds:this.readTypeIds(e),children:this.visitMany(e.children)})}visitDictionary(e){let{length:t,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Jn({type:e,length:t,nullCount:n,nullBitmap:this.readNullBitmap(e,n),data:this.readData(e.indices),dictionary:this.readDictionary(e)})}visitInterval(e){let{length:t,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Jn({type:e,length:t,nullCount:n,nullBitmap:this.readNullBitmap(e,n),data:this.readData(e)})}visitFixedSizeList(e){let{length:t,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Jn({type:e,length:t,nullCount:n,nullBitmap:this.readNullBitmap(e,n),child:this.visit(e.children[0])})}visitMap(e){let{length:t,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Jn({type:e,length:t,nullCount:n,nullBitmap:this.readNullBitmap(e,n),valueOffsets:this.readOffsets(e),child:this.visit(e.children[0])})}nextFieldNode(){return this.nodes[++this.nodesIndex]}nextBufferRange(){return this.buffers[++this.buffersIndex]}readNullBitmap(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.nextBufferRange();return t>0&&this.readData(e,n)||new Uint8Array(0)}readOffsets(e,t){return this.readData(e,t)}readTypeIds(e,t){return this.readData(e,t)}readData(e){let{length:t,offset:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextBufferRange();return this.bytes.subarray(n,n+t)}readDictionary(e){return this.dictionaries.get(e.id)}}class bi extends mi{constructor(e,t,n,r){super(new Uint8Array(0),t,n,r),this.sources=e}readNullBitmap(e,t){let{offset:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.nextBufferRange();return t<=0?new Uint8Array(0):An(this.sources[n])}readOffsets(e){let{offset:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextBufferRange();return X(Uint8Array,X(Int32Array,this.sources[t]))}readTypeIds(e){let{offset:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextBufferRange();return X(Uint8Array,X(e.ArrayType,this.sources[t]))}readData(e){let{offset:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextBufferRange();const{sources:n}=this;return Le.isTimestamp(e)||(Le.isInt(e)||Le.isTime(e))&&64===e.bitWidth||Le.isDate(e)&&e.unit===i.MILLISECOND?X(Uint8Array,pi.convertArray(n[t])):Le.isDecimal(e)?X(Uint8Array,yi.convertArray(n[t])):Le.isBinary(e)||Le.isFixedSizeBinary(e)?function(e){const t=e.join(""),n=new Uint8Array(t.length/2);for(let r=0;r<t.length;r+=2)n[r>>1]=Number.parseInt(t.slice(r,r+2),16);return n}(n[t]):Le.isBool(e)?An(n[t]):Le.isUtf8(e)?K(n[t].join("")):X(Uint8Array,X(e.ArrayType,n[t].map((e=>+e))))}}var gi,vi,wi,_i,Si,xi,ki,Ii;!function(e){e[e.BUFFER=0]="BUFFER"}(gi||(gi={})),function(e){e[e.LZ4_FRAME=0]="LZ4_FRAME",e[e.ZSTD=1]="ZSTD"}(vi||(vi={}));class Ti{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}static getRootAsBodyCompression(e,t){return(t||new Ti).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsBodyCompression(e,t){return e.setPosition(e.position()+4),(t||new Ti).__init(e.readInt32(e.position())+e.position(),e)}codec(){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt8(this.bb_pos+e):vi.LZ4_FRAME}method(){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.readInt8(this.bb_pos+e):gi.BUFFER}static startBodyCompression(e){e.startObject(2)}static addCodec(e,t){e.addFieldInt8(0,t,vi.LZ4_FRAME)}static addMethod(e,t){e.addFieldInt8(1,t,gi.BUFFER)}static endBodyCompression(e){return e.endObject()}static createBodyCompression(e,t,n){return Ti.startBodyCompression(e),Ti.addCodec(e,t),Ti.addMethod(e,n),Ti.endBodyCompression(e)}}class Oi{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}offset(){return this.bb.readInt64(this.bb_pos)}length(){return this.bb.readInt64(this.bb_pos+8)}static sizeOf(){return 16}static createBuffer(e,t,n){return e.prep(8,16),e.writeInt64(n),e.writeInt64(t),e.offset()}}class Ai{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}length(){return this.bb.readInt64(this.bb_pos)}nullCount(){return this.bb.readInt64(this.bb_pos+8)}static sizeOf(){return 16}static createFieldNode(e,t,n){return e.prep(8,16),e.writeInt64(n),e.writeInt64(t),e.offset()}}class Ci{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}static getRootAsRecordBatch(e,t){return(t||new Ci).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsRecordBatch(e,t){return e.setPosition(e.position()+4),(t||new Ci).__init(e.readInt32(e.position())+e.position(),e)}length(){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt64(this.bb_pos+e):this.bb.createLong(0,0)}nodes(e,t){const n=this.bb.__offset(this.bb_pos,6);return n?(t||new Ai).__init(this.bb.__vector(this.bb_pos+n)+16*e,this.bb):null}nodesLength(){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__vector_len(this.bb_pos+e):0}buffers(e,t){const n=this.bb.__offset(this.bb_pos,8);return n?(t||new Oi).__init(this.bb.__vector(this.bb_pos+n)+16*e,this.bb):null}buffersLength(){const e=this.bb.__offset(this.bb_pos,8);return e?this.bb.__vector_len(this.bb_pos+e):0}compression(e){const t=this.bb.__offset(this.bb_pos,10);return t?(e||new Ti).__init(this.bb.__indirect(this.bb_pos+t),this.bb):null}static startRecordBatch(e){e.startObject(4)}static addLength(e,t){e.addFieldInt64(0,t,e.createLong(0,0))}static addNodes(e,t){e.addFieldOffset(1,t,0)}static startNodesVector(e,t){e.startVector(16,t,8)}static addBuffers(e,t){e.addFieldOffset(2,t,0)}static startBuffersVector(e,t){e.startVector(16,t,8)}static addCompression(e,t){e.addFieldOffset(3,t,0)}static endRecordBatch(e){return e.endObject()}}class Ei{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}static getRootAsDictionaryBatch(e,t){return(t||new Ei).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsDictionaryBatch(e,t){return e.setPosition(e.position()+4),(t||new Ei).__init(e.readInt32(e.position())+e.position(),e)}id(){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt64(this.bb_pos+e):this.bb.createLong(0,0)}data(e){const t=this.bb.__offset(this.bb_pos,6);return t?(e||new Ci).__init(this.bb.__indirect(this.bb_pos+t),this.bb):null}isDelta(){const e=this.bb.__offset(this.bb_pos,8);return!!e&&!!this.bb.readInt8(this.bb_pos+e)}static startDictionaryBatch(e){e.startObject(3)}static addId(e,t){e.addFieldInt64(0,t,e.createLong(0,0))}static addData(e,t){e.addFieldOffset(1,t,0)}static addIsDelta(e,t){e.addFieldInt8(2,+t,0)}static endDictionaryBatch(e){return e.endObject()}}!function(e){e[e.HALF=0]="HALF",e[e.SINGLE=1]="SINGLE",e[e.DOUBLE=2]="DOUBLE"}(wi||(wi={}));class Ni{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}static getRootAsFloatingPoint(e,t){return(t||new Ni).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsFloatingPoint(e,t){return e.setPosition(e.position()+4),(t||new Ni).__init(e.readInt32(e.position())+e.position(),e)}precision(){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt16(this.bb_pos+e):wi.HALF}static startFloatingPoint(e){e.startObject(1)}static addPrecision(e,t){e.addFieldInt16(0,t,wi.HALF)}static endFloatingPoint(e){return e.endObject()}static createFloatingPoint(e,t){return Ni.startFloatingPoint(e),Ni.addPrecision(e,t),Ni.endFloatingPoint(e)}}class Mi{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}static getRootAsDecimal(e,t){return(t||new Mi).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsDecimal(e,t){return e.setPosition(e.position()+4),(t||new Mi).__init(e.readInt32(e.position())+e.position(),e)}precision(){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt32(this.bb_pos+e):0}scale(){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.readInt32(this.bb_pos+e):0}bitWidth(){const e=this.bb.__offset(this.bb_pos,8);return e?this.bb.readInt32(this.bb_pos+e):128}static startDecimal(e){e.startObject(3)}static addPrecision(e,t){e.addFieldInt32(0,t,0)}static addScale(e,t){e.addFieldInt32(1,t,0)}static addBitWidth(e,t){e.addFieldInt32(2,t,128)}static endDecimal(e){return e.endObject()}static createDecimal(e,t,n,r){return Mi.startDecimal(e),Mi.addPrecision(e,t),Mi.addScale(e,n),Mi.addBitWidth(e,r),Mi.endDecimal(e)}}!function(e){e[e.DAY=0]="DAY",e[e.MILLISECOND=1]="MILLISECOND"}(_i||(_i={}));class Bi{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}static getRootAsDate(e,t){return(t||new Bi).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsDate(e,t){return e.setPosition(e.position()+4),(t||new Bi).__init(e.readInt32(e.position())+e.position(),e)}unit(){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt16(this.bb_pos+e):_i.MILLISECOND}static startDate(e){e.startObject(1)}static addUnit(e,t){e.addFieldInt16(0,t,_i.MILLISECOND)}static endDate(e){return e.endObject()}static createDate(e,t){return Bi.startDate(e),Bi.addUnit(e,t),Bi.endDate(e)}}!function(e){e[e.SECOND=0]="SECOND",e[e.MILLISECOND=1]="MILLISECOND",e[e.MICROSECOND=2]="MICROSECOND",e[e.NANOSECOND=3]="NANOSECOND"}(Si||(Si={}));class Li{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}static getRootAsTime(e,t){return(t||new Li).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsTime(e,t){return e.setPosition(e.position()+4),(t||new Li).__init(e.readInt32(e.position())+e.position(),e)}unit(){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt16(this.bb_pos+e):Si.MILLISECOND}bitWidth(){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.readInt32(this.bb_pos+e):32}static startTime(e){e.startObject(2)}static addUnit(e,t){e.addFieldInt16(0,t,Si.MILLISECOND)}static addBitWidth(e,t){e.addFieldInt32(1,t,32)}static endTime(e){return e.endObject()}static createTime(e,t,n){return Li.startTime(e),Li.addUnit(e,t),Li.addBitWidth(e,n),Li.endTime(e)}}class Di{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}static getRootAsTimestamp(e,t){return(t||new Di).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsTimestamp(e,t){return e.setPosition(e.position()+4),(t||new Di).__init(e.readInt32(e.position())+e.position(),e)}unit(){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt16(this.bb_pos+e):Si.SECOND}timezone(e){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__string(this.bb_pos+t,e):null}static startTimestamp(e){e.startObject(2)}static addUnit(e,t){e.addFieldInt16(0,t,Si.SECOND)}static addTimezone(e,t){e.addFieldOffset(1,t,0)}static endTimestamp(e){return e.endObject()}static createTimestamp(e,t,n){return Di.startTimestamp(e),Di.addUnit(e,t),Di.addTimezone(e,n),Di.endTimestamp(e)}}!function(e){e[e.YEAR_MONTH=0]="YEAR_MONTH",e[e.DAY_TIME=1]="DAY_TIME",e[e.MONTH_DAY_NANO=2]="MONTH_DAY_NANO"}(xi||(xi={}));class Fi{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}static getRootAsInterval(e,t){return(t||new Fi).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsInterval(e,t){return e.setPosition(e.position()+4),(t||new Fi).__init(e.readInt32(e.position())+e.position(),e)}unit(){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt16(this.bb_pos+e):xi.YEAR_MONTH}static startInterval(e){e.startObject(1)}static addUnit(e,t){e.addFieldInt16(0,t,xi.YEAR_MONTH)}static endInterval(e){return e.endObject()}static createInterval(e,t){return Fi.startInterval(e),Fi.addUnit(e,t),Fi.endInterval(e)}}!function(e){e[e.Sparse=0]="Sparse",e[e.Dense=1]="Dense"}(ki||(ki={}));class Pi{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}static getRootAsUnion(e,t){return(t||new Pi).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsUnion(e,t){return e.setPosition(e.position()+4),(t||new Pi).__init(e.readInt32(e.position())+e.position(),e)}mode(){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt16(this.bb_pos+e):ki.Sparse}typeIds(e){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt32(this.bb.__vector(this.bb_pos+t)+4*e):0}typeIdsLength(){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__vector_len(this.bb_pos+e):0}typeIdsArray(){const e=this.bb.__offset(this.bb_pos,6);return e?new Int32Array(this.bb.bytes().buffer,this.bb.bytes().byteOffset+this.bb.__vector(this.bb_pos+e),this.bb.__vector_len(this.bb_pos+e)):null}static startUnion(e){e.startObject(2)}static addMode(e,t){e.addFieldInt16(0,t,ki.Sparse)}static addTypeIds(e,t){e.addFieldOffset(1,t,0)}static createTypeIdsVector(e,t){e.startVector(4,t.length,4);for(let n=t.length-1;n>=0;n--)e.addInt32(t[n]);return e.endVector()}static startTypeIdsVector(e,t){e.startVector(4,t,4)}static endUnion(e){return e.endObject()}static createUnion(e,t,n){return Pi.startUnion(e),Pi.addMode(e,t),Pi.addTypeIds(e,n),Pi.endUnion(e)}}class Ri{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}static getRootAsFixedSizeBinary(e,t){return(t||new Ri).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsFixedSizeBinary(e,t){return e.setPosition(e.position()+4),(t||new Ri).__init(e.readInt32(e.position())+e.position(),e)}byteWidth(){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt32(this.bb_pos+e):0}static startFixedSizeBinary(e){e.startObject(1)}static addByteWidth(e,t){e.addFieldInt32(0,t,0)}static endFixedSizeBinary(e){return e.endObject()}static createFixedSizeBinary(e,t){return Ri.startFixedSizeBinary(e),Ri.addByteWidth(e,t),Ri.endFixedSizeBinary(e)}}class zi{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}static getRootAsFixedSizeList(e,t){return(t||new zi).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsFixedSizeList(e,t){return e.setPosition(e.position()+4),(t||new zi).__init(e.readInt32(e.position())+e.position(),e)}listSize(){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt32(this.bb_pos+e):0}static startFixedSizeList(e){e.startObject(1)}static addListSize(e,t){e.addFieldInt32(0,t,0)}static endFixedSizeList(e){return e.endObject()}static createFixedSizeList(e,t){return zi.startFixedSizeList(e),zi.addListSize(e,t),zi.endFixedSizeList(e)}}class Ui{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}static getRootAsMap(e,t){return(t||new Ui).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsMap(e,t){return e.setPosition(e.position()+4),(t||new Ui).__init(e.readInt32(e.position())+e.position(),e)}keysSorted(){const e=this.bb.__offset(this.bb_pos,4);return!!e&&!!this.bb.readInt8(this.bb_pos+e)}static startMap(e){e.startObject(1)}static addKeysSorted(e,t){e.addFieldInt8(0,+t,0)}static endMap(e){return e.endObject()}static createMap(e,t){return Ui.startMap(e),Ui.addKeysSorted(e,t),Ui.endMap(e)}}!function(e){e[e.NONE=0]="NONE",e[e.Schema=1]="Schema",e[e.DictionaryBatch=2]="DictionaryBatch",e[e.RecordBatch=3]="RecordBatch",e[e.Tensor=4]="Tensor",e[e.SparseTensor=5]="SparseTensor"}(Ii||(Ii={}));class ji{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}static getRootAsMessage(e,t){return(t||new ji).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsMessage(e,t){return e.setPosition(e.position()+4),(t||new ji).__init(e.readInt32(e.position())+e.position(),e)}version(){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt16(this.bb_pos+e):Nr.V1}headerType(){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.readUint8(this.bb_pos+e):Ii.NONE}header(e){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__union(e,this.bb_pos+t):null}bodyLength(){const e=this.bb.__offset(this.bb_pos,10);return e?this.bb.readInt64(this.bb_pos+e):this.bb.createLong(0,0)}customMetadata(e,t){const n=this.bb.__offset(this.bb_pos,12);return n?(t||new Pr).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*e),this.bb):null}customMetadataLength(){const e=this.bb.__offset(this.bb_pos,12);return e?this.bb.__vector_len(this.bb_pos+e):0}static startMessage(e){e.startObject(5)}static addVersion(e,t){e.addFieldInt16(0,t,Nr.V1)}static addHeaderType(e,t){e.addFieldInt8(1,t,Ii.NONE)}static addHeader(e,t){e.addFieldOffset(2,t,0)}static addBodyLength(e,t){e.addFieldInt64(3,t,e.createLong(0,0))}static addCustomMetadata(e,t){e.addFieldOffset(4,t,0)}static createCustomMetadataVector(e,t){e.startVector(4,t.length,4);for(let n=t.length-1;n>=0;n--)e.addOffset(t[n]);return e.endVector()}static startCustomMetadataVector(e,t){e.startVector(4,t,4)}static endMessage(e){return e.endObject()}static finishMessageBuffer(e,t){e.finish(t)}static finishSizePrefixedMessageBuffer(e,t){e.finish(t,void 0,!0)}static createMessage(e,t,n,r,i,o){return ji.startMessage(e),ji.addVersion(e,t),ji.addHeaderType(e,n),ji.addHeader(e,r),ji.addBodyLength(e,i),ji.addCustomMetadata(e,o),ji.endMessage(e)}}class Vi{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}static getRootAsNull(e,t){return(t||new Vi).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsNull(e,t){return e.setPosition(e.position()+4),(t||new Vi).__init(e.readInt32(e.position())+e.position(),e)}static startNull(e){e.startObject(0)}static endNull(e){return e.endObject()}static createNull(e){return Vi.startNull(e),Vi.endNull(e)}}class Wi{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}static getRootAsBinary(e,t){return(t||new Wi).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsBinary(e,t){return e.setPosition(e.position()+4),(t||new Wi).__init(e.readInt32(e.position())+e.position(),e)}static startBinary(e){e.startObject(0)}static endBinary(e){return e.endObject()}static createBinary(e){return Wi.startBinary(e),Wi.endBinary(e)}}class $i{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}static getRootAsBool(e,t){return(t||new $i).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsBool(e,t){return e.setPosition(e.position()+4),(t||new $i).__init(e.readInt32(e.position())+e.position(),e)}static startBool(e){e.startObject(0)}static endBool(e){return e.endObject()}static createBool(e){return $i.startBool(e),$i.endBool(e)}}class Hi{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}static getRootAsUtf8(e,t){return(t||new Hi).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsUtf8(e,t){return e.setPosition(e.position()+4),(t||new Hi).__init(e.readInt32(e.position())+e.position(),e)}static startUtf8(e){e.startObject(0)}static endUtf8(e){return e.endObject()}static createUtf8(e){return Hi.startUtf8(e),Hi.endUtf8(e)}}class Yi{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}static getRootAsList(e,t){return(t||new Yi).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsList(e,t){return e.setPosition(e.position()+4),(t||new Yi).__init(e.readInt32(e.position())+e.position(),e)}static startList(e){e.startObject(0)}static endList(e){return e.endObject()}static createList(e){return Yi.startList(e),Yi.endList(e)}}class Ki{constructor(){this.bb=null,this.bb_pos=0}__init(e,t){return this.bb_pos=e,this.bb=t,this}static getRootAsStruct_(e,t){return(t||new Ki).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsStruct_(e,t){return e.setPosition(e.position()+4),(t||new Ki).__init(e.readInt32(e.position())+e.position(),e)}static startStruct_(e){e.startObject(0)}static endStruct_(e){return e.endObject()}static createStruct_(e){return Ki.startStruct_(e),Ki.endStruct_(e)}}var Qi=Cr;const qi=new class extends rt{visit(e,t){return null==e||null==t?void 0:super.visit(e,t)}visitNull(e,t){return Vi.startNull(t),Vi.endNull(t)}visitInt(e,t){return Rr.startInt(t),Rr.addBitWidth(t,e.bitWidth),Rr.addIsSigned(t,e.isSigned),Rr.endInt(t)}visitFloat(e,t){return Ni.startFloatingPoint(t),Ni.addPrecision(t,e.precision),Ni.endFloatingPoint(t)}visitBinary(e,t){return Wi.startBinary(t),Wi.endBinary(t)}visitBool(e,t){return $i.startBool(t),$i.endBool(t)}visitUtf8(e,t){return Hi.startUtf8(t),Hi.endUtf8(t)}visitDecimal(e,t){return Mi.startDecimal(t),Mi.addScale(t,e.scale),Mi.addPrecision(t,e.precision),Mi.addBitWidth(t,e.bitWidth),Mi.endDecimal(t)}visitDate(e,t){return Bi.startDate(t),Bi.addUnit(t,e.unit),Bi.endDate(t)}visitTime(e,t){return Li.startTime(t),Li.addUnit(t,e.unit),Li.addBitWidth(t,e.bitWidth),Li.endTime(t)}visitTimestamp(e,t){const n=e.timezone&&t.createString(e.timezone)||void 0;return Di.startTimestamp(t),Di.addUnit(t,e.unit),void 0!==n&&Di.addTimezone(t,n),Di.endTimestamp(t)}visitInterval(e,t){return Fi.startInterval(t),Fi.addUnit(t,e.unit),Fi.endInterval(t)}visitList(e,t){return Yi.startList(t),Yi.endList(t)}visitStruct(e,t){return Ki.startStruct_(t),Ki.endStruct_(t)}visitUnion(e,t){Pi.startTypeIdsVector(t,e.typeIds.length);const n=Pi.createTypeIdsVector(t,e.typeIds);return Pi.startUnion(t),Pi.addMode(t,e.mode),Pi.addTypeIds(t,n),Pi.endUnion(t)}visitDictionary(e,t){const n=this.visit(e.indices,t);return zr.startDictionaryEncoding(t),zr.addId(t,new Qi(e.id,0)),zr.addIsOrdered(t,e.isOrdered),void 0!==n&&zr.addIndexType(t,n),zr.endDictionaryEncoding(t)}visitFixedSizeBinary(e,t){return Ri.startFixedSizeBinary(t),Ri.addByteWidth(t,e.byteWidth),Ri.endFixedSizeBinary(t)}visitFixedSizeList(e,t){return zi.startFixedSizeList(t),zi.addListSize(t,e.listSize),zi.endFixedSizeList(t)}visitMap(e,t){return Ui.startMap(t),Ui.addKeysSorted(t,e.keysSorted),Ui.endMap(t)}};function Gi(e){return new so(e.count,Ji(e.columns),Zi(e.columns))}function Xi(e,t){return(e.children||[]).filter(Boolean).map((e=>er.fromJSON(e,t)))}function Ji(e){return(e||[]).reduce(((e,t)=>{return[...e,new co(t.count,(n=t.VALIDITY,(n||[]).reduce(((e,t)=>e+ +(0===t)),0))),...Ji(t.children)];var n}),[])}function Zi(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];for(let n=-1,r=(e||[]).length;++n<r;){const r=e[n];r.VALIDITY&&t.push(new uo(t.length,r.VALIDITY.length)),r.TYPE&&t.push(new uo(t.length,r.TYPE.length)),r.OFFSET&&t.push(new uo(t.length,r.OFFSET.length)),r.DATA&&t.push(new uo(t.length,r.DATA.length)),t=Zi(r.children,t)}return t}function eo(e){return new Map(Object.entries(e||{}))}function to(e){return new Fe(e.isSigned,e.bitWidth)}function no(e,n){const s=e.type.name;switch(s){case"NONE":case"null":return new De;case"binary":return new ze;case"utf8":return new Ue;case"bool":return new je;case"list":return new Ke((n||[])[0]);case"struct":case"struct_":return new Qe(n||[])}switch(s){case"int":{const t=e.type;return new Fe(t.isSigned,t.bitWidth)}case"floatingpoint":{const t=e.type;return new Re(r[t.precision])}case"decimal":{const t=e.type;return new Ve(t.scale,t.precision,t.bitWidth)}case"date":{const t=e.type;return new We(i[t.unit])}case"time":{const t=e.type;return new $e(o[t.unit],t.bitWidth)}case"timestamp":{const t=e.type;return new He(o[t.unit],t.timezone)}case"interval":{const t=e.type;return new Ye(a[t.unit])}case"union":{const r=e.type;return new qe(t[r.mode],r.typeIds||[],n||[])}case"fixedsizebinary":{const t=e.type;return new Ge(t.byteWidth)}case"fixedsizelist":{const t=e.type;return new Xe(t.listSize,(n||[])[0])}case"map":{const t=e.type;return new Je((n||[])[0],t.keysSorted)}}throw new Error('Unrecognized type: "'.concat(s,'"'))}var ro=Cr,io=Fr,oo=Dr;class ao{constructor(e,t,n,r){this._version=t,this._headerType=n,this.body=new Uint8Array(0),r&&(this._createHeader=()=>r),this._bodyLength="number"===typeof e?e:e.low}static fromJSON(t,n){const r=new ao(0,e.V4,n);return r._createHeader=function(e,t){return()=>{switch(t){case s.Schema:return Zn.fromJSON(e);case s.RecordBatch:return so.fromJSON(e);case s.DictionaryBatch:return lo.fromJSON(e)}throw new Error("Unrecognized Message type: { name: ".concat(s[t],", type: ").concat(t," }"))}}(t,n),r}static decode(e){e=new oo(Z(e));const t=ji.getRootAsMessage(e),n=t.bodyLength(),r=t.version(),i=t.headerType(),o=new ao(n,r,i);return o._createHeader=function(e,t){return()=>{switch(t){case s.Schema:return Zn.decode(e.header(new jr));case s.RecordBatch:return so.decode(e.header(new Ci),e.version());case s.DictionaryBatch:return lo.decode(e.header(new Ei),e.version())}throw new Error("Unrecognized Message type: { name: ".concat(s[t],", type: ").concat(t," }"))}}(t,i),o}static encode(t){const n=new io;let r=-1;return t.isSchema()?r=Zn.encode(n,t.header()):t.isRecordBatch()?r=so.encode(n,t.header()):t.isDictionaryBatch()&&(r=lo.encode(n,t.header())),ji.startMessage(n),ji.addVersion(n,e.V4),ji.addHeader(n,r),ji.addHeaderType(n,t.headerType),ji.addBodyLength(n,new ro(t.bodyLength,0)),ji.finishMessageBuffer(n,ji.endMessage(n)),n.asUint8Array()}static from(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(t instanceof Zn)return new ao(0,e.V4,s.Schema,t);if(t instanceof so)return new ao(n,e.V4,s.RecordBatch,t);if(t instanceof lo)return new ao(n,e.V4,s.DictionaryBatch,t);throw new Error("Unrecognized Message header: ".concat(t))}get type(){return this.headerType}get version(){return this._version}get headerType(){return this._headerType}get bodyLength(){return this._bodyLength}header(){return this._createHeader()}isSchema(){return this.headerType===s.Schema}isRecordBatch(){return this.headerType===s.RecordBatch}isDictionaryBatch(){return this.headerType===s.DictionaryBatch}}class so{constructor(e,t,n){this._nodes=t,this._buffers=n,this._length="number"===typeof e?e:e.low}get nodes(){return this._nodes}get length(){return this._length}get buffers(){return this._buffers}}class lo{constructor(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this._data=e,this._isDelta=n,this._id="number"===typeof t?t:t.low}get id(){return this._id}get data(){return this._data}get isDelta(){return this._isDelta}get length(){return this.data.length}get nodes(){return this.data.nodes}get buffers(){return this.data.buffers}}class uo{constructor(e,t){this.offset="number"===typeof e?e:e.low,this.length="number"===typeof t?t:t.low}}class co{constructor(e,t){this.length="number"===typeof e?e:e.low,this.nullCount="number"===typeof t?t:t.low}}function fo(e,t){const n=[];for(let r,i=-1,o=-1,a=e.childrenLength();++i<a;)(r=e.children(i))&&(n[++o]=er.decode(r,t));return n}function ho(e){const t=new Map;if(e)for(let n,r,i=-1,o=Math.trunc(e.customMetadataLength());++i<o;)(n=e.customMetadata(i))&&null!=(r=n.key())&&t.set(r,n.value());return t}function po(e){return new Fe(e.isSigned(),e.bitWidth())}function yo(e,t){const n=e.typeType();switch(n){case Lr.NONE:case Lr.Null:return new De;case Lr.Binary:return new ze;case Lr.Utf8:return new Ue;case Lr.Bool:return new je;case Lr.List:return new Ke((t||[])[0]);case Lr.Struct_:return new Qe(t||[])}switch(n){case Lr.Int:{const t=e.type(new Rr);return new Fe(t.isSigned(),t.bitWidth())}case Lr.FloatingPoint:{const t=e.type(new Ni);return new Re(t.precision())}case Lr.Decimal:{const t=e.type(new Mi);return new Ve(t.scale(),t.precision(),t.bitWidth())}case Lr.Date:{const t=e.type(new Bi);return new We(t.unit())}case Lr.Time:{const t=e.type(new Li);return new $e(t.unit(),t.bitWidth())}case Lr.Timestamp:{const t=e.type(new Di);return new He(t.unit(),t.timezone())}case Lr.Interval:{const t=e.type(new Fi);return new Ye(t.unit())}case Lr.Union:{const n=e.type(new Pi);return new qe(n.mode(),n.typeIdsArray()||[],t||[])}case Lr.FixedSizeBinary:{const t=e.type(new Ri);return new Ge(t.byteWidth())}case Lr.FixedSizeList:{const n=e.type(new zi);return new Xe(n.listSize(),(t||[])[0])}case Lr.Map:{const n=e.type(new Ui);return new Je((t||[])[0],n.keysSorted())}}throw new Error('Unrecognized type: "'.concat(Lr[n],'" (').concat(n,")"))}er.encode=function(e,t){let n=-1,r=-1,i=-1;const o=t.type;let a=t.typeId;Le.isDictionary(o)?(a=o.dictionary.typeId,i=qi.visit(o,e),r=qi.visit(o.dictionary,e)):r=qi.visit(o,e);const s=(o.children||[]).map((t=>er.encode(e,t))),l=Ur.createChildrenVector(e,s),u=t.metadata&&t.metadata.size>0?Ur.createCustomMetadataVector(e,[...t.metadata].map((t=>{let[n,r]=t;const i=e.createString("".concat(n)),o=e.createString("".concat(r));return Pr.startKeyValue(e),Pr.addKey(e,i),Pr.addValue(e,o),Pr.endKeyValue(e)}))):-1;t.name&&(n=e.createString(t.name));Ur.startField(e),Ur.addType(e,r),Ur.addTypeType(e,a),Ur.addChildren(e,l),Ur.addNullable(e,!!t.nullable),-1!==n&&Ur.addName(e,n);-1!==i&&Ur.addDictionary(e,i);-1!==u&&Ur.addCustomMetadata(e,u);return Ur.endField(e)},er.decode=function(e,t){let n,r,i,o,a,s;t&&(s=e.dictionary())?t.has(n=s.id().low)?(o=(o=s.indexType())?po(o):new Pe,a=new tt(t.get(n),o,n,s.isOrdered()),r=new er(e.name(),a,e.nullable(),ho(e))):(o=(o=s.indexType())?po(o):new Pe,t.set(n,i=yo(e,fo(e,t))),a=new tt(i,o,n,s.isOrdered()),r=new er(e.name(),a,e.nullable(),ho(e))):(i=yo(e,fo(e,t)),r=new er(e.name(),i,e.nullable(),ho(e)));return r||null},er.fromJSON=function(e,t){let n,r,i,o,a,s;return t&&(o=e.dictionary)?t.has(n=o.id)?(r=(r=o.indexType)?to(r):new Pe,s=new tt(t.get(n),r,n,o.isOrdered),i=new er(e.name,s,e.nullable,eo(e.customMetadata))):(r=(r=o.indexType)?to(r):new Pe,t.set(n,a=no(e,Xi(e,t))),s=new tt(a,r,n,o.isOrdered),i=new er(e.name,s,e.nullable,eo(e.customMetadata))):(a=no(e,Xi(e,t)),i=new er(e.name,a,e.nullable,eo(e.customMetadata))),i||null},Zn.encode=function(e,t){const n=t.fields.map((t=>er.encode(e,t)));jr.startFieldsVector(e,n.length);const r=jr.createFieldsVector(e,n),i=t.metadata&&t.metadata.size>0?jr.createCustomMetadataVector(e,[...t.metadata].map((t=>{let[n,r]=t;const i=e.createString("".concat(n)),o=e.createString("".concat(r));return Pr.startKeyValue(e),Pr.addKey(e,i),Pr.addValue(e,o),Pr.endKeyValue(e)}))):-1;jr.startSchema(e),jr.addFields(e,r),jr.addEndianness(e,mo?Mr.Little:Mr.Big),-1!==i&&jr.addCustomMetadata(e,i);return jr.endSchema(e)},Zn.decode=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Map;const n=function(e,t){const n=[];for(let r,i=-1,o=-1,a=e.fieldsLength();++i<a;)(r=e.fields(i))&&(n[++o]=er.decode(r,t));return n}(e,t);return new Zn(n,ho(e),t)},Zn.fromJSON=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Map;return new Zn(function(e,t){return(e.fields||[]).filter(Boolean).map((e=>er.fromJSON(e,t)))}(e,t),eo(e.customMetadata),t)},so.encode=function(e,t){const n=t.nodes||[],r=t.buffers||[];Ci.startNodesVector(e,n.length);for(const a of n.slice().reverse())co.encode(e,a);const i=e.endVector();Ci.startBuffersVector(e,r.length);for(const a of r.slice().reverse())uo.encode(e,a);const o=e.endVector();return Ci.startRecordBatch(e),Ci.addLength(e,new ro(t.length,0)),Ci.addNodes(e,i),Ci.addBuffers(e,o),Ci.endRecordBatch(e)},so.decode=function(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.V4;if(null!==t.compression())throw new Error("Record batch compression not implemented");return new so(t.length(),function(e){const t=[];for(let n,r=-1,i=-1,o=e.nodesLength();++r<o;)(n=e.nodes(r))&&(t[++i]=co.decode(n));return t}(t),function(t,n){const r=[];for(let i,o=-1,a=-1,s=t.buffersLength();++o<s;)(i=t.buffers(o))&&(n<e.V4&&(i.bb_pos+=8*(o+1)),r[++a]=uo.decode(i));return r}(t,n))},so.fromJSON=Gi,lo.encode=function(e,t){const n=so.encode(e,t.data);return Ei.startDictionaryBatch(e),Ei.addId(e,new ro(t.id,0)),Ei.addIsDelta(e,t.isDelta),Ei.addData(e,n),Ei.endDictionaryBatch(e)},lo.decode=function(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.V4;return new lo(so.decode(t.data(),n),t.id(),t.isDelta())},lo.fromJSON=function(e){return new lo(Gi(e.data),e.id,e.isDelta)},co.encode=function(e,t){return Ai.createFieldNode(e,new ro(t.length,0),new ro(t.nullCount,0))},co.decode=function(e){return new co(e.length(),e.nullCount())},uo.encode=function(e,t){return Oi.createBuffer(e,new ro(t.offset,0),new ro(t.length,0))},uo.decode=function(e){return new uo(e.offset(),e.length())};const mo=(()=>{const e=new ArrayBuffer(2);return new DataView(e).setInt16(0,256,!0),256===new Int16Array(e)[0]})(),bo=e=>"Expected ".concat(s[e]," Message in stream, but was null or length 0."),go=e=>"Header pointer of flatbuffer-encoded ".concat(s[e]," Message is null or length 0."),vo=(e,t)=>"Expected to read ".concat(e," metadata bytes, but only read ").concat(t,"."),wo=(e,t)=>"Expected to read ".concat(e," bytes for message body, but only read ").concat(t,".");class _o{constructor(e){this.source=e instanceof ii?e:new ii(e)}[Symbol.iterator](){return this}next(){let e;return(e=this.readMetadataLength()).done||-1===e.value&&(e=this.readMetadataLength()).done||(e=this.readMetadata(e.value)).done?Zr:e}throw(e){return this.source.throw(e)}return(e){return this.source.return(e)}readMessage(e){let t;if((t=this.next()).done)return null;if(null!=e&&t.value.headerType!==e)throw new Error(bo(e));return t.value}readMessageBody(e){if(e<=0)return new Uint8Array(0);const t=Z(this.source.read(e));if(t.byteLength<e)throw new Error(wo(e,t.byteLength));return t.byteOffset%8===0&&t.byteOffset+t.byteLength<=t.buffer.byteLength?t:t.slice()}readSchema(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];const t=s.Schema,n=this.readMessage(t),r=null===n||void 0===n?void 0:n.header();if(e&&!r)throw new Error(go(t));return r}readMetadataLength(){const e=this.source.read(ko),t=e&&new Dr(e),n=(null===t||void 0===t?void 0:t.readInt32(0))||0;return{done:0===n,value:n}}readMetadata(e){const t=this.source.read(e);if(!t)return Zr;if(t.byteLength<e)throw new Error(vo(e,t.byteLength));return{done:!1,value:ao.decode(t)}}}class So{constructor(e,t){this.source=e instanceof oi?e:N(e)?new ui(e,t):new oi(e)}[Symbol.asyncIterator](){return this}next(){return R(this,void 0,void 0,(function*(){let e;return(e=yield this.readMetadataLength()).done||-1===e.value&&(e=yield this.readMetadataLength()).done||(e=yield this.readMetadata(e.value)).done?Zr:e}))}throw(e){return R(this,void 0,void 0,(function*(){return yield this.source.throw(e)}))}return(e){return R(this,void 0,void 0,(function*(){return yield this.source.return(e)}))}readMessage(e){return R(this,void 0,void 0,(function*(){let t;if((t=yield this.next()).done)return null;if(null!=e&&t.value.headerType!==e)throw new Error(bo(e));return t.value}))}readMessageBody(e){return R(this,void 0,void 0,(function*(){if(e<=0)return new Uint8Array(0);const t=Z(yield this.source.read(e));if(t.byteLength<e)throw new Error(wo(e,t.byteLength));return t.byteOffset%8===0&&t.byteOffset+t.byteLength<=t.buffer.byteLength?t:t.slice()}))}readSchema(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return R(this,void 0,void 0,(function*(){const t=s.Schema,n=yield this.readMessage(t),r=null===n||void 0===n?void 0:n.header();if(e&&!r)throw new Error(go(t));return r}))}readMetadataLength(){return R(this,void 0,void 0,(function*(){const e=yield this.source.read(ko),t=e&&new Dr(e),n=(null===t||void 0===t?void 0:t.readInt32(0))||0;return{done:0===n,value:n}}))}readMetadata(e){return R(this,void 0,void 0,(function*(){const t=yield this.source.read(e);if(!t)return Zr;if(t.byteLength<e)throw new Error(vo(e,t.byteLength));return{done:!1,value:ao.decode(t)}}))}}class xo extends _o{constructor(e){super(new Uint8Array(0)),this._schema=!1,this._body=[],this._batchIndex=0,this._dictionaryIndex=0,this._json=e instanceof ei?e:new ei(e)}next(){const{_json:e}=this;if(!this._schema){this._schema=!0;return{done:!1,value:ao.fromJSON(e.schema,s.Schema)}}if(this._dictionaryIndex<e.dictionaries.length){const t=e.dictionaries[this._dictionaryIndex++];this._body=t.data.columns;return{done:!1,value:ao.fromJSON(t,s.DictionaryBatch)}}if(this._batchIndex<e.batches.length){const t=e.batches[this._batchIndex++];this._body=t.columns;return{done:!1,value:ao.fromJSON(t,s.RecordBatch)}}return this._body=[],Zr}readMessageBody(e){return function e(t){return(t||[]).reduce(((t,n)=>[...t,...n.VALIDITY&&[n.VALIDITY]||[],...n.TYPE&&[n.TYPE]||[],...n.OFFSET&&[n.OFFSET]||[],...n.DATA&&[n.DATA]||[],...e(n.children)]),[])}(this._body)}readMessage(e){let t;if((t=this.next()).done)return null;if(null!=e&&t.value.headerType!==e)throw new Error(bo(e));return t.value}readSchema(){const e=s.Schema,t=this.readMessage(e),n=null===t||void 0===t?void 0:t.header();if(!t||!n)throw new Error(go(e));return n}}const ko=4,Io="ARROW1",To=new Uint8Array(6);for(let n=0;n<6;n+=1)To[n]=Io.codePointAt(n);function Oo(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;for(let n=-1,r=To.length;++n<r;)if(To[n]!==e[t+n])return!1;return!0}const Ao=To.length,Co=Ao+ko,Eo=2*Ao+ko;class No extends ti{constructor(e){super(),this._impl=e}get closed(){return this._impl.closed}get schema(){return this._impl.schema}get autoDestroy(){return this._impl.autoDestroy}get dictionaries(){return this._impl.dictionaries}get numDictionaries(){return this._impl.numDictionaries}get numRecordBatches(){return this._impl.numRecordBatches}get footer(){return this._impl.isFile()?this._impl.footer:null}isSync(){return this._impl.isSync()}isAsync(){return this._impl.isAsync()}isFile(){return this._impl.isFile()}isStream(){return this._impl.isStream()}next(){return this._impl.next()}throw(e){return this._impl.throw(e)}return(e){return this._impl.return(e)}cancel(){return this._impl.cancel()}reset(e){return this._impl.reset(e),this._DOMStream=void 0,this._nodeStream=void 0,this}open(e){const t=this._impl.open(e);return T(t)?t.then((()=>this)):this}readRecordBatch(e){return this._impl.isFile()?this._impl.readRecordBatch(e):null}[Symbol.iterator](){return this._impl[Symbol.iterator]()}[Symbol.asyncIterator](){return this._impl[Symbol.asyncIterator]()}toDOMStream(){return qr.toDOMStream(this.isSync()?{[Symbol.iterator]:()=>this}:{[Symbol.asyncIterator]:()=>this})}toNodeStream(){return qr.toNodeStream(this.isSync()?{[Symbol.iterator]:()=>this}:{[Symbol.asyncIterator]:()=>this},{objectMode:!0})}static throughNode(e){throw new Error('"throughNode" not available in this environment')}static throughDOM(e,t){throw new Error('"throughDOM" not available in this environment')}static from(e){return e instanceof No?e:C(e)?function(e){return new Mo(new jo(e))}(e):N(e)?function(e){return R(this,void 0,void 0,(function*(){const{size:t}=yield e.stat(),n=new ui(e,t);return t>=Eo&&Oo(yield n.readAt(0,Ao+7&-8))?new Do(new Uo(n)):new Bo(new Ro(n))}))}(e):T(e)?(()=>R(this,void 0,void 0,(function*(){return yield No.from(yield e)})))():M(e)||L(e)||D(e)||A(e)?function(e){return R(this,void 0,void 0,(function*(){const t=yield e.peek(Ao+7&-8);return t&&t.byteLength>=4?Oo(t)?new Lo(new zo(yield e.read())):new Bo(new Ro(e)):new Bo(new Ro(function(){return j(this,arguments,(function*(){}))}()))}))}(new oi(e)):function(e){const t=e.peek(Ao+7&-8);return t&&t.byteLength>=4?Oo(t)?new Lo(new zo(e.read())):new Mo(new Po(e)):new Mo(new Po(function*(){}()))}(new ii(e))}static readAll(e){return e instanceof No?e.isSync()?Wo(e):$o(e):C(e)||ArrayBuffer.isView(e)||O(e)||E(e)?Wo(e):$o(e)}}class Mo extends No{constructor(e){super(e),this._impl=e}readAll(){return[...this]}[Symbol.iterator](){return this._impl[Symbol.iterator]()}[Symbol.asyncIterator](){return j(this,arguments,(function*(){yield U(yield*V(W(this[Symbol.iterator]())))}))}}class Bo extends No{constructor(e){super(e),this._impl=e}readAll(){var e,t;return R(this,void 0,void 0,(function*(){const n=new Array;try{for(var r,i=W(this);!(r=yield i.next()).done;){const e=r.value;n.push(e)}}catch(o){e={error:o}}finally{try{r&&!r.done&&(t=i.return)&&(yield t.call(i))}finally{if(e)throw e.error}}return n}))}[Symbol.iterator](){throw new Error("AsyncRecordBatchStreamReader is not Iterable")}[Symbol.asyncIterator](){return this._impl[Symbol.asyncIterator]()}}class Lo extends Mo{constructor(e){super(e),this._impl=e}}class Do extends Bo{constructor(e){super(e),this._impl=e}}class Fo{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Map;this.closed=!1,this.autoDestroy=!0,this._dictionaryIndex=0,this._recordBatchIndex=0,this.dictionaries=e}get numDictionaries(){return this._dictionaryIndex}get numRecordBatches(){return this._recordBatchIndex}isSync(){return!1}isAsync(){return!1}isFile(){return!1}isStream(){return!1}reset(e){return this._dictionaryIndex=0,this._recordBatchIndex=0,this.schema=e,this.dictionaries=new Map,this}_loadRecordBatch(e,t){const n=this._loadVectors(e,t,this.schema.fields),r=Jn({type:new Qe(this.schema.fields),length:e.length,children:n});return new br(this.schema,r)}_loadDictionaryBatch(e,t){const{id:n,isDelta:r}=e,{dictionaries:i,schema:o}=this,a=i.get(n);if(r||!a){const i=o.dictionaries.get(n),s=this._loadVectors(e.data,t,[i]);return(a&&r?a.concat(new Qn(s)):new Qn(s)).memoize()}return a.memoize()}_loadVectors(e,t,n){return new mi(t,e.nodes,e.buffers,this.dictionaries).visitMany(n)}}class Po extends Fo{constructor(e,t){super(t),this._reader=C(e)?new xo(this._handle=e):new _o(this._handle=e)}isSync(){return!0}isStream(){return!0}[Symbol.iterator](){return this}cancel(){!this.closed&&(this.closed=!0)&&(this.reset()._reader.return(),this._reader=null,this.dictionaries=null)}open(e){return this.closed||(this.autoDestroy=Vo(this,e),this.schema||(this.schema=this._reader.readSchema())||this.cancel()),this}throw(e){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset()._reader.throw(e):Zr}return(e){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset()._reader.return(e):Zr}next(){if(this.closed)return Zr;let e;const{_reader:t}=this;for(;e=this._readNextMessageAndValidate();)if(e.isSchema())this.reset(e.header());else{if(e.isRecordBatch()){this._recordBatchIndex++;const n=e.header(),r=t.readMessageBody(e.bodyLength);return{done:!1,value:this._loadRecordBatch(n,r)}}if(e.isDictionaryBatch()){this._dictionaryIndex++;const n=e.header(),r=t.readMessageBody(e.bodyLength),i=this._loadDictionaryBatch(n,r);this.dictionaries.set(n.id,i)}}return this.schema&&0===this._recordBatchIndex?(this._recordBatchIndex++,{done:!1,value:new wr(this.schema)}):this.return()}_readNextMessageAndValidate(e){return this._reader.readMessage(e)}}class Ro extends Fo{constructor(e,t){super(t),this._reader=new So(this._handle=e)}isAsync(){return!0}isStream(){return!0}[Symbol.asyncIterator](){return this}cancel(){return R(this,void 0,void 0,(function*(){!this.closed&&(this.closed=!0)&&(yield this.reset()._reader.return(),this._reader=null,this.dictionaries=null)}))}open(e){return R(this,void 0,void 0,(function*(){return this.closed||(this.autoDestroy=Vo(this,e),this.schema||(this.schema=yield this._reader.readSchema())||(yield this.cancel())),this}))}throw(e){return R(this,void 0,void 0,(function*(){return!this.closed&&this.autoDestroy&&(this.closed=!0)?yield this.reset()._reader.throw(e):Zr}))}return(e){return R(this,void 0,void 0,(function*(){return!this.closed&&this.autoDestroy&&(this.closed=!0)?yield this.reset()._reader.return(e):Zr}))}next(){return R(this,void 0,void 0,(function*(){if(this.closed)return Zr;let e;const{_reader:t}=this;for(;e=yield this._readNextMessageAndValidate();)if(e.isSchema())yield this.reset(e.header());else{if(e.isRecordBatch()){this._recordBatchIndex++;const n=e.header(),r=yield t.readMessageBody(e.bodyLength);return{done:!1,value:this._loadRecordBatch(n,r)}}if(e.isDictionaryBatch()){this._dictionaryIndex++;const n=e.header(),r=yield t.readMessageBody(e.bodyLength),i=this._loadDictionaryBatch(n,r);this.dictionaries.set(n.id,i)}}return this.schema&&0===this._recordBatchIndex?(this._recordBatchIndex++,{done:!1,value:new wr(this.schema)}):yield this.return()}))}_readNextMessageAndValidate(e){return R(this,void 0,void 0,(function*(){return yield this._reader.readMessage(e)}))}}class zo extends Po{constructor(e,t){super(e instanceof li?e:new li(e),t)}get footer(){return this._footer}get numDictionaries(){return this._footer?this._footer.numDictionaries:0}get numRecordBatches(){return this._footer?this._footer.numRecordBatches:0}isSync(){return!0}isFile(){return!0}open(e){if(!this.closed&&!this._footer){this.schema=(this._footer=this._readFooter()).schema;for(const e of this._footer.dictionaryBatches())e&&this._readDictionaryBatch(this._dictionaryIndex++)}return super.open(e)}readRecordBatch(e){var t;if(this.closed)return null;this._footer||this.open();const n=null===(t=this._footer)||void 0===t?void 0:t.getRecordBatch(e);if(n&&this._handle.seek(n.offset)){const e=this._reader.readMessage(s.RecordBatch);if(null===e||void 0===e?void 0:e.isRecordBatch()){const t=e.header(),n=this._reader.readMessageBody(e.bodyLength);return this._loadRecordBatch(t,n)}}return null}_readDictionaryBatch(e){var t;const n=null===(t=this._footer)||void 0===t?void 0:t.getDictionaryBatch(e);if(n&&this._handle.seek(n.offset)){const e=this._reader.readMessage(s.DictionaryBatch);if(null===e||void 0===e?void 0:e.isDictionaryBatch()){const t=e.header(),n=this._reader.readMessageBody(e.bodyLength),r=this._loadDictionaryBatch(t,n);this.dictionaries.set(t.id,r)}}}_readFooter(){const{_handle:e}=this,t=e.size-Co,n=e.readInt32(t),r=e.readAt(t-n,n);return Yr.decode(r)}_readNextMessageAndValidate(e){var t;if(this._footer||this.open(),this._footer&&this._recordBatchIndex<this.numRecordBatches){const n=null===(t=this._footer)||void 0===t?void 0:t.getRecordBatch(this._recordBatchIndex);if(n&&this._handle.seek(n.offset))return this._reader.readMessage(e)}return null}}class Uo extends Ro{constructor(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];const i="number"!==typeof n[0]?n.shift():void 0,o=n[0]instanceof Map?n.shift():void 0;super(e instanceof ui?e:new ui(e,i),o)}get footer(){return this._footer}get numDictionaries(){return this._footer?this._footer.numDictionaries:0}get numRecordBatches(){return this._footer?this._footer.numRecordBatches:0}isFile(){return!0}isAsync(){return!0}open(e){const t=Object.create(null,{open:{get:()=>super.open}});return R(this,void 0,void 0,(function*(){if(!this.closed&&!this._footer){this.schema=(this._footer=yield this._readFooter()).schema;for(const e of this._footer.dictionaryBatches())e&&(yield this._readDictionaryBatch(this._dictionaryIndex++))}return yield t.open.call(this,e)}))}readRecordBatch(e){var t;return R(this,void 0,void 0,(function*(){if(this.closed)return null;this._footer||(yield this.open());const n=null===(t=this._footer)||void 0===t?void 0:t.getRecordBatch(e);if(n&&(yield this._handle.seek(n.offset))){const e=yield this._reader.readMessage(s.RecordBatch);if(null===e||void 0===e?void 0:e.isRecordBatch()){const t=e.header(),n=yield this._reader.readMessageBody(e.bodyLength);return this._loadRecordBatch(t,n)}}return null}))}_readDictionaryBatch(e){var t;return R(this,void 0,void 0,(function*(){const n=null===(t=this._footer)||void 0===t?void 0:t.getDictionaryBatch(e);if(n&&(yield this._handle.seek(n.offset))){const e=yield this._reader.readMessage(s.DictionaryBatch);if(null===e||void 0===e?void 0:e.isDictionaryBatch()){const t=e.header(),n=yield this._reader.readMessageBody(e.bodyLength),r=this._loadDictionaryBatch(t,n);this.dictionaries.set(t.id,r)}}}))}_readFooter(){return R(this,void 0,void 0,(function*(){const{_handle:e}=this;e._pending&&(yield e._pending);const t=e.size-Co,n=yield e.readInt32(t),r=yield e.readAt(t-n,n);return Yr.decode(r)}))}_readNextMessageAndValidate(e){return R(this,void 0,void 0,(function*(){if(this._footer||(yield this.open()),this._footer&&this._recordBatchIndex<this.numRecordBatches){const t=this._footer.getRecordBatch(this._recordBatchIndex);if(t&&(yield this._handle.seek(t.offset)))return yield this._reader.readMessage(e)}return null}))}}class jo extends Po{constructor(e,t){super(e,t)}_loadVectors(e,t,n){return new bi(t,e.nodes,e.buffers,this.dictionaries).visitMany(n)}}function Vo(e,t){return t&&"boolean"===typeof t.autoDestroy?t.autoDestroy:e.autoDestroy}function*Wo(e){const t=No.from(e);try{if(!t.open({autoDestroy:!1}).closed)do{yield t}while(!t.reset().open().closed)}finally{t.cancel()}}function $o(e){return j(this,arguments,(function*(){const t=yield U(No.from(e));try{if(!(yield U(t.open({autoDestroy:!1}))).closed)do{yield yield U(t)}while(!(yield U(t.reset().open())).closed)}finally{yield U(t.cancel())}}))}class Ho extends rt{constructor(){super(),this._byteLength=0,this._nodes=[],this._buffers=[],this._bufferRegions=[]}static assemble(){const e=t=>t.flatMap((t=>Array.isArray(t)?e(t):t instanceof br?t.data.children:t.data)),t=new Ho;for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return t.visitMany(e(r)),t}visit(e){if(e instanceof Qn)return this.visitMany(e.data),this;const{type:t}=e;if(!Le.isDictionary(t)){const{length:n,nullCount:r}=e;if(n>2147483647)throw new RangeError("Cannot write arrays larger than 2^31 - 1 in length");Le.isNull(t)||Yo.call(this,r<=0?new Uint8Array(0):On(e.offset,n,e.nullBitmap)),this.nodes.push(new co(n,r))}return super.visit(e)}visitNull(e){return this}visitDictionary(e){return this.visit(e.clone(e.type.indices))}get nodes(){return this._nodes}get buffers(){return this._buffers}get byteLength(){return this._byteLength}get bufferRegions(){return this._bufferRegions}}function Yo(e){const t=e.byteLength+7&-8;return this.buffers.push(e),this.bufferRegions.push(new uo(this._byteLength,t)),this._byteLength+=t,this}function Ko(e){return Yo.call(this,e.values.subarray(0,e.length*e.stride))}function Qo(e){const{length:t,values:n,valueOffsets:r}=e,i=r[0],o=r[t],a=Math.min(o-i,n.byteLength-i);return Yo.call(this,re(-r[0],t,r)),Yo.call(this,n.subarray(i,i+a)),this}function qo(e){const{length:t,valueOffsets:n}=e;return n&&Yo.call(this,re(n[0],t,n)),this.visit(e.children[0])}function Go(e){return this.visitMany(e.type.children.map(((t,n)=>e.children[n])).filter(Boolean))[0]}Ho.prototype.visitBool=function(e){let t;return e.nullCount>=e.length?Yo.call(this,new Uint8Array(0)):(t=e.values)instanceof Uint8Array?Yo.call(this,On(e.offset,e.length,t)):Yo.call(this,An(e.values))},Ho.prototype.visitInt=Ko,Ho.prototype.visitFloat=Ko,Ho.prototype.visitUtf8=Qo,Ho.prototype.visitBinary=Qo,Ho.prototype.visitFixedSizeBinary=Ko,Ho.prototype.visitDate=Ko,Ho.prototype.visitTimestamp=Ko,Ho.prototype.visitTime=Ko,Ho.prototype.visitDecimal=Ko,Ho.prototype.visitList=qo,Ho.prototype.visitStruct=Go,Ho.prototype.visitUnion=function(e){const{type:n,length:r,typeIds:i,valueOffsets:o}=e;if(Yo.call(this,i),n.mode===t.Sparse)return Go.call(this,e);if(n.mode===t.Dense){if(e.offset<=0)return Yo.call(this,o),Go.call(this,e);{const t=i.reduce(((e,t)=>Math.max(e,t)),i[0]),a=new Int32Array(t+1),s=new Int32Array(t+1).fill(-1),l=new Int32Array(r),u=re(-o[0],r,o);for(let e,n,o=-1;++o<r;)-1===(n=s[e=i[o]])&&(n=s[e]=u[e]),l[o]=u[o]-n,++a[e];Yo.call(this,l);for(let i,o=-1,c=n.children.length;++o<c;)if(i=e.children[o]){const e=n.typeIds[o],t=Math.min(r,a[e]);this.visit(i.slice(s[e],t))}}}return this},Ho.prototype.visitInterval=Ko,Ho.prototype.visitFixedSizeList=qo,Ho.prototype.visitMap=qo;class Xo extends ti{constructor(e){super(),this._position=0,this._started=!1,this._sink=new ri,this._schema=null,this._dictionaryBlocks=[],this._recordBatchBlocks=[],this._dictionaryDeltaOffsets=new Map,I(e)||(e={autoDestroy:!0,writeLegacyIpcFormat:!1}),this._autoDestroy="boolean"!==typeof e.autoDestroy||e.autoDestroy,this._writeLegacyIpcFormat="boolean"===typeof e.writeLegacyIpcFormat&&e.writeLegacyIpcFormat}static throughNode(e){throw new Error('"throughNode" not available in this environment')}static throughDOM(e,t){throw new Error('"throughDOM" not available in this environment')}toString(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this._sink.toString(e)}toUint8Array(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this._sink.toUint8Array(e)}writeAll(e){return T(e)?e.then((e=>this.writeAll(e))):A(e)?ta(this,e):ea(this,e)}get closed(){return this._sink.closed}[Symbol.asyncIterator](){return this._sink[Symbol.asyncIterator]()}toDOMStream(e){return this._sink.toDOMStream(e)}toNodeStream(e){return this._sink.toNodeStream(e)}close(){return this.reset()._sink.close()}abort(e){return this.reset()._sink.abort(e)}finish(){return this._autoDestroy?this.close():this.reset(this._sink,this._schema),this}reset(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this._sink,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;var n;return e===this._sink||e instanceof ri?this._sink=e:(this._sink=new ri,e&&(I(n=e)&&k(n.abort)&&k(n.getWriter)&&!B(n))?this.toDOMStream({type:"bytes"}).pipeTo(e):e&&(e=>I(e)&&k(e.end)&&k(e.write)&&x(e.writable)&&!B(e))(e)&&this.toNodeStream({objectMode:!1}).pipe(e)),this._started&&this._schema&&this._writeFooter(this._schema),this._started=!1,this._dictionaryBlocks=[],this._recordBatchBlocks=[],this._dictionaryDeltaOffsets=new Map,t&&pr(t,this._schema)||(null==t?(this._position=0,this._schema=null):(this._started=!0,this._schema=t,this._writeSchema(t))),this}write(e){let t=null;if(!this._sink)throw new Error("RecordBatchWriter is closed");if(null==e)return this.finish()&&void 0;if(e instanceof xr&&!(t=e.schema))return this.finish()&&void 0;if(e instanceof br&&!(t=e.schema))return this.finish()&&void 0;if(t&&!pr(t,this._schema)){if(this._started&&this._autoDestroy)return this.close();this.reset(this._sink,t)}e instanceof br?e instanceof wr||this._writeRecordBatch(e):e instanceof xr?this.writeAll(e.batches):O(e)&&this.writeAll(e)}_writeMessage(e){const t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:8)-1,n=ao.encode(e),r=n.byteLength,i=this._writeLegacyIpcFormat?4:8,o=r+i+t&~t,a=o-r-i;return e.headerType===s.RecordBatch?this._recordBatchBlocks.push(new Qr(o,e.bodyLength,this._position)):e.headerType===s.DictionaryBatch&&this._dictionaryBlocks.push(new Qr(o,e.bodyLength,this._position)),this._writeLegacyIpcFormat||this._write(Int32Array.of(-1)),this._write(Int32Array.of(o-i)),r>0&&this._write(n),this._writePadding(a)}_write(e){if(this._started){const t=Z(e);t&&t.byteLength>0&&(this._sink.write(t),this._position+=t.byteLength)}return this}_writeSchema(e){return this._writeMessage(ao.from(e))}_writeFooter(e){return this._writeLegacyIpcFormat?this._write(Int32Array.of(0)):this._write(Int32Array.of(-1,0))}_writeMagic(){return this._write(To)}_writePadding(e){return e>0?this._write(new Uint8Array(e)):this}_writeRecordBatch(e){const{byteLength:t,nodes:n,bufferRegions:r,buffers:i}=Ho.assemble(e),o=new so(e.numRows,n,r),a=ao.from(o,t);return this._writeDictionaries(e)._writeMessage(a)._writeBodyBuffers(i)}_writeDictionaryBatch(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this._dictionaryDeltaOffsets.set(t,e.length+(this._dictionaryDeltaOffsets.get(t)||0));const{byteLength:r,nodes:i,bufferRegions:o,buffers:a}=Ho.assemble(new Qn([e])),s=new so(e.length,i,o),l=new lo(s,t,n),u=ao.from(l,r);return this._writeMessage(u)._writeBodyBuffers(a)}_writeBodyBuffers(e){let t,n,r;for(let i=-1,o=e.length;++i<o;)(t=e[i])&&(n=t.byteLength)>0&&(this._write(t),(r=(n+7&-8)-n)>0&&this._writePadding(r));return this}_writeDictionaries(e){for(let[t,n]of e.dictionaries){let e=this._dictionaryDeltaOffsets.get(t)||0;if(0===e||(n=null===n||void 0===n?void 0:n.slice(e)).length>0)for(const r of n.data)this._writeDictionaryBatch(r,t,e>0),e+=r.length}return this}}class Jo extends Xo{static writeAll(e,t){const n=new Jo(t);return T(e)?e.then((e=>n.writeAll(e))):A(e)?ta(n,e):ea(n,e)}}class Zo extends Xo{static writeAll(e){const t=new Zo;return T(e)?e.then((e=>t.writeAll(e))):A(e)?ta(t,e):ea(t,e)}constructor(){super(),this._autoDestroy=!0}_writeSchema(e){return this._writeMagic()._writePadding(2)}_writeFooter(t){const n=Yr.encode(new Yr(t,e.V4,this._recordBatchBlocks,this._dictionaryBlocks));return super._writeFooter(t)._write(n)._write(Int32Array.of(n.byteLength))._writeMagic()}}function ea(e,t){let n=t;t instanceof xr&&(n=t.batches,e.reset(void 0,t.schema));for(const r of n)e.write(r);return e.finish()}function ta(e,t){var n,r,i,o;return R(this,void 0,void 0,(function*(){try{for(n=W(t);!(r=yield n.next()).done;){const t=r.value;e.write(t)}}catch(a){i={error:a}}finally{try{r&&!r.done&&(o=n.return)&&(yield o.call(n))}finally{if(i)throw i.error}}return e.finish()}))}function na(e){const t=No.from(e);return T(t)?t.then((e=>na(e))):t.isAsync()?t.readAll().then((e=>new xr(e))):new xr(t.readAll())}function ra(e){return("stream"===(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"stream")?Jo:Zo).writeAll(e).toUint8Array(!0)}var ia,oa=function(){function e(e,t,n,r){var i=this;this.getCell=function(e,t){var n=e<i.headerRows&&t<i.headerColumns,r=e>=i.headerRows&&t<i.headerColumns,o=e<i.headerRows&&t>=i.headerColumns;if(n){var a=["blank"];return t>0&&a.push("level"+e),{type:"blank",classNames:a.join(" "),content:""}}if(o)return{type:"columns",classNames:(a=["col_heading","level"+e,"col"+(l=t-i.headerColumns)]).join(" "),content:i.getContent(i.columnsTable,l,e)};if(r){a=["row_heading","level"+t,"row"+(s=e-i.headerRows)];return{type:"index",id:"T_".concat(i.uuid,"level").concat(t,"_row").concat(s),classNames:a.join(" "),content:i.getContent(i.indexTable,s,t)}}a=["data","row"+(s=e-i.headerRows),"col"+(l=t-i.headerColumns)];var s,l,u=i.styler?i.getContent(i.styler.displayValuesTable,s,l):i.getContent(i.dataTable,s,l);return{type:"data",id:"T_".concat(i.uuid,"row").concat(s,"_col").concat(l),classNames:a.join(" "),content:u}},this.getContent=function(e,t,n){var r=e.getChildAt(n);return null===r?"":i.getColumnTypeId(e,n)===l.Timestamp?i.nanosToDate(r.get(t)):r.get(t)},this.dataTable=na(e),this.indexTable=na(t),this.columnsTable=na(n),this.styler=r?{caption:r.caption,displayValuesTable:na(r.displayValues),styles:r.styles,uuid:r.uuid}:void 0}return Object.defineProperty(e.prototype,"rows",{get:function(){return this.indexTable.numRows+this.columnsTable.numCols},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"columns",{get:function(){return this.indexTable.numCols+this.columnsTable.numRows},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"headerRows",{get:function(){return this.rows-this.dataRows},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"headerColumns",{get:function(){return this.columns-this.dataColumns},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"dataRows",{get:function(){return this.dataTable.numRows},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"dataColumns",{get:function(){return this.dataTable.numCols},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"uuid",{get:function(){return this.styler&&this.styler.uuid},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"caption",{get:function(){return this.styler&&this.styler.caption},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"styles",{get:function(){return this.styler&&this.styler.styles},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"table",{get:function(){return this.dataTable},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"index",{get:function(){return this.indexTable},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"columnTable",{get:function(){return this.columnsTable},enumerable:!1,configurable:!0}),e.prototype.serialize=function(){return{data:ra(this.dataTable),index:ra(this.indexTable),columns:ra(this.columnsTable)}},e.prototype.getColumnTypeId=function(e,t){return e.schema.fields[t].type.typeId},e.prototype.nanosToDate=function(e){return new Date(e/1e6)},e}(),aa=function(){return aa=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},aa.apply(this,arguments)};!function(e){e.COMPONENT_READY="streamlit:componentReady",e.SET_COMPONENT_VALUE="streamlit:setComponentValue",e.SET_FRAME_HEIGHT="streamlit:setFrameHeight"}(ia||(ia={}));var sa=function(){function e(){}return e.API_VERSION=1,e.RENDER_EVENT="streamlit:render",e.events=new EventTarget,e.registeredMessageListener=!1,e.setComponentReady=function(){e.registeredMessageListener||(window.addEventListener("message",e.onMessageEvent),e.registeredMessageListener=!0),e.sendBackMsg(ia.COMPONENT_READY,{apiVersion:e.API_VERSION})},e.setFrameHeight=function(t){void 0===t&&(t=document.body.scrollHeight),t!==e.lastFrameHeight&&(e.lastFrameHeight=t,e.sendBackMsg(ia.SET_FRAME_HEIGHT,{height:t}))},e.setComponentValue=function(t){var n;t instanceof oa?(n="dataframe",t=t.serialize()):!function(e){var t=!1;try{t=e instanceof BigInt64Array||e instanceof BigUint64Array}catch(n){}return e instanceof Int8Array||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array||t}(t)?t instanceof ArrayBuffer?(n="bytes",t=new Uint8Array(t)):n="json":(n="bytes",t=new Uint8Array(t.buffer)),e.sendBackMsg(ia.SET_COMPONENT_VALUE,{value:t,dataType:n})},e.onMessageEvent=function(t){if(t.data.type===e.RENDER_EVENT)e.onRenderMessage(t.data)},e.onRenderMessage=function(t){var n=t.args;null==n&&(console.error("Got null args in onRenderMessage. This should never happen"),n={});var r=t.dfs&&t.dfs.length>0?e.argsDataframeToObject(t.dfs):{};n=aa(aa({},n),r);var i=Boolean(t.disabled),o=t.theme;o&&la(o);var a={disabled:i,args:n,theme:o},s=new CustomEvent(e.RENDER_EVENT,{detail:a});e.events.dispatchEvent(s)},e.argsDataframeToObject=function(t){var n=t.map((function(t){var n=t.key,r=t.value;return[n,e.toArrowTable(r)]}));return Object.fromEntries(n)},e.toArrowTable=function(e){var t,n=(t=e.data).data,r=t.index,i=t.columns,o=t.styler;return new oa(n,r,i,o)},e.sendBackMsg=function(e,t){window.parent.postMessage(aa({isStreamlitMessage:!0,type:e},t),"*")},e}(),la=function(e){var t=document.createElement("style");document.head.appendChild(t),t.innerHTML="\n    :root {\n      --primary-color: ".concat(e.primaryColor,";\n      --background-color: ").concat(e.backgroundColor,";\n      --secondary-background-color: ").concat(e.secondaryBackgroundColor,";\n      --text-color: ").concat(e.textColor,";\n      --font: ").concat(e.font,";\n    }\n\n    body {\n      background-color: var(--background-color);\n      color: var(--text-color);\n    }\n  ")};var ua=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!==typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();!function(e){function t(){return null!==e&&e.apply(this,arguments)||this}ua(t,e),t.prototype.componentDidMount=function(){sa.setFrameHeight()},t.prototype.componentDidUpdate=function(){sa.setFrameHeight()}}(y.PureComponent);function ca(){return ca=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ca.apply(this,arguments)}function da(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}function fa(e){let t="https://mui.com/production-error/?code="+e;for(let n=1;n<arguments.length;n+=1)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified MUI error #"+e+"; visit "+t+" for the full message."}function ha(e){return null!==e&&"object"===typeof e&&e.constructor===Object}function pa(e){if(!ha(e))return e;const t={};return Object.keys(e).forEach((n=>{t[n]=pa(e[n])})),t}function ya(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{clone:!0};const r=n.clone?ca({},e):e;return ha(e)&&ha(t)&&Object.keys(t).forEach((i=>{"__proto__"!==i&&(ha(t[i])&&i in e&&ha(e[i])?r[i]=ya(e[i],t[i],n):n.clone?r[i]=ha(t[i])?pa(t[i]):t[i]:r[i]=t[i])})),r}const ma=["values","unit","step"],ba=e=>{const t=Object.keys(e).map((t=>({key:t,val:e[t]})))||[];return t.sort(((e,t)=>e.val-t.val)),t.reduce(((e,t)=>ca({},e,{[t.key]:t.val})),{})};const ga={borderRadius:4},va={xs:0,sm:600,md:900,lg:1200,xl:1536},wa={keys:["xs","sm","md","lg","xl"],up:e=>"@media (min-width:".concat(va[e],"px)")};function _a(e,t,n){const r=e.theme||{};if(Array.isArray(t)){const e=r.breakpoints||wa;return t.reduce(((r,i,o)=>(r[e.up(e.keys[o])]=n(t[o]),r)),{})}if("object"===typeof t){const e=r.breakpoints||wa;return Object.keys(t).reduce(((r,i)=>{if(-1!==Object.keys(e.values||va).indexOf(i)){r[e.up(i)]=n(t[i],i)}else{const e=i;r[e]=t[e]}return r}),{})}return n(t)}function Sa(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};var t;return(null==(t=e.keys)?void 0:t.reduce(((t,n)=>(t[e.up(n)]={},t)),{}))||{}}function xa(e,t){return e.reduce(((e,t)=>{const n=e[t];return(!n||0===Object.keys(n).length)&&delete e[t],e}),t)}function ka(e){let{values:t,breakpoints:n,base:r}=e;const i=r||function(e,t){if("object"!==typeof e)return{};const n={},r=Object.keys(t);return Array.isArray(e)?r.forEach(((t,r)=>{r<e.length&&(n[t]=!0)})):r.forEach((t=>{null!=e[t]&&(n[t]=!0)})),n}(t,n),o=Object.keys(i);if(0===o.length)return t;let a;return o.reduce(((e,n,r)=>(Array.isArray(t)?(e[n]=null!=t[r]?t[r]:t[a],a=r):"object"===typeof t?(e[n]=null!=t[n]?t[n]:t[a],a=n):e[n]=t,e)),{})}function Ia(e){if("string"!==typeof e)throw new Error(fa(7));return e.charAt(0).toUpperCase()+e.slice(1)}function Ta(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!t||"string"!==typeof t)return null;if(e&&e.vars&&n){const n="vars.".concat(t).split(".").reduce(((e,t)=>e&&e[t]?e[t]:null),e);if(null!=n)return n}return t.split(".").reduce(((e,t)=>e&&null!=e[t]?e[t]:null),e)}function Oa(e,t,n){let r,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:n;return r="function"===typeof e?e(n):Array.isArray(e)?e[n]||i:Ta(e,n)||i,t&&(r=t(r,i,e)),r}const Aa=function(e){const{prop:t,cssProperty:n=e.prop,themeKey:r,transform:i}=e,o=e=>{if(null==e[t])return null;const o=e[t],a=Ta(e.theme,r)||{};return _a(e,o,(e=>{let r=Oa(a,i,e);return e===r&&"string"===typeof e&&(r=Oa(a,i,"".concat(t).concat("default"===e?"":Ia(e)),e)),!1===n?r:{[n]:r}}))};return o.propTypes={},o.filterProps=[t],o};const Ca=function(e,t){return t?ya(e,t,{clone:!1}):e};const Ea={m:"margin",p:"padding"},Na={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},Ma={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},Ba=function(e){const t={};return n=>(void 0===t[n]&&(t[n]=e(n)),t[n])}((e=>{if(e.length>2){if(!Ma[e])return[e];e=Ma[e]}const[t,n]=e.split(""),r=Ea[t],i=Na[n]||"";return Array.isArray(i)?i.map((e=>r+e)):[r+i]})),La=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],Da=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"],Fa=[...La,...Da];function Pa(e,t,n,r){var i;const o=null!=(i=Ta(e,t,!1))?i:n;return"number"===typeof o?e=>"string"===typeof e?e:o*e:Array.isArray(o)?e=>"string"===typeof e?e:o[e]:"function"===typeof o?o:()=>{}}function Ra(e){return Pa(e,"spacing",8)}function za(e,t){if("string"===typeof t||null==t)return t;const n=e(Math.abs(t));return t>=0?n:"number"===typeof n?-n:"-".concat(n)}function Ua(e,t,n,r){if(-1===t.indexOf(n))return null;const i=function(e,t){return n=>e.reduce(((e,r)=>(e[r]=za(t,n),e)),{})}(Ba(n),r);return _a(e,e[n],i)}function ja(e,t){const n=Ra(e.theme);return Object.keys(e).map((r=>Ua(e,t,r,n))).reduce(Ca,{})}function Va(e){return ja(e,La)}function Wa(e){return ja(e,Da)}function $a(e){return ja(e,Fa)}Va.propTypes={},Va.filterProps=La,Wa.propTypes={},Wa.filterProps=Da,$a.propTypes={},$a.filterProps=Fa;const Ha=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];const r=t.reduce(((e,t)=>(t.filterProps.forEach((n=>{e[n]=t})),e)),{}),i=e=>Object.keys(e).reduce(((t,n)=>r[n]?Ca(t,r[n](e)):t),{});return i.propTypes={},i.filterProps=t.reduce(((e,t)=>e.concat(t.filterProps)),[]),i};function Ya(e){return"number"!==typeof e?e:"".concat(e,"px solid")}function Ka(e,t){return Aa({prop:e,themeKey:"borders",transform:t})}const Qa=Ka("border",Ya),qa=Ka("borderTop",Ya),Ga=Ka("borderRight",Ya),Xa=Ka("borderBottom",Ya),Ja=Ka("borderLeft",Ya),Za=Ka("borderColor"),es=Ka("borderTopColor"),ts=Ka("borderRightColor"),ns=Ka("borderBottomColor"),rs=Ka("borderLeftColor"),is=Ka("outline",Ya),os=Ka("outlineColor"),as=e=>{if(void 0!==e.borderRadius&&null!==e.borderRadius){const t=Pa(e.theme,"shape.borderRadius",4),n=e=>({borderRadius:za(t,e)});return _a(e,e.borderRadius,n)}return null};as.propTypes={},as.filterProps=["borderRadius"];Ha(Qa,qa,Ga,Xa,Ja,Za,es,ts,ns,rs,as,is,os);const ss=e=>{if(void 0!==e.gap&&null!==e.gap){const t=Pa(e.theme,"spacing",8),n=e=>({gap:za(t,e)});return _a(e,e.gap,n)}return null};ss.propTypes={},ss.filterProps=["gap"];const ls=e=>{if(void 0!==e.columnGap&&null!==e.columnGap){const t=Pa(e.theme,"spacing",8),n=e=>({columnGap:za(t,e)});return _a(e,e.columnGap,n)}return null};ls.propTypes={},ls.filterProps=["columnGap"];const us=e=>{if(void 0!==e.rowGap&&null!==e.rowGap){const t=Pa(e.theme,"spacing",8),n=e=>({rowGap:za(t,e)});return _a(e,e.rowGap,n)}return null};us.propTypes={},us.filterProps=["rowGap"];Ha(ss,ls,us,Aa({prop:"gridColumn"}),Aa({prop:"gridRow"}),Aa({prop:"gridAutoFlow"}),Aa({prop:"gridAutoColumns"}),Aa({prop:"gridAutoRows"}),Aa({prop:"gridTemplateColumns"}),Aa({prop:"gridTemplateRows"}),Aa({prop:"gridTemplateAreas"}),Aa({prop:"gridArea"}));function cs(e,t){return"grey"===t?t:e}Ha(Aa({prop:"color",themeKey:"palette",transform:cs}),Aa({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:cs}),Aa({prop:"backgroundColor",themeKey:"palette",transform:cs}));function ds(e){return e<=1&&0!==e?"".concat(100*e,"%"):e}const fs=Aa({prop:"width",transform:ds}),hs=e=>{if(void 0!==e.maxWidth&&null!==e.maxWidth){const t=t=>{var n,r;const i=(null==(n=e.theme)||null==(n=n.breakpoints)||null==(n=n.values)?void 0:n[t])||va[t];return i?"px"!==(null==(r=e.theme)||null==(r=r.breakpoints)?void 0:r.unit)?{maxWidth:"".concat(i).concat(e.theme.breakpoints.unit)}:{maxWidth:i}:{maxWidth:ds(t)}};return _a(e,e.maxWidth,t)}return null};hs.filterProps=["maxWidth"];const ps=Aa({prop:"minWidth",transform:ds}),ys=Aa({prop:"height",transform:ds}),ms=Aa({prop:"maxHeight",transform:ds}),bs=Aa({prop:"minHeight",transform:ds}),gs=(Aa({prop:"size",cssProperty:"width",transform:ds}),Aa({prop:"size",cssProperty:"height",transform:ds}),Ha(fs,hs,ps,ys,ms,bs,Aa({prop:"boxSizing"})),{border:{themeKey:"borders",transform:Ya},borderTop:{themeKey:"borders",transform:Ya},borderRight:{themeKey:"borders",transform:Ya},borderBottom:{themeKey:"borders",transform:Ya},borderLeft:{themeKey:"borders",transform:Ya},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:Ya},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:as},color:{themeKey:"palette",transform:cs},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:cs},backgroundColor:{themeKey:"palette",transform:cs},p:{style:Wa},pt:{style:Wa},pr:{style:Wa},pb:{style:Wa},pl:{style:Wa},px:{style:Wa},py:{style:Wa},padding:{style:Wa},paddingTop:{style:Wa},paddingRight:{style:Wa},paddingBottom:{style:Wa},paddingLeft:{style:Wa},paddingX:{style:Wa},paddingY:{style:Wa},paddingInline:{style:Wa},paddingInlineStart:{style:Wa},paddingInlineEnd:{style:Wa},paddingBlock:{style:Wa},paddingBlockStart:{style:Wa},paddingBlockEnd:{style:Wa},m:{style:Va},mt:{style:Va},mr:{style:Va},mb:{style:Va},ml:{style:Va},mx:{style:Va},my:{style:Va},margin:{style:Va},marginTop:{style:Va},marginRight:{style:Va},marginBottom:{style:Va},marginLeft:{style:Va},marginX:{style:Va},marginY:{style:Va},marginInline:{style:Va},marginInlineStart:{style:Va},marginInlineEnd:{style:Va},marginBlock:{style:Va},marginBlockStart:{style:Va},marginBlockEnd:{style:Va},displayPrint:{cssProperty:!1,transform:e=>({"@media print":{display:e}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:ss},rowGap:{style:us},columnGap:{style:ls},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:ds},maxWidth:{style:hs},minWidth:{transform:ds},height:{transform:ds},maxHeight:{transform:ds},minHeight:{transform:ds},boxSizing:{},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}});const vs=function(){function e(e,t,n,r){const i={[e]:t,theme:n},o=r[e];if(!o)return{[e]:t};const{cssProperty:a=e,themeKey:s,transform:l,style:u}=o;if(null==t)return null;if("typography"===s&&"inherit"===t)return{[e]:t};const c=Ta(n,s)||{};if(u)return u(i);return _a(i,t,(t=>{let n=Oa(c,l,t);return t===n&&"string"===typeof t&&(n=Oa(c,l,"".concat(e).concat("default"===t?"":Ia(t)),t)),!1===a?n:{[a]:n}}))}return function t(n){var r;const{sx:i,theme:o={}}=n||{};if(!i)return null;const a=null!=(r=o.unstable_sxConfig)?r:gs;function s(n){let r=n;if("function"===typeof n)r=n(o);else if("object"!==typeof n)return n;if(!r)return null;const i=Sa(o.breakpoints),s=Object.keys(i);let l=i;return Object.keys(r).forEach((n=>{const i=(s=r[n],u=o,"function"===typeof s?s(u):s);var s,u;if(null!==i&&void 0!==i)if("object"===typeof i)if(a[n])l=Ca(l,e(n,i,o,a));else{const e=_a({theme:o},i,(e=>({[n]:e})));!function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];const r=t.reduce(((e,t)=>e.concat(Object.keys(t))),[]),i=new Set(r);return t.every((e=>i.size===Object.keys(e).length))}(e,i)?l=Ca(l,e):l[n]=t({sx:i,theme:o})}else l=Ca(l,e(n,i,o,a))})),xa(s,l)}return Array.isArray(i)?i.map(s):s(i)}}();vs.filterProps=["sx"];const ws=vs,_s=["breakpoints","palette","spacing","shape"];const Ss=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{breakpoints:t={},palette:n={},spacing:r,shape:i={}}=e,o=da(e,_s),a=function(e){const{values:t={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:n="px",step:r=5}=e,i=da(e,ma),o=ba(t),a=Object.keys(o);function s(e){const r="number"===typeof t[e]?t[e]:e;return"@media (min-width:".concat(r).concat(n,")")}function l(e){const i="number"===typeof t[e]?t[e]:e;return"@media (max-width:".concat(i-r/100).concat(n,")")}function u(e,i){const o=a.indexOf(i);return"@media (min-width:".concat("number"===typeof t[e]?t[e]:e).concat(n,") and ")+"(max-width:".concat((-1!==o&&"number"===typeof t[a[o]]?t[a[o]]:i)-r/100).concat(n,")")}return ca({keys:a,values:o,up:s,down:l,between:u,only:function(e){return a.indexOf(e)+1<a.length?u(e,a[a.indexOf(e)+1]):s(e)},not:function(e){const t=a.indexOf(e);return 0===t?s(a[1]):t===a.length-1?l(a[t]):u(e,a[a.indexOf(e)+1]).replace("@media","@media not all and")},unit:n},i)}(t),s=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:8;if(e.mui)return e;const t=Ra({spacing:e}),n=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return(0===n.length?[1]:n).map((e=>{const n=t(e);return"number"===typeof n?"".concat(n,"px"):n})).join(" ")};return n.mui=!0,n}(r);let l=ya({breakpoints:a,direction:"ltr",components:{},palette:ca({mode:"light"},n),spacing:s,shape:ca({},ga,i)},o);for(var u=arguments.length,c=new Array(u>1?u-1:0),d=1;d<u;d++)c[d-1]=arguments[d];return l=c.reduce(((e,t)=>ya(e,t)),l),l.unstable_sxConfig=ca({},gs,null==o?void 0:o.unstable_sxConfig),l.unstable_sx=function(e){return ws({sx:e,theme:this})},l};function xs(e,t){return ca({toolbar:{minHeight:56,[e.up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[e.up("sm")]:{minHeight:64}}},t)}function ks(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;return Math.min(Math.max(t,e),n)}function Is(e){if(e.type)return e;if("#"===e.charAt(0))return Is(function(e){e=e.slice(1);const t=new RegExp(".{1,".concat(e.length>=6?2:1,"}"),"g");let n=e.match(t);return n&&1===n[0].length&&(n=n.map((e=>e+e))),n?"rgb".concat(4===n.length?"a":"","(").concat(n.map(((e,t)=>t<3?parseInt(e,16):Math.round(parseInt(e,16)/255*1e3)/1e3)).join(", "),")"):""}(e));const t=e.indexOf("("),n=e.substring(0,t);if(-1===["rgb","rgba","hsl","hsla","color"].indexOf(n))throw new Error(fa(9,e));let r,i=e.substring(t+1,e.length-1);if("color"===n){if(i=i.split(" "),r=i.shift(),4===i.length&&"/"===i[3].charAt(0)&&(i[3]=i[3].slice(1)),-1===["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].indexOf(r))throw new Error(fa(10,r))}else i=i.split(",");return i=i.map((e=>parseFloat(e))),{type:n,values:i,colorSpace:r}}function Ts(e){const{type:t,colorSpace:n}=e;let{values:r}=e;return-1!==t.indexOf("rgb")?r=r.map(((e,t)=>t<3?parseInt(e,10):e)):-1!==t.indexOf("hsl")&&(r[1]="".concat(r[1],"%"),r[2]="".concat(r[2],"%")),r=-1!==t.indexOf("color")?"".concat(n," ").concat(r.join(" ")):"".concat(r.join(", ")),"".concat(t,"(").concat(r,")")}function Os(e){let t="hsl"===(e=Is(e)).type||"hsla"===e.type?Is(function(e){e=Is(e);const{values:t}=e,n=t[0],r=t[1]/100,i=t[2]/100,o=r*Math.min(i,1-i),a=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:(e+n/30)%12;return i-o*Math.max(Math.min(t-3,9-t,1),-1)};let s="rgb";const l=[Math.round(255*a(0)),Math.round(255*a(8)),Math.round(255*a(4))];return"hsla"===e.type&&(s+="a",l.push(t[3])),Ts({type:s,values:l})}(e)).values:e.values;return t=t.map((t=>("color"!==e.type&&(t/=255),t<=.03928?t/12.92:((t+.055)/1.055)**2.4))),Number((.2126*t[0]+.7152*t[1]+.0722*t[2]).toFixed(3))}function As(e,t){return e=Is(e),t=ks(t),"rgb"!==e.type&&"hsl"!==e.type||(e.type+="a"),"color"===e.type?e.values[3]="/".concat(t):e.values[3]=t,Ts(e)}function Cs(e,t){if(e=Is(e),t=ks(t),-1!==e.type.indexOf("hsl"))e.values[2]*=1-t;else if(-1!==e.type.indexOf("rgb")||-1!==e.type.indexOf("color"))for(let n=0;n<3;n+=1)e.values[n]*=1-t;return Ts(e)}function Es(e,t){if(e=Is(e),t=ks(t),-1!==e.type.indexOf("hsl"))e.values[2]+=(100-e.values[2])*t;else if(-1!==e.type.indexOf("rgb"))for(let n=0;n<3;n+=1)e.values[n]+=(255-e.values[n])*t;else if(-1!==e.type.indexOf("color"))for(let n=0;n<3;n+=1)e.values[n]+=(1-e.values[n])*t;return Ts(e)}const Ns={black:"#000",white:"#fff"},Ms={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"},Bs={50:"#f3e5f5",100:"#e1bee7",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",600:"#8e24aa",700:"#7b1fa2",800:"#6a1b9a",900:"#4a148c",A100:"#ea80fc",A200:"#e040fb",A400:"#d500f9",A700:"#aa00ff"},Ls={50:"#ffebee",100:"#ffcdd2",200:"#ef9a9a",300:"#e57373",400:"#ef5350",500:"#f44336",600:"#e53935",700:"#d32f2f",800:"#c62828",900:"#b71c1c",A100:"#ff8a80",A200:"#ff5252",A400:"#ff1744",A700:"#d50000"},Ds={50:"#fff3e0",100:"#ffe0b2",200:"#ffcc80",300:"#ffb74d",400:"#ffa726",500:"#ff9800",600:"#fb8c00",700:"#f57c00",800:"#ef6c00",900:"#e65100",A100:"#ffd180",A200:"#ffab40",A400:"#ff9100",A700:"#ff6d00"},Fs={50:"#e3f2fd",100:"#bbdefb",200:"#90caf9",300:"#64b5f6",400:"#42a5f5",500:"#2196f3",600:"#1e88e5",700:"#1976d2",800:"#1565c0",900:"#0d47a1",A100:"#82b1ff",A200:"#448aff",A400:"#2979ff",A700:"#2962ff"},Ps={50:"#e1f5fe",100:"#b3e5fc",200:"#81d4fa",300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",600:"#039be5",700:"#0288d1",800:"#0277bd",900:"#01579b",A100:"#80d8ff",A200:"#40c4ff",A400:"#00b0ff",A700:"#0091ea"},Rs={50:"#e8f5e9",100:"#c8e6c9",200:"#a5d6a7",300:"#81c784",400:"#66bb6a",500:"#4caf50",600:"#43a047",700:"#388e3c",800:"#2e7d32",900:"#1b5e20",A100:"#b9f6ca",A200:"#69f0ae",A400:"#00e676",A700:"#00c853"},zs=["mode","contrastThreshold","tonalOffset"],Us={text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:Ns.white,default:Ns.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}},js={text:{primary:Ns.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:Ns.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}};function Vs(e,t,n,r){const i=r.light||r,o=r.dark||1.5*r;e[t]||(e.hasOwnProperty(n)?e[t]=e[n]:"light"===t?e.light=Es(e.main,i):"dark"===t&&(e.dark=Cs(e.main,o)))}function Ws(e){const{mode:t="light",contrastThreshold:n=3,tonalOffset:r=.2}=e,i=da(e,zs),o=e.primary||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:Fs[200],light:Fs[50],dark:Fs[400]}:{main:Fs[700],light:Fs[400],dark:Fs[800]}}(t),a=e.secondary||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:Bs[200],light:Bs[50],dark:Bs[400]}:{main:Bs[500],light:Bs[300],dark:Bs[700]}}(t),s=e.error||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:Ls[500],light:Ls[300],dark:Ls[700]}:{main:Ls[700],light:Ls[400],dark:Ls[800]}}(t),l=e.info||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:Ps[400],light:Ps[300],dark:Ps[700]}:{main:Ps[700],light:Ps[500],dark:Ps[900]}}(t),u=e.success||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:Rs[400],light:Rs[300],dark:Rs[700]}:{main:Rs[800],light:Rs[500],dark:Rs[900]}}(t),c=e.warning||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:Ds[400],light:Ds[300],dark:Ds[700]}:{main:"#ed6c02",light:Ds[500],dark:Ds[900]}}(t);function d(e){const t=function(e,t){const n=Os(e),r=Os(t);return(Math.max(n,r)+.05)/(Math.min(n,r)+.05)}(e,js.text.primary)>=n?js.text.primary:Us.text.primary;return t}const f=e=>{let{color:t,name:n,mainShade:i=500,lightShade:o=300,darkShade:a=700}=e;if(t=ca({},t),!t.main&&t[i]&&(t.main=t[i]),!t.hasOwnProperty("main"))throw new Error(fa(11,n?" (".concat(n,")"):"",i));if("string"!==typeof t.main)throw new Error(fa(12,n?" (".concat(n,")"):"",JSON.stringify(t.main)));return Vs(t,"light",o,r),Vs(t,"dark",a,r),t.contrastText||(t.contrastText=d(t.main)),t},h={dark:js,light:Us};return ya(ca({common:ca({},Ns),mode:t,primary:f({color:o,name:"primary"}),secondary:f({color:a,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:f({color:s,name:"error"}),warning:f({color:c,name:"warning"}),info:f({color:l,name:"info"}),success:f({color:u,name:"success"}),grey:Ms,contrastThreshold:n,getContrastText:d,augmentColor:f,tonalOffset:r},h[t]),i)}const $s=["fontFamily","fontSize","fontWeightLight","fontWeightRegular","fontWeightMedium","fontWeightBold","htmlFontSize","allVariants","pxToRem"];const Hs={textTransform:"uppercase"},Ys='"Roboto", "Helvetica", "Arial", sans-serif';function Ks(e,t){const n="function"===typeof t?t(e):t,{fontFamily:r=Ys,fontSize:i=14,fontWeightLight:o=300,fontWeightRegular:a=400,fontWeightMedium:s=500,fontWeightBold:l=700,htmlFontSize:u=16,allVariants:c,pxToRem:d}=n,f=da(n,$s);const h=i/14,p=d||(e=>"".concat(e/u*h,"rem")),y=(e,t,n,i,o)=>{return ca({fontFamily:r,fontWeight:e,fontSize:p(t),lineHeight:n},r===Ys?{letterSpacing:"".concat((a=i/t,Math.round(1e5*a)/1e5),"em")}:{},o,c);var a},m={h1:y(o,96,1.167,-1.5),h2:y(o,60,1.2,-.5),h3:y(a,48,1.167,0),h4:y(a,34,1.235,.25),h5:y(a,24,1.334,0),h6:y(s,20,1.6,.15),subtitle1:y(a,16,1.75,.15),subtitle2:y(s,14,1.57,.1),body1:y(a,16,1.5,.15),body2:y(a,14,1.43,.15),button:y(s,14,1.75,.4,Hs),caption:y(a,12,1.66,.4),overline:y(a,12,2.66,1,Hs),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return ya(ca({htmlFontSize:u,pxToRem:p,fontFamily:r,fontSize:i,fontWeightLight:o,fontWeightRegular:a,fontWeightMedium:s,fontWeightBold:l},m),f,{clone:!1})}function Qs(){return["".concat(arguments.length<=0?void 0:arguments[0],"px ").concat(arguments.length<=1?void 0:arguments[1],"px ").concat(arguments.length<=2?void 0:arguments[2],"px ").concat(arguments.length<=3?void 0:arguments[3],"px rgba(0,0,0,").concat(.2,")"),"".concat(arguments.length<=4?void 0:arguments[4],"px ").concat(arguments.length<=5?void 0:arguments[5],"px ").concat(arguments.length<=6?void 0:arguments[6],"px ").concat(arguments.length<=7?void 0:arguments[7],"px rgba(0,0,0,").concat(.14,")"),"".concat(arguments.length<=8?void 0:arguments[8],"px ").concat(arguments.length<=9?void 0:arguments[9],"px ").concat(arguments.length<=10?void 0:arguments[10],"px ").concat(arguments.length<=11?void 0:arguments[11],"px rgba(0,0,0,").concat(.12,")")].join(",")}const qs=["none",Qs(0,2,1,-1,0,1,1,0,0,1,3,0),Qs(0,3,1,-2,0,2,2,0,0,1,5,0),Qs(0,3,3,-2,0,3,4,0,0,1,8,0),Qs(0,2,4,-1,0,4,5,0,0,1,10,0),Qs(0,3,5,-1,0,5,8,0,0,1,14,0),Qs(0,3,5,-1,0,6,10,0,0,1,18,0),Qs(0,4,5,-2,0,7,10,1,0,2,16,1),Qs(0,5,5,-3,0,8,10,1,0,3,14,2),Qs(0,5,6,-3,0,9,12,1,0,3,16,2),Qs(0,6,6,-3,0,10,14,1,0,4,18,3),Qs(0,6,7,-4,0,11,15,1,0,4,20,3),Qs(0,7,8,-4,0,12,17,2,0,5,22,4),Qs(0,7,8,-4,0,13,19,2,0,5,24,4),Qs(0,7,9,-4,0,14,21,2,0,5,26,4),Qs(0,8,9,-5,0,15,22,2,0,6,28,5),Qs(0,8,10,-5,0,16,24,2,0,6,30,5),Qs(0,8,11,-5,0,17,26,2,0,6,32,5),Qs(0,9,11,-5,0,18,28,2,0,7,34,6),Qs(0,9,12,-6,0,19,29,2,0,7,36,6),Qs(0,10,13,-6,0,20,31,3,0,8,38,7),Qs(0,10,13,-6,0,21,33,3,0,8,40,7),Qs(0,10,14,-6,0,22,35,3,0,8,42,7),Qs(0,11,14,-7,0,23,36,3,0,9,44,8),Qs(0,11,15,-7,0,24,38,3,0,9,46,8)],Gs=["duration","easing","delay"],Xs={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},Js={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function Zs(e){return"".concat(Math.round(e),"ms")}function el(e){if(!e)return 0;const t=e/36;return Math.round(10*(4+15*t**.25+t/5))}function tl(e){const t=ca({},Xs,e.easing),n=ca({},Js,e.duration);return ca({getAutoHeightDuration:el,create:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["all"],r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{duration:i=n.standard,easing:o=t.easeInOut,delay:a=0}=r;da(r,Gs);return(Array.isArray(e)?e:[e]).map((e=>"".concat(e," ").concat("string"===typeof i?i:Zs(i)," ").concat(o," ").concat("string"===typeof a?a:Zs(a)))).join(",")}},e,{easing:t,duration:n})}const nl={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500},rl=["breakpoints","mixins","spacing","palette","transitions","typography","shape"];function il(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{mixins:t={},palette:n={},transitions:r={},typography:i={}}=e,o=da(e,rl);if(e.vars)throw new Error(fa(18));const a=Ws(n),s=Ss(e);let l=ya(s,{mixins:xs(s.breakpoints,t),palette:a,shadows:qs.slice(),typography:Ks(a,i),transitions:tl(r),zIndex:ca({},nl)});l=ya(l,o);for(var u=arguments.length,c=new Array(u>1?u-1:0),d=1;d<u;d++)c[d-1]=arguments[d];return l=c.reduce(((e,t)=>ya(e,t)),l),l.unstable_sxConfig=ca({},gs,null==o?void 0:o.unstable_sxConfig),l.unstable_sx=function(e){return ws({sx:e,theme:this})},l}const ol=il;function al(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=al(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}const sl=function(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=al(e))&&(r&&(r+=" "),r+=t);return r};function ll(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;const r={};return Object.keys(e).forEach((i=>{r[i]=e[i].reduce(((e,r)=>{if(r){const i=t(r);""!==i&&e.push(i),n&&n[r]&&e.push(n[r])}return e}),[]).join(" ")})),r}const ul=e=>e,cl=(()=>{let e=ul;return{configure(t){e=t},generate:t=>e(t),reset(){e=ul}}})(),dl={active:"active",checked:"checked",completed:"completed",disabled:"disabled",error:"error",expanded:"expanded",focused:"focused",focusVisible:"focusVisible",open:"open",readOnly:"readOnly",required:"required",selected:"selected"};function fl(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"Mui";const r=dl[t];return r?"".concat(n,"-").concat(r):"".concat(cl.generate(e),"-").concat(t)}function hl(e){var t=Object.create(null);return function(n){return void 0===t[n]&&(t[n]=e(n)),t[n]}}var pl=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|download|draggable|encType|enterKeyHint|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,yl=hl((function(e){return pl.test(e)||111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)<91}));var ml=function(){function e(e){var t=this;this._insertTag=function(e){var n;n=0===t.tags.length?t.insertionPoint?t.insertionPoint.nextSibling:t.prepend?t.container.firstChild:t.before:t.tags[t.tags.length-1].nextSibling,t.container.insertBefore(e,n),t.tags.push(e)},this.isSpeedy=void 0===e.speedy||e.speedy,this.tags=[],this.ctr=0,this.nonce=e.nonce,this.key=e.key,this.container=e.container,this.prepend=e.prepend,this.insertionPoint=e.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(e){e.forEach(this._insertTag)},t.insert=function(e){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(function(e){var t=document.createElement("style");return t.setAttribute("data-emotion",e.key),void 0!==e.nonce&&t.setAttribute("nonce",e.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t}(this));var t=this.tags[this.tags.length-1];if(this.isSpeedy){var n=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}(t);try{n.insertRule(e,n.cssRules.length)}catch(r){0}}else t.appendChild(document.createTextNode(e));this.ctr++},t.flush=function(){this.tags.forEach((function(e){return e.parentNode&&e.parentNode.removeChild(e)})),this.tags=[],this.ctr=0},e}(),bl=Math.abs,gl=String.fromCharCode,vl=Object.assign;function wl(e){return e.trim()}function _l(e,t,n){return e.replace(t,n)}function Sl(e,t){return e.indexOf(t)}function xl(e,t){return 0|e.charCodeAt(t)}function kl(e,t,n){return e.slice(t,n)}function Il(e){return e.length}function Tl(e){return e.length}function Ol(e,t){return t.push(e),e}var Al=1,Cl=1,El=0,Nl=0,Ml=0,Bl="";function Ll(e,t,n,r,i,o,a){return{value:e,root:t,parent:n,type:r,props:i,children:o,line:Al,column:Cl,length:a,return:""}}function Dl(e,t){return vl(Ll("",null,null,"",null,null,0),e,{length:-e.length},t)}function Fl(){return Ml=Nl>0?xl(Bl,--Nl):0,Cl--,10===Ml&&(Cl=1,Al--),Ml}function Pl(){return Ml=Nl<El?xl(Bl,Nl++):0,Cl++,10===Ml&&(Cl=1,Al++),Ml}function Rl(){return xl(Bl,Nl)}function zl(){return Nl}function Ul(e,t){return kl(Bl,e,t)}function jl(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Vl(e){return Al=Cl=1,El=Il(Bl=e),Nl=0,[]}function Wl(e){return Bl="",e}function $l(e){return wl(Ul(Nl-1,Kl(91===e?e+2:40===e?e+1:e)))}function Hl(e){for(;(Ml=Rl())&&Ml<33;)Pl();return jl(e)>2||jl(Ml)>3?"":" "}function Yl(e,t){for(;--t&&Pl()&&!(Ml<48||Ml>102||Ml>57&&Ml<65||Ml>70&&Ml<97););return Ul(e,zl()+(t<6&&32==Rl()&&32==Pl()))}function Kl(e){for(;Pl();)switch(Ml){case e:return Nl;case 34:case 39:34!==e&&39!==e&&Kl(Ml);break;case 40:41===e&&Kl(e);break;case 92:Pl()}return Nl}function Ql(e,t){for(;Pl()&&e+Ml!==57&&(e+Ml!==84||47!==Rl()););return"/*"+Ul(t,Nl-1)+"*"+gl(47===e?e:Pl())}function ql(e){for(;!jl(Rl());)Pl();return Ul(e,Nl)}var Gl="-ms-",Xl="-moz-",Jl="-webkit-",Zl="comm",eu="rule",tu="decl",nu="@keyframes";function ru(e,t){for(var n="",r=Tl(e),i=0;i<r;i++)n+=t(e[i],i,e,t)||"";return n}function iu(e,t,n,r){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case tu:return e.return=e.return||e.value;case Zl:return"";case nu:return e.return=e.value+"{"+ru(e.children,r)+"}";case eu:e.value=e.props.join(",")}return Il(n=ru(e.children,r))?e.return=e.value+"{"+n+"}":""}function ou(e){return Wl(au("",null,null,null,[""],e=Vl(e),0,[0],e))}function au(e,t,n,r,i,o,a,s,l){for(var u=0,c=0,d=a,f=0,h=0,p=0,y=1,m=1,b=1,g=0,v="",w=i,_=o,S=r,x=v;m;)switch(p=g,g=Pl()){case 40:if(108!=p&&58==xl(x,d-1)){-1!=Sl(x+=_l($l(g),"&","&\f"),"&\f")&&(b=-1);break}case 34:case 39:case 91:x+=$l(g);break;case 9:case 10:case 13:case 32:x+=Hl(p);break;case 92:x+=Yl(zl()-1,7);continue;case 47:switch(Rl()){case 42:case 47:Ol(lu(Ql(Pl(),zl()),t,n),l);break;default:x+="/"}break;case 123*y:s[u++]=Il(x)*b;case 125*y:case 59:case 0:switch(g){case 0:case 125:m=0;case 59+c:-1==b&&(x=_l(x,/\f/g,"")),h>0&&Il(x)-d&&Ol(h>32?uu(x+";",r,n,d-1):uu(_l(x," ","")+";",r,n,d-2),l);break;case 59:x+=";";default:if(Ol(S=su(x,t,n,u,c,i,s,v,w=[],_=[],d),o),123===g)if(0===c)au(x,t,S,S,w,o,d,s,_);else switch(99===f&&110===xl(x,3)?100:f){case 100:case 108:case 109:case 115:au(e,S,S,r&&Ol(su(e,S,S,0,0,i,s,v,i,w=[],d),_),i,_,d,s,r?w:_);break;default:au(x,S,S,S,[""],_,0,s,_)}}u=c=h=0,y=b=1,v=x="",d=a;break;case 58:d=1+Il(x),h=p;default:if(y<1)if(123==g)--y;else if(125==g&&0==y++&&125==Fl())continue;switch(x+=gl(g),g*y){case 38:b=c>0?1:(x+="\f",-1);break;case 44:s[u++]=(Il(x)-1)*b,b=1;break;case 64:45===Rl()&&(x+=$l(Pl())),f=Rl(),c=d=Il(v=x+=ql(zl())),g++;break;case 45:45===p&&2==Il(x)&&(y=0)}}return o}function su(e,t,n,r,i,o,a,s,l,u,c){for(var d=i-1,f=0===i?o:[""],h=Tl(f),p=0,y=0,m=0;p<r;++p)for(var b=0,g=kl(e,d+1,d=bl(y=a[p])),v=e;b<h;++b)(v=wl(y>0?f[b]+" "+g:_l(g,/&\f/g,f[b])))&&(l[m++]=v);return Ll(e,t,n,0===i?eu:s,l,u,c)}function lu(e,t,n){return Ll(e,t,n,Zl,gl(Ml),kl(e,2,-2),0)}function uu(e,t,n,r){return Ll(e,t,n,tu,kl(e,0,r),kl(e,r+1,-1),r)}var cu=function(e,t,n){for(var r=0,i=0;r=i,i=Rl(),38===r&&12===i&&(t[n]=1),!jl(i);)Pl();return Ul(e,Nl)},du=function(e,t){return Wl(function(e,t){var n=-1,r=44;do{switch(jl(r)){case 0:38===r&&12===Rl()&&(t[n]=1),e[n]+=cu(Nl-1,t,n);break;case 2:e[n]+=$l(r);break;case 4:if(44===r){e[++n]=58===Rl()?"&\f":"",t[n]=e[n].length;break}default:e[n]+=gl(r)}}while(r=Pl());return e}(Vl(e),t))},fu=new WeakMap,hu=function(e){if("rule"===e.type&&e.parent&&!(e.length<1)){for(var t=e.value,n=e.parent,r=e.column===n.column&&e.line===n.line;"rule"!==n.type;)if(!(n=n.parent))return;if((1!==e.props.length||58===t.charCodeAt(0)||fu.get(n))&&!r){fu.set(e,!0);for(var i=[],o=du(t,i),a=n.props,s=0,l=0;s<o.length;s++)for(var u=0;u<a.length;u++,l++)e.props[l]=i[s]?o[s].replace(/&\f/g,a[u]):a[u]+" "+o[s]}}},pu=function(e){if("decl"===e.type){var t=e.value;108===t.charCodeAt(0)&&98===t.charCodeAt(2)&&(e.return="",e.value="")}};function yu(e,t){switch(function(e,t){return 45^xl(e,0)?(((t<<2^xl(e,0))<<2^xl(e,1))<<2^xl(e,2))<<2^xl(e,3):0}(e,t)){case 5103:return Jl+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return Jl+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return Jl+e+Xl+e+Gl+e+e;case 6828:case 4268:return Jl+e+Gl+e+e;case 6165:return Jl+e+Gl+"flex-"+e+e;case 5187:return Jl+e+_l(e,/(\w+).+(:[^]+)/,Jl+"box-$1$2"+Gl+"flex-$1$2")+e;case 5443:return Jl+e+Gl+"flex-item-"+_l(e,/flex-|-self/,"")+e;case 4675:return Jl+e+Gl+"flex-line-pack"+_l(e,/align-content|flex-|-self/,"")+e;case 5548:return Jl+e+Gl+_l(e,"shrink","negative")+e;case 5292:return Jl+e+Gl+_l(e,"basis","preferred-size")+e;case 6060:return Jl+"box-"+_l(e,"-grow","")+Jl+e+Gl+_l(e,"grow","positive")+e;case 4554:return Jl+_l(e,/([^-])(transform)/g,"$1"+Jl+"$2")+e;case 6187:return _l(_l(_l(e,/(zoom-|grab)/,Jl+"$1"),/(image-set)/,Jl+"$1"),e,"")+e;case 5495:case 3959:return _l(e,/(image-set\([^]*)/,Jl+"$1$`$1");case 4968:return _l(_l(e,/(.+:)(flex-)?(.*)/,Jl+"box-pack:$3"+Gl+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+Jl+e+e;case 4095:case 3583:case 4068:case 2532:return _l(e,/(.+)-inline(.+)/,Jl+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(Il(e)-1-t>6)switch(xl(e,t+1)){case 109:if(45!==xl(e,t+4))break;case 102:return _l(e,/(.+:)(.+)-([^]+)/,"$1"+Jl+"$2-$3$1"+Xl+(108==xl(e,t+3)?"$3":"$2-$3"))+e;case 115:return~Sl(e,"stretch")?yu(_l(e,"stretch","fill-available"),t)+e:e}break;case 4949:if(115!==xl(e,t+1))break;case 6444:switch(xl(e,Il(e)-3-(~Sl(e,"!important")&&10))){case 107:return _l(e,":",":"+Jl)+e;case 101:return _l(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+Jl+(45===xl(e,14)?"inline-":"")+"box$3$1"+Jl+"$2$3$1"+Gl+"$2box$3")+e}break;case 5936:switch(xl(e,t+11)){case 114:return Jl+e+Gl+_l(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return Jl+e+Gl+_l(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return Jl+e+Gl+_l(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return Jl+e+Gl+e+e}return e}var mu=[function(e,t,n,r){if(e.length>-1&&!e.return)switch(e.type){case tu:e.return=yu(e.value,e.length);break;case nu:return ru([Dl(e,{value:_l(e.value,"@","@"+Jl)})],r);case eu:if(e.length)return function(e,t){return e.map(t).join("")}(e.props,(function(t){switch(function(e,t){return(e=t.exec(e))?e[0]:e}(t,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return ru([Dl(e,{props:[_l(t,/:(read-\w+)/,":-moz-$1")]})],r);case"::placeholder":return ru([Dl(e,{props:[_l(t,/:(plac\w+)/,":"+Jl+"input-$1")]}),Dl(e,{props:[_l(t,/:(plac\w+)/,":-moz-$1")]}),Dl(e,{props:[_l(t,/:(plac\w+)/,Gl+"input-$1")]})],r)}return""}))}}],bu=function(e){var t=e.key;if("css"===t){var n=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(n,(function(e){-1!==e.getAttribute("data-emotion").indexOf(" ")&&(document.head.appendChild(e),e.setAttribute("data-s",""))}))}var r=e.stylisPlugins||mu;var i,o,a={},s=[];i=e.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+t+' "]'),(function(e){for(var t=e.getAttribute("data-emotion").split(" "),n=1;n<t.length;n++)a[t[n]]=!0;s.push(e)}));var l,u,c=[iu,(u=function(e){l.insert(e)},function(e){e.root||(e=e.return)&&u(e)})],d=function(e){var t=Tl(e);return function(n,r,i,o){for(var a="",s=0;s<t;s++)a+=e[s](n,r,i,o)||"";return a}}([hu,pu].concat(r,c));o=function(e,t,n,r){l=n,ru(ou(e?e+"{"+t.styles+"}":t.styles),d),r&&(f.inserted[t.name]=!0)};var f={key:t,sheet:new ml({key:t,container:i,nonce:e.nonce,speedy:e.speedy,prepend:e.prepend,insertionPoint:e.insertionPoint}),nonce:e.nonce,inserted:a,registered:{},insert:o};return f.sheet.hydrate(s),f};var gu={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},vu=/[A-Z]|^ms/g,wu=/_EMO_([^_]+?)_([^]*?)_EMO_/g,_u=function(e){return 45===e.charCodeAt(1)},Su=function(e){return null!=e&&"boolean"!==typeof e},xu=hl((function(e){return _u(e)?e:e.replace(vu,"-$&").toLowerCase()})),ku=function(e,t){switch(e){case"animation":case"animationName":if("string"===typeof t)return t.replace(wu,(function(e,t,n){return Tu={name:t,styles:n,next:Tu},t}))}return 1===gu[e]||_u(e)||"number"!==typeof t||0===t?t:t+"px"};function Iu(e,t,n){if(null==n)return"";if(void 0!==n.__emotion_styles)return n;switch(typeof n){case"boolean":return"";case"object":if(1===n.anim)return Tu={name:n.name,styles:n.styles,next:Tu},n.name;if(void 0!==n.styles){var r=n.next;if(void 0!==r)for(;void 0!==r;)Tu={name:r.name,styles:r.styles,next:Tu},r=r.next;return n.styles+";"}return function(e,t,n){var r="";if(Array.isArray(n))for(var i=0;i<n.length;i++)r+=Iu(e,t,n[i])+";";else for(var o in n){var a=n[o];if("object"!==typeof a)null!=t&&void 0!==t[a]?r+=o+"{"+t[a]+"}":Su(a)&&(r+=xu(o)+":"+ku(o,a)+";");else if(!Array.isArray(a)||"string"!==typeof a[0]||null!=t&&void 0!==t[a[0]]){var s=Iu(e,t,a);switch(o){case"animation":case"animationName":r+=xu(o)+":"+s+";";break;default:r+=o+"{"+s+"}"}}else for(var l=0;l<a.length;l++)Su(a[l])&&(r+=xu(o)+":"+ku(o,a[l])+";")}return r}(e,t,n);case"function":if(void 0!==e){var i=Tu,o=n(e);return Tu=i,Iu(e,t,o)}}if(null==t)return n;var a=t[n];return void 0!==a?a:n}var Tu,Ou=/label:\s*([^\s;\n{]+)\s*(;|$)/g;var Au=!!d.useInsertionEffect&&d.useInsertionEffect,Cu=Au||function(e){return e()},Eu=(Au||c.useLayoutEffect,c.createContext("undefined"!==typeof HTMLElement?bu({key:"css"}):null));Eu.Provider;var Nu=function(e){return(0,c.forwardRef)((function(t,n){var r=(0,c.useContext)(Eu);return e(t,r,n)}))};var Mu=c.createContext({});var Bu=function(e,t,n){var r=e.key+"-"+t.name;!1===n&&void 0===e.registered[r]&&(e.registered[r]=t.styles)},Lu=yl,Du=function(e){return"theme"!==e},Fu=function(e){return"string"===typeof e&&e.charCodeAt(0)>96?Lu:Du},Pu=function(e,t,n){var r;if(t){var i=t.shouldForwardProp;r=e.__emotion_forwardProp&&i?function(t){return e.__emotion_forwardProp(t)&&i(t)}:i}return"function"!==typeof r&&n&&(r=e.__emotion_forwardProp),r},Ru=function(e){var t=e.cache,n=e.serialized,r=e.isStringTag;return Bu(t,n,r),Cu((function(){return function(e,t,n){Bu(e,t,n);var r=e.key+"-"+t.name;if(void 0===e.inserted[t.name]){var i=t;do{e.insert(t===i?"."+r:"",i,e.sheet,!0),i=i.next}while(void 0!==i)}}(t,n,r)})),null},zu=function e(t,n){var r,i,o=t.__emotion_real===t,a=o&&t.__emotion_base||t;void 0!==n&&(r=n.label,i=n.target);var s=Pu(t,n,o),l=s||Fu(a),u=!l("as");return function(){var d=arguments,f=o&&void 0!==t.__emotion_styles?t.__emotion_styles.slice(0):[];if(void 0!==r&&f.push("label:"+r+";"),null==d[0]||void 0===d[0].raw)f.push.apply(f,d);else{0,f.push(d[0][0]);for(var h=d.length,p=1;p<h;p++)f.push(d[p],d[0][p])}var y=Nu((function(e,t,n){var r=u&&e.as||a,o="",d=[],h=e;if(null==e.theme){for(var p in h={},e)h[p]=e[p];h.theme=c.useContext(Mu)}"string"===typeof e.className?o=function(e,t,n){var r="";return n.split(" ").forEach((function(n){void 0!==e[n]?t.push(e[n]+";"):r+=n+" "})),r}(t.registered,d,e.className):null!=e.className&&(o=e.className+" ");var y=function(e,t,n){if(1===e.length&&"object"===typeof e[0]&&null!==e[0]&&void 0!==e[0].styles)return e[0];var r=!0,i="";Tu=void 0;var o=e[0];null==o||void 0===o.raw?(r=!1,i+=Iu(n,t,o)):i+=o[0];for(var a=1;a<e.length;a++)i+=Iu(n,t,e[a]),r&&(i+=o[a]);Ou.lastIndex=0;for(var s,l="";null!==(s=Ou.exec(i));)l+="-"+s[1];var u=function(e){for(var t,n=0,r=0,i=e.length;i>=4;++r,i-=4)t=1540483477*(65535&(t=255&e.charCodeAt(r)|(255&e.charCodeAt(++r))<<8|(255&e.charCodeAt(++r))<<16|(255&e.charCodeAt(++r))<<24))+(59797*(t>>>16)<<16),n=1540483477*(65535&(t^=t>>>24))+(59797*(t>>>16)<<16)^1540483477*(65535&n)+(59797*(n>>>16)<<16);switch(i){case 3:n^=(255&e.charCodeAt(r+2))<<16;case 2:n^=(255&e.charCodeAt(r+1))<<8;case 1:n=1540483477*(65535&(n^=255&e.charCodeAt(r)))+(59797*(n>>>16)<<16)}return(((n=1540483477*(65535&(n^=n>>>13))+(59797*(n>>>16)<<16))^n>>>15)>>>0).toString(36)}(i)+l;return{name:u,styles:i,next:Tu}}(f.concat(d),t.registered,h);o+=t.key+"-"+y.name,void 0!==i&&(o+=" "+i);var m=u&&void 0===s?Fu(r):l,b={};for(var g in e)u&&"as"===g||m(g)&&(b[g]=e[g]);return b.className=o,b.ref=n,c.createElement(c.Fragment,null,c.createElement(Ru,{cache:t,serialized:y,isStringTag:"string"===typeof r}),c.createElement(r,b))}));return y.displayName=void 0!==r?r:"Styled("+("string"===typeof a?a:a.displayName||a.name||"Component")+")",y.defaultProps=t.defaultProps,y.__emotion_real=y,y.__emotion_base=a,y.__emotion_styles=f,y.__emotion_forwardProp=s,Object.defineProperty(y,"toString",{value:function(){return"."+i}}),y.withComponent=function(t,r){return e(t,ca({},n,r,{shouldForwardProp:Pu(y,r,!0)})).apply(void 0,f)},y}}.bind();["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"].forEach((function(e){zu[e]=zu(e)}));const Uu=["variant"];function ju(e){return 0===e.length}function Vu(e){const{variant:t}=e,n=da(e,Uu);let r=t||"";return Object.keys(n).sort().forEach((t=>{r+="color"===t?ju(r)?e[t]:Ia(e[t]):"".concat(ju(r)?t:Ia(t)).concat(Ia(e[t].toString()))})),r}const Wu=["name","slot","skipVariantsResolver","skipSx","overridesResolver"];const $u=e=>{const t={};return e&&e.forEach((e=>{const n=Vu(e.props);t[n]=e.style})),t},Hu=(e,t,n)=>{const{ownerState:r={}}=e,i=[];return n&&n.forEach((n=>{let o=!0;Object.keys(n.props).forEach((t=>{r[t]!==n.props[t]&&e[t]!==n.props[t]&&(o=!1)})),o&&i.push(t[Vu(n.props)])})),i};function Yu(e){return"ownerState"!==e&&"theme"!==e&&"sx"!==e&&"as"!==e}const Ku=Ss(),Qu=e=>e?e.charAt(0).toLowerCase()+e.slice(1):e;function qu(e){let{defaultTheme:t,theme:n,themeId:r}=e;return i=n,0===Object.keys(i).length?t:n[r]||n;var i}function Gu(e){return e?(t,n)=>n[e]:null}const Xu=e=>{let{styledArg:t,props:n,defaultTheme:r,themeId:i}=e;const o=t(ca({},n,{theme:qu(ca({},n,{defaultTheme:r,themeId:i}))}));let a;if(o&&o.variants&&(a=o.variants,delete o.variants),a){return[o,...Hu(n,$u(a),a)]}return o};function Ju(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{themeId:t,defaultTheme:n=Ku,rootShouldForwardProp:r=Yu,slotShouldForwardProp:i=Yu}=e,o=e=>ws(ca({},e,{theme:qu(ca({},e,{defaultTheme:n,themeId:t}))}));return o.__mui_systemSx=!0,function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};((e,t)=>{Array.isArray(e.__emotion_styles)&&(e.__emotion_styles=t(e.__emotion_styles))})(e,(e=>e.filter((e=>!(null!=e&&e.__mui_systemSx)))));const{name:s,slot:l,skipVariantsResolver:u,skipSx:c,overridesResolver:d=Gu(Qu(l))}=a,f=da(a,Wu),h=void 0!==u?u:l&&"Root"!==l&&"root"!==l||!1,p=c||!1;let y=Yu;"Root"===l||"root"===l?y=r:l?y=i:function(e){return"string"===typeof e&&e.charCodeAt(0)>96}(e)&&(y=void 0);const m=function(e,t){return zu(e,t)}(e,ca({shouldForwardProp:y,label:undefined},f)),b=function(r){for(var i=arguments.length,a=new Array(i>1?i-1:0),l=1;l<i;l++)a[l-1]=arguments[l];const u=a?a.map((e=>{if("function"===typeof e&&e.__emotion_real!==e)return r=>Xu({styledArg:e,props:r,defaultTheme:n,themeId:t});if(ha(e)){let t,n=e;return e&&e.variants&&(t=e.variants,delete n.variants,n=n=>{let r=e;return Hu(n,$u(t),t).forEach((e=>{r=ya(r,e)})),r}),n}return e})):[];let c=r;if(ha(r)){let e;r&&r.variants&&(e=r.variants,delete c.variants,c=t=>{let n=r;return Hu(t,$u(e),e).forEach((e=>{n=ya(n,e)})),n})}else"function"===typeof r&&r.__emotion_real!==r&&(c=e=>Xu({styledArg:r,props:e,defaultTheme:n,themeId:t}));s&&d&&u.push((e=>{const r=qu(ca({},e,{defaultTheme:n,themeId:t})),i=((e,t)=>t.components&&t.components[e]&&t.components[e].styleOverrides?t.components[e].styleOverrides:null)(s,r);if(i){const t={};return Object.entries(i).forEach((n=>{let[i,o]=n;t[i]="function"===typeof o?o(ca({},e,{theme:r})):o})),d(e,t)}return null})),s&&!h&&u.push((e=>{const r=qu(ca({},e,{defaultTheme:n,themeId:t}));return((e,t,n,r)=>{var i;const o=null==n||null==(i=n.components)||null==(i=i[r])?void 0:i.variants;return Hu(e,t,o)})(e,((e,t)=>{let n=[];return t&&t.components&&t.components[e]&&t.components[e].variants&&(n=t.components[e].variants),$u(n)})(s,r),r,s)})),p||u.push(o);const f=u.length-a.length;if(Array.isArray(r)&&f>0){const e=new Array(f).fill("");c=[...r,...e],c.raw=[...r.raw,...e]}const y=m(c,...u);return e.muiName&&(y.muiName=e.muiName),y};return m.withConfig&&(b.withConfig=m.withConfig),b}}const Zu=Ju();function ec(e,t){const n=ca({},t);return Object.keys(e).forEach((r=>{if(r.toString().match(/^(components|slots)$/))n[r]=ca({},e[r],n[r]);else if(r.toString().match(/^(componentsProps|slotProps)$/)){const i=e[r]||{},o=t[r];n[r]={},o&&Object.keys(o)?i&&Object.keys(i)?(n[r]=ca({},o),Object.keys(i).forEach((e=>{n[r][e]=ec(i[e],o[e])}))):n[r]=o:n[r]=i}else void 0===n[r]&&(n[r]=e[r])})),n}function tc(e){const{theme:t,name:n,props:r}=e;return t&&t.components&&t.components[n]&&t.components[n].defaultProps?ec(t.components[n].defaultProps,r):r}const nc=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;const t=c.useContext(Mu);return t&&(n=t,0!==Object.keys(n).length)?t:e;var n},rc=Ss();const ic=function(){return nc(arguments.length>0&&void 0!==arguments[0]?arguments[0]:rc)};function oc(e){let{props:t,name:n,defaultTheme:r,themeId:i}=e,o=ic(r);i&&(o=o[i]||o);return tc({theme:o,name:n,props:t})}const ac=["sx"],sc=e=>{var t,n;const r={systemProps:{},otherProps:{}},i=null!=(t=null==e||null==(n=e.theme)?void 0:n.unstable_sxConfig)?t:gs;return Object.keys(e).forEach((t=>{i[t]?r.systemProps[t]=e[t]:r.otherProps[t]=e[t]})),r};var lc=n(184);const uc=["component","direction","spacing","divider","children","className","useFlexGap"],cc=Ss(),dc=Zu("div",{name:"MuiStack",slot:"Root",overridesResolver:(e,t)=>t.root});function fc(e){return oc({props:e,name:"MuiStack",defaultTheme:cc})}function hc(e,t){const n=c.Children.toArray(e).filter(Boolean);return n.reduce(((e,r,i)=>(e.push(r),i<n.length-1&&e.push(c.cloneElement(t,{key:"separator-".concat(i)})),e)),[])}const pc=e=>{let{ownerState:t,theme:n}=e,r=ca({display:"flex",flexDirection:"column"},_a({theme:n},ka({values:t.direction,breakpoints:n.breakpoints.values}),(e=>({flexDirection:e}))));if(t.spacing){const e=Ra(n),i=Object.keys(n.breakpoints.values).reduce(((e,n)=>(("object"===typeof t.spacing&&null!=t.spacing[n]||"object"===typeof t.direction&&null!=t.direction[n])&&(e[n]=!0),e)),{}),o=ka({values:t.direction,base:i}),a=ka({values:t.spacing,base:i});"object"===typeof o&&Object.keys(o).forEach(((e,t,n)=>{if(!o[e]){const r=t>0?o[n[t-1]]:"column";o[e]=r}}));r=ya(r,_a({theme:n},a,((n,r)=>{return t.useFlexGap?{gap:za(e,n)}:{"& > :not(style):not(style)":{margin:0},"& > :not(style) ~ :not(style)":{["margin".concat((i=r?o[r]:t.direction,{row:"Left","row-reverse":"Right",column:"Top","column-reverse":"Bottom"}[i]))]:za(e,n)}};var i})))}return r=function(e){const t=Sa(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];const o=[t,...r].reduce(((e,t)=>ya(e,t)),{});return xa(Object.keys(t),o)}(n.breakpoints,r),r};const yc=ol(),mc="$$material",bc=Yu,gc=Ju({themeId:mc,defaultTheme:yc,rootShouldForwardProp:e=>Yu(e)&&"classes"!==e});function vc(e){let{props:t,name:n}=e;return oc({props:t,name:n,defaultTheme:yc,themeId:mc})}const wc=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{createStyledComponent:t=dc,useThemeProps:n=fc,componentName:r="MuiStack"}=e,i=t(pc),o=c.forwardRef((function(e,t){const o=function(e){const{sx:t}=e,n=da(e,ac),{systemProps:r,otherProps:i}=sc(n);let o;return o=Array.isArray(t)?[r,...t]:"function"===typeof t?function(){const e=t(...arguments);return ha(e)?ca({},r,e):r}:ca({},r,t),ca({},i,{sx:o})}(n(e)),{component:a="div",direction:s="column",spacing:l=0,divider:u,children:c,className:d,useFlexGap:f=!1}=o,h=da(o,uc),p={direction:s,spacing:l,useFlexGap:f},y=ll({root:["root"]},(e=>fl(r,e)),{});return(0,lc.jsx)(i,ca({as:a,ownerState:p,ref:t,className:sl(y.root,d)},h,{children:u?hc(c,u):c}))}));return o}({createStyledComponent:gc("div",{name:"MuiStack",slot:"Root",overridesResolver:(e,t)=>t.root}),useThemeProps:e=>vc({props:e,name:"MuiStack"})}),_c=wc;function Sc(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return c.useMemo((()=>t.every((e=>null==e))?null:e=>{t.forEach((t=>{!function(e,t){"function"===typeof e?e(t):e&&(e.current=t)}(t,e)}))}),t)}function xc(e){return"string"===typeof e}function kc(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(void 0===e)return{};const n={};return Object.keys(e).filter((n=>n.match(/^on[A-Z]/)&&"function"===typeof e[n]&&!t.includes(n))).forEach((t=>{n[t]=e[t]})),n}function Ic(e){if(void 0===e)return{};const t={};return Object.keys(e).filter((t=>!(t.match(/^on[A-Z]/)&&"function"===typeof e[t]))).forEach((n=>{t[n]=e[n]})),t}const Tc=["elementType","externalSlotProps","ownerState","skipResolvingSlotProps"];function Oc(e){var t;const{elementType:n,externalSlotProps:r,ownerState:i,skipResolvingSlotProps:o=!1}=e,a=da(e,Tc),s=o?{}:function(e,t,n){return"function"===typeof e?e(t,n):e}(r,i),{props:l,internalRef:u}=function(e){const{getSlotProps:t,additionalProps:n,externalSlotProps:r,externalForwardedProps:i,className:o}=e;if(!t){const e=sl(null==n?void 0:n.className,o,null==i?void 0:i.className,null==r?void 0:r.className),t=ca({},null==n?void 0:n.style,null==i?void 0:i.style,null==r?void 0:r.style),a=ca({},n,i,r);return e.length>0&&(a.className=e),Object.keys(t).length>0&&(a.style=t),{props:a,internalRef:void 0}}const a=kc(ca({},i,r)),s=Ic(r),l=Ic(i),u=t(a),c=sl(null==u?void 0:u.className,null==n?void 0:n.className,o,null==i?void 0:i.className,null==r?void 0:r.className),d=ca({},null==u?void 0:u.style,null==n?void 0:n.style,null==i?void 0:i.style,null==r?void 0:r.style),f=ca({},u,n,l,s);return c.length>0&&(f.className=c),Object.keys(d).length>0&&(f.style=d),{props:f,internalRef:u.ref}}(ca({},a,{externalSlotProps:s})),c=function(e,t,n){return void 0===e||xc(e)?t:ca({},t,{ownerState:ca({},t.ownerState,n)})}(n,ca({},l,{ref:Sc(u,null==s?void 0:s.ref,null==(t=e.additionalProps)?void 0:t.ref)}),i);return c}function Ac(e){return e&&e.ownerDocument||document}let Cc,Ec=!0,Nc=!1;const Mc={text:!0,search:!0,url:!0,tel:!0,email:!0,password:!0,number:!0,date:!0,month:!0,week:!0,time:!0,datetime:!0,"datetime-local":!0};function Bc(e){e.metaKey||e.altKey||e.ctrlKey||(Ec=!0)}function Lc(){Ec=!1}function Dc(){"hidden"===this.visibilityState&&Nc&&(Ec=!0)}function Fc(e){const{target:t}=e;try{return t.matches(":focus-visible")}catch(n){}return Ec||function(e){const{type:t,tagName:n}=e;return!("INPUT"!==n||!Mc[t]||e.readOnly)||"TEXTAREA"===n&&!e.readOnly||!!e.isContentEditable}(t)}function Pc(){const e=c.useCallback((e=>{var t;null!=e&&((t=e.ownerDocument).addEventListener("keydown",Bc,!0),t.addEventListener("mousedown",Lc,!0),t.addEventListener("pointerdown",Lc,!0),t.addEventListener("touchstart",Lc,!0),t.addEventListener("visibilitychange",Dc,!0))}),[]),t=c.useRef(!1);return{isFocusVisibleRef:t,onFocus:function(e){return!!Fc(e)&&(t.current=!0,!0)},onBlur:function(){return!!t.current&&(Nc=!0,window.clearTimeout(Cc),Cc=window.setTimeout((()=>{Nc=!1}),100),t.current=!1,!0)},ref:e}}const Rc="undefined"!==typeof window?c.useLayoutEffect:c.useEffect;const zc=function(e){const t=c.useRef(e);return Rc((()=>{t.current=e})),c.useRef((function(){return(0,t.current)(...arguments)})).current},Uc={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:-1,overflow:"hidden",padding:0,position:"absolute",whiteSpace:"nowrap",width:"1px"};function jc(e,t){return e-t}function Vc(e,t,n){return null==e?t:Math.min(Math.max(t,e),n)}function Wc(e,t){var n;const{index:r}=null!=(n=e.reduce(((e,n,r)=>{const i=Math.abs(t-n);return null===e||i<e.distance||i===e.distance?{distance:i,index:r}:e}),null))?n:{};return r}function $c(e,t){if(void 0!==t.current&&e.changedTouches){const n=e;for(let e=0;e<n.changedTouches.length;e+=1){const r=n.changedTouches[e];if(r.identifier===t.current)return{x:r.clientX,y:r.clientY}}return!1}return{x:e.clientX,y:e.clientY}}function Hc(e,t,n){return 100*(e-t)/(n-t)}function Yc(e,t,n){const r=Math.round((e-n)/t)*t+n;return Number(r.toFixed(function(e){if(Math.abs(e)<1){const t=e.toExponential().split("e-"),n=t[0].split(".")[1];return(n?n.length:0)+parseInt(t[1],10)}const t=e.toString().split(".")[1];return t?t.length:0}(t)))}function Kc(e){let{values:t,newValue:n,index:r}=e;const i=t.slice();return i[r]=n,i.sort(jc)}function Qc(e){let{sliderRef:t,activeIndex:n,setActive:r}=e;var i,o;const a=Ac(t.current);var s;null!=(i=t.current)&&i.contains(a.activeElement)&&Number(null==a||null==(o=a.activeElement)?void 0:o.getAttribute("data-index"))===n||(null==(s=t.current)||s.querySelector('[type="range"][data-index="'.concat(n,'"]')).focus());r&&r(n)}function qc(e,t){return"number"===typeof e&&"number"===typeof t?e===t:"object"===typeof e&&"object"===typeof t&&function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:(e,t)=>e===t;return e.length===t.length&&e.every(((e,r)=>n(e,t[r])))}(e,t)}const Gc={horizontal:{offset:e=>({left:"".concat(e,"%")}),leap:e=>({width:"".concat(e,"%")})},"horizontal-reverse":{offset:e=>({right:"".concat(e,"%")}),leap:e=>({width:"".concat(e,"%")})},vertical:{offset:e=>({bottom:"".concat(e,"%")}),leap:e=>({height:"".concat(e,"%")})}},Xc=e=>e;let Jc;function Zc(){return void 0===Jc&&(Jc="undefined"===typeof CSS||"function"!==typeof CSS.supports||CSS.supports("touch-action","none")),Jc}function ed(e){const{"aria-labelledby":t,defaultValue:n,disabled:r=!1,disableSwap:i=!1,isRtl:o=!1,marks:a=!1,max:s=100,min:l=0,name:u,onChange:d,onChangeCommitted:f,orientation:h="horizontal",rootRef:p,scale:y=Xc,step:m=1,tabIndex:b,value:g}=e,v=c.useRef(),[w,_]=c.useState(-1),[S,x]=c.useState(-1),[k,I]=c.useState(!1),T=c.useRef(0),[O,A]=function(e){let{controlled:t,default:n,name:r,state:i="value"}=e;const{current:o}=c.useRef(void 0!==t),[a,s]=c.useState(n);return[o?t:a,c.useCallback((e=>{o||s(e)}),[])]}({controlled:g,default:null!=n?n:l,name:"Slider"}),C=d&&((e,t,n)=>{const r=e.nativeEvent||e,i=new r.constructor(r.type,r);Object.defineProperty(i,"target",{writable:!0,value:{value:t,name:u}}),d(i,t,n)}),E=Array.isArray(O);let N=E?O.slice().sort(jc):[O];N=N.map((e=>Vc(e,l,s)));const M=!0===a&&null!==m?[...Array(Math.floor((s-l)/m)+1)].map(((e,t)=>({value:l+m*t}))):a||[],B=M.map((e=>e.value)),{isFocusVisibleRef:L,onBlur:D,onFocus:F,ref:P}=Pc(),[R,z]=c.useState(-1),U=c.useRef(),j=Sc(P,U),V=Sc(p,j),W=e=>t=>{var n;const r=Number(t.currentTarget.getAttribute("data-index"));F(t),!0===L.current&&z(r),x(r),null==e||null==(n=e.onFocus)||n.call(e,t)},$=e=>t=>{var n;D(t),!1===L.current&&z(-1),x(-1),null==e||null==(n=e.onBlur)||n.call(e,t)};Rc((()=>{var e;r&&U.current.contains(document.activeElement)&&(null==(e=document.activeElement)||e.blur())}),[r]),r&&-1!==w&&_(-1),r&&-1!==R&&z(-1);const H=c.useRef();let Y=h;o&&"horizontal"===h&&(Y+="-reverse");const K=e=>{let{finger:t,move:n=!1}=e;const{current:r}=U,{width:o,height:a,bottom:u,left:c}=r.getBoundingClientRect();let d,f;if(d=0===Y.indexOf("vertical")?(u-t.y)/a:(t.x-c)/o,-1!==Y.indexOf("-reverse")&&(d=1-d),f=function(e,t,n){return(n-t)*e+t}(d,l,s),m)f=Yc(f,m,l);else{const e=Wc(B,f);f=B[e]}f=Vc(f,l,s);let h=0;if(E){h=n?H.current:Wc(N,f),i&&(f=Vc(f,N[h-1]||-1/0,N[h+1]||1/0));const e=f;f=Kc({values:N,newValue:f,index:h}),i&&n||(h=f.indexOf(e),H.current=h)}return{newValue:f,activeIndex:h}},Q=zc((e=>{const t=$c(e,v);if(!t)return;if(T.current+=1,"mousemove"===e.type&&0===e.buttons)return void q(e);const{newValue:n,activeIndex:r}=K({finger:t,move:!0});Qc({sliderRef:U,activeIndex:r,setActive:_}),A(n),!k&&T.current>2&&I(!0),C&&!qc(n,O)&&C(e,n,r)})),q=zc((e=>{const t=$c(e,v);if(I(!1),!t)return;const{newValue:n}=K({finger:t,move:!0});_(-1),"touchend"===e.type&&x(-1),f&&f(e,n),v.current=void 0,X()})),G=zc((e=>{if(r)return;Zc()||e.preventDefault();const t=e.changedTouches[0];null!=t&&(v.current=t.identifier);const n=$c(e,v);if(!1!==n){const{newValue:t,activeIndex:r}=K({finger:n});Qc({sliderRef:U,activeIndex:r,setActive:_}),A(t),C&&!qc(t,O)&&C(e,t,r)}T.current=0;const i=Ac(U.current);i.addEventListener("touchmove",Q),i.addEventListener("touchend",q)})),X=c.useCallback((()=>{const e=Ac(U.current);e.removeEventListener("mousemove",Q),e.removeEventListener("mouseup",q),e.removeEventListener("touchmove",Q),e.removeEventListener("touchend",q)}),[q,Q]);c.useEffect((()=>{const{current:e}=U;return e.addEventListener("touchstart",G,{passive:Zc()}),()=>{e.removeEventListener("touchstart",G,{passive:Zc()}),X()}}),[X,G]),c.useEffect((()=>{r&&X()}),[r,X]);const J=Hc(E?N[0]:l,l,s),Z=Hc(N[N.length-1],l,s)-J,ee=e=>t=>{var n;null==(n=e.onMouseLeave)||n.call(e,t),x(-1)};return{active:w,axis:Y,axisProps:Gc,dragging:k,focusedThumbIndex:R,getHiddenInputProps:function(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};var a;const c=kc(n);var d;const p=ca({},c,{onChange:(d=c||{},e=>{var t;null==(t=d.onChange)||t.call(d,e);const n=Number(e.currentTarget.getAttribute("data-index")),r=N[n],o=B.indexOf(r);let a=e.target.valueAsNumber;if(M&&null==m){const e=B[B.length-1];a=a>e?e:a<B[0]?B[0]:a<r?B[o-1]:B[o+1]}if(a=Vc(a,l,s),E){i&&(a=Vc(a,N[n-1]||-1/0,N[n+1]||1/0));const e=a;a=Kc({values:N,newValue:a,index:n});let t=n;i||(t=a.indexOf(e)),Qc({sliderRef:U,activeIndex:t})}A(a),z(n),C&&!qc(a,O)&&C(e,a,n),f&&f(e,a)}),onFocus:W(c||{}),onBlur:$(c||{})});return ca({tabIndex:b,"aria-labelledby":t,"aria-orientation":h,"aria-valuemax":y(s),"aria-valuemin":y(l),name:u,type:"range",min:e.min,max:e.max,step:null===e.step&&e.marks?"any":null!=(a=e.step)?a:void 0,disabled:r},n,p,{style:ca({},Uc,{direction:o?"rtl":"ltr",width:"100%",height:"100%"})})},getRootProps:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=kc(e);var n;const i=ca({},t,{onMouseDown:(n=t||{},e=>{var t;if(null==(t=n.onMouseDown)||t.call(n,e),r)return;if(e.defaultPrevented)return;if(0!==e.button)return;e.preventDefault();const i=$c(e,v);if(!1!==i){const{newValue:t,activeIndex:n}=K({finger:i});Qc({sliderRef:U,activeIndex:n,setActive:_}),A(t),C&&!qc(t,O)&&C(e,t,n)}T.current=0;const o=Ac(U.current);o.addEventListener("mousemove",Q),o.addEventListener("mouseup",q)})});return ca({},e,{ref:V},i)},getThumbProps:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=kc(e);var n;return ca({},e,t,{onMouseOver:(n=t||{},e=>{var t;null==(t=n.onMouseOver)||t.call(n,e);const r=Number(e.currentTarget.getAttribute("data-index"));x(r)}),onMouseLeave:ee(t||{})})},marks:M,open:S,range:E,rootRef:V,trackLeap:Z,trackOffset:J,values:N,getThumbStyle:e=>({pointerEvents:-1!==w&&w!==e?"none":void 0})}}const td=e=>!e||!xc(e),nd=Ia;function rd(e){return fl("MuiSlider",e)}const id=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"Mui";const r={};return t.forEach((t=>{r[t]=fl(e,t,n)})),r}("MuiSlider",["root","active","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","disabled","dragging","focusVisible","mark","markActive","marked","markLabel","markLabelActive","rail","sizeSmall","thumb","thumbColorPrimary","thumbColorSecondary","thumbColorError","thumbColorSuccess","thumbColorInfo","thumbColorWarning","track","trackInverted","trackFalse","thumbSizeSmall","valueLabel","valueLabelOpen","valueLabelCircle","valueLabelLabel","vertical"]);const od=["aria-label","aria-valuetext","aria-labelledby","component","components","componentsProps","color","classes","className","disableSwap","disabled","getAriaLabel","getAriaValueText","marks","max","min","name","onChange","onChangeCommitted","orientation","size","step","scale","slotProps","slots","tabIndex","track","value","valueLabelDisplay","valueLabelFormat"];function ad(e){return e}const sd=gc("span",{name:"MuiSlider",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,t["color".concat(nd(n.color))],"medium"!==n.size&&t["size".concat(nd(n.size))],n.marked&&t.marked,"vertical"===n.orientation&&t.vertical,"inverted"===n.track&&t.trackInverted,!1===n.track&&t.trackFalse]}})((e=>{let{theme:t,ownerState:n}=e;return ca({borderRadius:12,boxSizing:"content-box",display:"inline-block",position:"relative",cursor:"pointer",touchAction:"none",color:(t.vars||t).palette[n.color].main,WebkitTapHighlightColor:"transparent"},"horizontal"===n.orientation&&ca({height:4,width:"100%",padding:"13px 0","@media (pointer: coarse)":{padding:"20px 0"}},"small"===n.size&&{height:2},n.marked&&{marginBottom:20}),"vertical"===n.orientation&&ca({height:"100%",width:4,padding:"0 13px","@media (pointer: coarse)":{padding:"0 20px"}},"small"===n.size&&{width:2},n.marked&&{marginRight:44}),{"@media print":{colorAdjust:"exact"},["&.".concat(id.disabled)]:{pointerEvents:"none",cursor:"default",color:(t.vars||t).palette.grey[400]},["&.".concat(id.dragging)]:{["& .".concat(id.thumb,", & .").concat(id.track)]:{transition:"none"}}})})),ld=gc("span",{name:"MuiSlider",slot:"Rail",overridesResolver:(e,t)=>t.rail})((e=>{let{ownerState:t}=e;return ca({display:"block",position:"absolute",borderRadius:"inherit",backgroundColor:"currentColor",opacity:.38},"horizontal"===t.orientation&&{width:"100%",height:"inherit",top:"50%",transform:"translateY(-50%)"},"vertical"===t.orientation&&{height:"100%",width:"inherit",left:"50%",transform:"translateX(-50%)"},"inverted"===t.track&&{opacity:1})})),ud=gc("span",{name:"MuiSlider",slot:"Track",overridesResolver:(e,t)=>t.track})((e=>{let{theme:t,ownerState:n}=e;const r="light"===t.palette.mode?Es(t.palette[n.color].main,.62):Cs(t.palette[n.color].main,.5);return ca({display:"block",position:"absolute",borderRadius:"inherit",border:"1px solid currentColor",backgroundColor:"currentColor",transition:t.transitions.create(["left","width","bottom","height"],{duration:t.transitions.duration.shortest})},"small"===n.size&&{border:"none"},"horizontal"===n.orientation&&{height:"inherit",top:"50%",transform:"translateY(-50%)"},"vertical"===n.orientation&&{width:"inherit",left:"50%",transform:"translateX(-50%)"},!1===n.track&&{display:"none"},"inverted"===n.track&&{backgroundColor:t.vars?t.vars.palette.Slider["".concat(n.color,"Track")]:r,borderColor:t.vars?t.vars.palette.Slider["".concat(n.color,"Track")]:r})})),cd=gc("span",{name:"MuiSlider",slot:"Thumb",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.thumb,t["thumbColor".concat(nd(n.color))],"medium"!==n.size&&t["thumbSize".concat(nd(n.size))]]}})((e=>{let{theme:t,ownerState:n}=e;return ca({position:"absolute",width:20,height:20,boxSizing:"border-box",borderRadius:"50%",outline:0,backgroundColor:"currentColor",display:"flex",alignItems:"center",justifyContent:"center",transition:t.transitions.create(["box-shadow","left","bottom"],{duration:t.transitions.duration.shortest})},"small"===n.size&&{width:12,height:12},"horizontal"===n.orientation&&{top:"50%",transform:"translate(-50%, -50%)"},"vertical"===n.orientation&&{left:"50%",transform:"translate(-50%, 50%)"},{"&:before":ca({position:"absolute",content:'""',borderRadius:"inherit",width:"100%",height:"100%",boxShadow:(t.vars||t).shadows[2]},"small"===n.size&&{boxShadow:"none"}),"&::after":{position:"absolute",content:'""',borderRadius:"50%",width:42,height:42,top:"50%",left:"50%",transform:"translate(-50%, -50%)"},["&:hover, &.".concat(id.focusVisible)]:{boxShadow:"0px 0px 0px 8px ".concat(t.vars?"rgba(".concat(t.vars.palette[n.color].mainChannel," / 0.16)"):As(t.palette[n.color].main,.16)),"@media (hover: none)":{boxShadow:"none"}},["&.".concat(id.active)]:{boxShadow:"0px 0px 0px 14px ".concat(t.vars?"rgba(".concat(t.vars.palette[n.color].mainChannel," / 0.16)"):As(t.palette[n.color].main,.16))},["&.".concat(id.disabled)]:{"&:hover":{boxShadow:"none"}}})})),dd=gc((function(e){const{children:t,className:n,value:r}=e,i=(e=>{const{open:t}=e;return{offset:sl(t&&id.valueLabelOpen),circle:id.valueLabelCircle,label:id.valueLabelLabel}})(e);return t?c.cloneElement(t,{className:sl(t.props.className)},(0,lc.jsxs)(c.Fragment,{children:[t.props.children,(0,lc.jsx)("span",{className:sl(i.offset,n),"aria-hidden":!0,children:(0,lc.jsx)("span",{className:i.circle,children:(0,lc.jsx)("span",{className:i.label,children:r})})})]})):null}),{name:"MuiSlider",slot:"ValueLabel",overridesResolver:(e,t)=>t.valueLabel})((e=>{let{theme:t,ownerState:n}=e;return ca({["&.".concat(id.valueLabelOpen)]:{transform:"".concat("vertical"===n.orientation?"translateY(-50%)":"translateY(-100%)"," scale(1)")},zIndex:1,whiteSpace:"nowrap"},t.typography.body2,{fontWeight:500,transition:t.transitions.create(["transform"],{duration:t.transitions.duration.shortest}),transform:"".concat("vertical"===n.orientation?"translateY(-50%)":"translateY(-100%)"," scale(0)"),position:"absolute",backgroundColor:(t.vars||t).palette.grey[600],borderRadius:2,color:(t.vars||t).palette.common.white,display:"flex",alignItems:"center",justifyContent:"center",padding:"0.25rem 0.75rem"},"horizontal"===n.orientation&&{top:"-10px",transformOrigin:"bottom center","&:before":{position:"absolute",content:'""',width:8,height:8,transform:"translate(-50%, 50%) rotate(45deg)",backgroundColor:"inherit",bottom:0,left:"50%"}},"vertical"===n.orientation&&{right:"small"===n.size?"20px":"30px",top:"50%",transformOrigin:"right center","&:before":{position:"absolute",content:'""',width:8,height:8,transform:"translate(-50%, -50%) rotate(45deg)",backgroundColor:"inherit",right:-8,top:"50%"}},"small"===n.size&&{fontSize:t.typography.pxToRem(12),padding:"0.25rem 0.5rem"})})),fd=gc("span",{name:"MuiSlider",slot:"Mark",shouldForwardProp:e=>bc(e)&&"markActive"!==e,overridesResolver:(e,t)=>{const{markActive:n}=e;return[t.mark,n&&t.markActive]}})((e=>{let{theme:t,ownerState:n,markActive:r}=e;return ca({position:"absolute",width:2,height:2,borderRadius:1,backgroundColor:"currentColor"},"horizontal"===n.orientation&&{top:"50%",transform:"translate(-1px, -50%)"},"vertical"===n.orientation&&{left:"50%",transform:"translate(-50%, 1px)"},r&&{backgroundColor:(t.vars||t).palette.background.paper,opacity:.8})})),hd=gc("span",{name:"MuiSlider",slot:"MarkLabel",shouldForwardProp:e=>bc(e)&&"markLabelActive"!==e,overridesResolver:(e,t)=>t.markLabel})((e=>{let{theme:t,ownerState:n,markLabelActive:r}=e;return ca({},t.typography.body2,{color:(t.vars||t).palette.text.secondary,position:"absolute",whiteSpace:"nowrap"},"horizontal"===n.orientation&&{top:30,transform:"translateX(-50%)","@media (pointer: coarse)":{top:40}},"vertical"===n.orientation&&{left:36,transform:"translateY(50%)","@media (pointer: coarse)":{left:44}},r&&{color:(t.vars||t).palette.text.primary})})),pd=e=>{let{children:t}=e;return t},yd=c.forwardRef((function(e,t){var n,r,i,o,a,s,l,u,d,f,h,p,y,m,b,g,v,w,_,S,x,k,I,T;const O=vc({props:e,name:"MuiSlider"}),A="rtl"===function(){const e=ic(yc);return e[mc]||e}().direction,{"aria-label":C,"aria-valuetext":E,"aria-labelledby":N,component:M="span",components:B={},componentsProps:L={},color:D="primary",classes:F,className:P,disableSwap:R=!1,disabled:z=!1,getAriaLabel:U,getAriaValueText:j,marks:V=!1,max:W=100,min:$=0,orientation:H="horizontal",size:Y="medium",step:K=1,scale:Q=ad,slotProps:q,slots:G,track:X="normal",valueLabelDisplay:J="off",valueLabelFormat:Z=ad}=O,ee=da(O,od),te=ca({},O,{isRtl:A,max:W,min:$,classes:F,disabled:z,disableSwap:R,orientation:H,marks:V,color:D,size:Y,step:K,scale:Q,track:X,valueLabelDisplay:J,valueLabelFormat:Z}),{axisProps:ne,getRootProps:re,getHiddenInputProps:ie,getThumbProps:oe,open:ae,active:se,axis:le,focusedThumbIndex:ue,range:ce,dragging:de,marks:fe,values:he,trackOffset:pe,trackLeap:ye,getThumbStyle:me}=ed(ca({},te,{rootRef:t}));te.marked=fe.length>0&&fe.some((e=>e.label)),te.dragging=de,te.focusedThumbIndex=ue;const be=(e=>{const{disabled:t,dragging:n,marked:r,orientation:i,track:o,classes:a,color:s,size:l}=e;return ll({root:["root",t&&"disabled",n&&"dragging",r&&"marked","vertical"===i&&"vertical","inverted"===o&&"trackInverted",!1===o&&"trackFalse",s&&"color".concat(nd(s)),l&&"size".concat(nd(l))],rail:["rail"],track:["track"],mark:["mark"],markActive:["markActive"],markLabel:["markLabel"],markLabelActive:["markLabelActive"],valueLabel:["valueLabel"],thumb:["thumb",t&&"disabled",l&&"thumbSize".concat(nd(l)),s&&"thumbColor".concat(nd(s))],active:["active"],disabled:["disabled"],focusVisible:["focusVisible"]},rd,a)})(te),ge=null!=(n=null!=(r=null==G?void 0:G.root)?r:B.Root)?n:sd,ve=null!=(i=null!=(o=null==G?void 0:G.rail)?o:B.Rail)?i:ld,we=null!=(a=null!=(s=null==G?void 0:G.track)?s:B.Track)?a:ud,_e=null!=(l=null!=(u=null==G?void 0:G.thumb)?u:B.Thumb)?l:cd,Se=null!=(d=null!=(f=null==G?void 0:G.valueLabel)?f:B.ValueLabel)?d:dd,xe=null!=(h=null!=(p=null==G?void 0:G.mark)?p:B.Mark)?h:fd,ke=null!=(y=null!=(m=null==G?void 0:G.markLabel)?m:B.MarkLabel)?y:hd,Ie=null!=(b=null!=(g=null==G?void 0:G.input)?g:B.Input)?b:"input",Te=null!=(v=null==q?void 0:q.root)?v:L.root,Oe=null!=(w=null==q?void 0:q.rail)?w:L.rail,Ae=null!=(_=null==q?void 0:q.track)?_:L.track,Ce=null!=(S=null==q?void 0:q.thumb)?S:L.thumb,Ee=null!=(x=null==q?void 0:q.valueLabel)?x:L.valueLabel,Ne=null!=(k=null==q?void 0:q.mark)?k:L.mark,Me=null!=(I=null==q?void 0:q.markLabel)?I:L.markLabel,Be=null!=(T=null==q?void 0:q.input)?T:L.input,Le=Oc({elementType:ge,getSlotProps:re,externalSlotProps:Te,externalForwardedProps:ee,additionalProps:ca({},td(ge)&&{as:M}),ownerState:ca({},te,null==Te?void 0:Te.ownerState),className:[be.root,P]}),De=Oc({elementType:ve,externalSlotProps:Oe,ownerState:te,className:be.rail}),Fe=Oc({elementType:we,externalSlotProps:Ae,additionalProps:{style:ca({},ne[le].offset(pe),ne[le].leap(ye))},ownerState:ca({},te,null==Ae?void 0:Ae.ownerState),className:be.track}),Pe=Oc({elementType:_e,getSlotProps:oe,externalSlotProps:Ce,ownerState:ca({},te,null==Ce?void 0:Ce.ownerState),className:be.thumb}),Re=Oc({elementType:Se,externalSlotProps:Ee,ownerState:ca({},te,null==Ee?void 0:Ee.ownerState),className:be.valueLabel}),ze=Oc({elementType:xe,externalSlotProps:Ne,ownerState:te,className:be.mark}),Ue=Oc({elementType:ke,externalSlotProps:Me,ownerState:te,className:be.markLabel}),je=Oc({elementType:Ie,getSlotProps:ie,externalSlotProps:Be,ownerState:te});return(0,lc.jsxs)(ge,ca({},Le,{children:[(0,lc.jsx)(ve,ca({},De)),(0,lc.jsx)(we,ca({},Fe)),fe.filter((e=>e.value>=$&&e.value<=W)).map(((e,t)=>{const n=Hc(e.value,$,W),r=ne[le].offset(n);let i;return i=!1===X?-1!==he.indexOf(e.value):"normal"===X&&(ce?e.value>=he[0]&&e.value<=he[he.length-1]:e.value<=he[0])||"inverted"===X&&(ce?e.value<=he[0]||e.value>=he[he.length-1]:e.value>=he[0]),(0,lc.jsxs)(c.Fragment,{children:[(0,lc.jsx)(xe,ca({"data-index":t},ze,!xc(xe)&&{markActive:i},{style:ca({},r,ze.style),className:sl(ze.className,i&&be.markActive)})),null!=e.label?(0,lc.jsx)(ke,ca({"aria-hidden":!0,"data-index":t},Ue,!xc(ke)&&{markLabelActive:i},{style:ca({},r,Ue.style),className:sl(be.markLabel,Ue.className,i&&be.markLabelActive),children:e.label})):null]},t)})),he.map(((e,t)=>{const n=Hc(e,$,W),r=ne[le].offset(n),i="off"===J?pd:Se;return(0,lc.jsx)(i,ca({},!xc(i)&&{valueLabelFormat:Z,valueLabelDisplay:J,value:"function"===typeof Z?Z(Q(e),t):Z,index:t,open:ae===t||se===t||"on"===J,disabled:z},Re,{children:(0,lc.jsx)(_e,ca({"data-index":t},Pe,{className:sl(be.thumb,Pe.className,se===t&&be.active,ue===t&&be.focusVisible),style:ca({},r,me(t),Pe.style),children:(0,lc.jsx)(Ie,ca({"data-index":t,"aria-label":U?U(t):C,"aria-valuenow":Q(e),"aria-labelledby":N,"aria-valuetext":j?j(Q(e),t):E,value:he[t]},je))}))}),t)}))]}))})),md=yd;const bd=c.createContext(null);function gd(){return c.useContext(bd)}const vd="function"===typeof Symbol&&Symbol.for?Symbol.for("mui.nested"):"__THEME_NESTED__";const wd=function(e){const{children:t,theme:n}=e,r=gd(),i=c.useMemo((()=>{const e=null===r?n:function(e,t){if("function"===typeof t)return t(e);return ca({},e,t)}(r,n);return null!=e&&(e[vd]=null!==r),e}),[n,r]);return(0,lc.jsx)(bd.Provider,{value:i,children:t})},_d={};function Sd(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return c.useMemo((()=>{const i=e&&t[e]||t;if("function"===typeof n){const o=n(i),a=e?ca({},t,{[e]:o}):o;return r?()=>a:a}return ca({},t,e?{[e]:n}:n)}),[e,t,n,r])}const xd=function(e){const{children:t,theme:n,themeId:r}=e,i=nc(_d),o=gd()||_d,a=Sd(r,i,n),s=Sd(r,o,n,!0);return(0,lc.jsx)(wd,{theme:s,children:(0,lc.jsx)(Mu.Provider,{value:a,children:t})})},kd=["theme"];function Id(e){let{theme:t}=e,n=da(e,kd);const r=t[mc];return(0,lc.jsx)(xd,ca({},n,{themeId:r?mc:void 0,theme:r||t}))}const Td=function(e){var t=function(t){function n(e){var n=t.call(this,e)||this;return n.componentDidMount=function(){sa.events.addEventListener(sa.RENDER_EVENT,n.onRenderEvent),sa.setComponentReady()},n.componentDidUpdate=function(){null!=n.state.componentError&&sa.setFrameHeight()},n.componentWillUnmount=function(){sa.events.removeEventListener(sa.RENDER_EVENT,n.onRenderEvent)},n.onRenderEvent=function(e){n.setState({renderData:e.detail})},n.state={renderData:void 0,componentError:void 0},n}return ua(n,t),n.prototype.render=function(){return null!=this.state.componentError?y.createElement("div",null,y.createElement("h1",null,"Component Error"),y.createElement("span",null,this.state.componentError.message)):null==this.state.renderData?null:y.createElement(e,{width:window.innerWidth,disabled:this.state.renderData.disabled,args:this.state.renderData.args,theme:this.state.renderData.theme})},n.getDerivedStateFromError=function(e){return{componentError:e}},n}(y.PureComponent);return p()(t,e)}((e=>{const{label:t,thumb_height:n,thumb_style:r,thumb_color:i,height:o,min_value:a,max_value:s,default_value:l,step:u,track_color:d,slider_color:f,value_always_visible:h}=e.args,[p,y]=c.useState(l);(0,c.useEffect)((()=>sa.setFrameHeight()));const m=ol({components:{MuiSlider:{styleOverrides:{root:{height:o,color:"inherit",overflow:"visible",width:"fit-content !important",fontSize:8,marginBottom:0},thumb:{color:i,borderRadius:r,height:n},valueLabel:{backgroundColor:i,borderRadius:"10px",fontSize:10},track:{color:f,width:"10px !important",borderRadius:2,marginBottom:0,borderWidth:1},rail:{color:d,width:"10px !important",borderRadius:2,marginBottom:0}}},MuiStack:{styleOverrides:{root:{fontSize:12,fontFamily:["Source Sans Pro","sans-serif"],alignItems:"center",justifyContent:"center",textWrap:"wrap",margin:0}}}}});return(0,lc.jsx)(Id,{theme:m,children:(0,lc.jsxs)(_c,{component:"div",direction:"column",alignItems:"center",justifyContent:"center",sx:{maxWidth:300},children:[(0,lc.jsx)("label",{children:s}),(0,lc.jsx)(md,{min:a,step:u,max:s,defaultValue:l,onChangeCommitted:(e,t)=>{sa.setComponentValue(t),y(t)},valueLabelDisplay:h,orientation:"vertical"}),(0,lc.jsx)("label",{children:a}),(0,lc.jsx)("b",{children:t})]})})}));f.render((0,lc.jsx)(c.StrictMode,{children:(0,lc.jsx)(Td,{})}),document.getElementById("root"))})()})();
//# sourceMappingURL=main.749ca5aa.js.map