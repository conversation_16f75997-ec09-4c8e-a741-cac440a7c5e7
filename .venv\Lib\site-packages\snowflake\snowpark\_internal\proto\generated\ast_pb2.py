# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: ast.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\tast.proto\x12\x03\x61st\x1a\x1egoogle/protobuf/wrappers.proto\"\x8b\x01\n\x12InternedValueTable\x12@\n\rstring_values\x18\x01 \x03(\x0b\x32).ast.InternedValueTable.StringValuesEntry\x1a\x33\n\x11StringValuesEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\x1b\n\x0bList_String\x12\x0c\n\x04list\x18\x01 \x03(\t\"?\n\x0fTuple_Expr_Expr\x12\x15\n\x02_1\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x15\n\x02_2\x18\x02 \x01(\x0b\x32\t.ast.Expr\"5\n\x10Tuple_Expr_Float\x12\x15\n\x02_1\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\n\n\x02_2\x18\x02 \x01(\x01\"6\n\x11Tuple_String_Expr\x12\n\n\x02_1\x18\x01 \x01(\t\x12\x15\n\x02_2\x18\x02 \x01(\x0b\x32\t.ast.Expr\"2\n\x18Tuple_String_List_String\x12\n\n\x02_1\x18\x01 \x01(\t\x12\n\n\x02_2\x18\x02 \x03(\t\"-\n\x13Tuple_String_String\x12\n\n\x02_1\x18\x01 \x01(\t\x12\n\n\x02_2\x18\x02 \x01(\t\"G\n\x08\x43\x61llable\x12\n\n\x02id\x18\x01 \x01(\x03\x12\x0c\n\x04name\x18\x02 \x01(\t\x12!\n\x0bobject_name\x18\x03 \x01(\x0b\x32\x0c.ast.NameRef\"y\n\rColumnAliasFn\x12\x1f\n\x15\x63olumn_alias_fn_alias\x18\x01 \x01(\x08H\x00\x12\x1c\n\x12\x63olumn_alias_fn_as\x18\x02 \x01(\x08H\x00\x12\x1e\n\x14\x63olumn_alias_fn_name\x18\x03 \x01(\x08H\x00\x42\t\n\x07variant\"w\n\tColumnRef\x12\x32\n\x11\x63olumn_identifier\x18\x01 \x01(\x0b\x32\x15.ast.ColumnIdentifierH\x00\x12&\n\x0b\x63olumn_name\x18\x02 \x01(\x0b\x32\x0f.ast.ColumnNameH\x00\x42\x0e\n\x0csealed_value\" \n\x10\x43olumnIdentifier\x12\x0c\n\x04name\x18\x01 \x01(\t\"\x1a\n\nColumnName\x12\x0c\n\x04name\x18\x01 \x01(\t\"\x9b\x06\n\x08\x44\x61taType\x12$\n\narray_type\x18\x01 \x01(\x0b\x32\x0e.ast.ArrayTypeH\x00\x12\x15\n\x0b\x62inary_type\x18\x02 \x01(\x08H\x00\x12\x16\n\x0c\x62oolean_type\x18\x03 \x01(\x08H\x00\x12\x13\n\tbyte_type\x18\x04 \x01(\x08H\x00\x12\x13\n\tdate_type\x18\x05 \x01(\x08H\x00\x12(\n\x0c\x64\x65\x63imal_type\x18\x06 \x01(\x0b\x32\x10.ast.DecimalTypeH\x00\x12\x15\n\x0b\x64ouble_type\x18\x07 \x01(\x08H\x00\x12\x13\n\tfile_type\x18\x08 \x01(\x08H\x00\x12\x14\n\nfloat_type\x18\t \x01(\x08H\x00\x12\x18\n\x0egeography_type\x18\n \x01(\x08H\x00\x12\x17\n\rgeometry_type\x18\x0b \x01(\x08H\x00\x12\x16\n\x0cinteger_type\x18\x0c \x01(\x08H\x00\x12\x13\n\tlong_type\x18\r \x01(\x08H\x00\x12 \n\x08map_type\x18\x0e \x01(\x0b\x32\x0c.ast.MapTypeH\x00\x12\x13\n\tnull_type\x18\x0f \x01(\x08H\x00\x12:\n\x16pandas_data_frame_type\x18\x10 \x01(\x0b\x32\x18.ast.PandasDataFrameTypeH\x00\x12\x33\n\x12pandas_series_type\x18\x11 \x01(\x0b\x32\x15.ast.PandasSeriesTypeH\x00\x12\x14\n\nshort_type\x18\x12 \x01(\x08H\x00\x12&\n\x0bstring_type\x18\x13 \x01(\x0b\x32\x0f.ast.StringTypeH\x00\x12(\n\x0cstruct_field\x18\x14 \x01(\x0b\x32\x10.ast.StructFieldH\x00\x12&\n\x0bstruct_type\x18\x15 \x01(\x0b\x32\x0f.ast.StructTypeH\x00\x12\x13\n\ttime_type\x18\x16 \x01(\x08H\x00\x12,\n\x0etimestamp_type\x18\x17 \x01(\x0b\x32\x12.ast.TimestampTypeH\x00\x12\x16\n\x0cvariant_type\x18\x18 \x01(\x08H\x00\x12&\n\x0bvector_type\x18\x19 \x01(\x0b\x32\x0f.ast.VectorTypeH\x00\x42\t\n\x07variant\":\n\tArrayType\x12\x12\n\nstructured\x18\x01 \x01(\x08\x12\x19\n\x02ty\x18\x02 \x01(\x0b\x32\r.ast.DataType\"/\n\x0b\x44\x65\x63imalType\x12\x11\n\tprecision\x18\x01 \x01(\x03\x12\r\n\x05scale\x18\x02 \x01(\x03\"]\n\x07MapType\x12\x1d\n\x06key_ty\x18\x01 \x01(\x0b\x32\r.ast.DataType\x12\x12\n\nstructured\x18\x02 \x01(\x08\x12\x1f\n\x08value_ty\x18\x03 \x01(\x0b\x32\r.ast.DataType\"9\n\nStringType\x12+\n\x06length\x18\x01 \x01(\x0b\x32\x1b.google.protobuf.Int64Value\"l\n\x0bStructField\x12)\n\x11\x63olumn_identifier\x18\x01 \x01(\x0b\x32\x0e.ast.ColumnRef\x12 \n\tdata_type\x18\x02 \x01(\x0b\x32\r.ast.DataType\x12\x10\n\x08nullable\x18\x03 \x01(\x08\"B\n\nStructType\x12 \n\x06\x66ields\x18\x01 \x03(\x0b\x32\x10.ast.StructField\x12\x12\n\nstructured\x18\x02 \x01(\x08\":\n\rTimestampType\x12)\n\ttime_zone\x18\x01 \x01(\x0b\x32\x16.ast.TimestampTimeZone\":\n\nVectorType\x12\x11\n\tdimension\x18\x01 \x01(\x03\x12\x19\n\x02ty\x18\x02 \x01(\x0b\x32\r.ast.DataType\"0\n\x10PandasSeriesType\x12\x1c\n\x05\x65l_ty\x18\x01 \x01(\x0b\x32\r.ast.DataType\"J\n\x13PandasDataFrameType\x12\x11\n\tcol_names\x18\x01 \x03(\t\x12 \n\tcol_types\x18\x02 \x03(\x0b\x32\r.ast.DataType\"\xd0\x01\n\rDataframeData\x12\x37\n\x14\x64\x61taframe_data__list\x18\x01 \x01(\x0b\x32\x17.ast.DataframeData_ListH\x00\x12;\n\x16\x64\x61taframe_data__pandas\x18\x02 \x01(\x0b\x32\x19.ast.DataframeData_PandasH\x00\x12\x39\n\x15\x64\x61taframe_data__tuple\x18\x03 \x01(\x0b\x32\x18.ast.DataframeData_TupleH\x00\x42\x0e\n\x0csealed_value\"+\n\x12\x44\x61taframeData_List\x12\x15\n\x02vs\x18\x01 \x03(\x0b\x32\t.ast.Expr\",\n\x13\x44\x61taframeData_Tuple\x12\x15\n\x02vs\x18\x01 \x03(\x0b\x32\t.ast.Expr\"=\n\x14\x44\x61taframeData_Pandas\x12%\n\x01v\x18\x01 \x01(\x0b\x32\x1a.ast.StagedPandasDataframe\"\x9f\x01\n\x0f\x44\x61taframeSchema\x12;\n\x16\x64\x61taframe_schema__list\x18\x01 \x01(\x0b\x32\x19.ast.DataframeSchema_ListH\x00\x12?\n\x18\x64\x61taframe_schema__struct\x18\x02 \x01(\x0b\x32\x1b.ast.DataframeSchema_StructH\x00\x42\x0e\n\x0csealed_value\"\"\n\x14\x44\x61taframeSchema_List\x12\n\n\x02vs\x18\x01 \x03(\t\"4\n\x16\x44\x61taframeSchema_Struct\x12\x1a\n\x01v\x18\x01 \x01(\x0b\x32\x0f.ast.StructType\"r\n\x0b\x46lattenMode\x12\x1c\n\x12\x66latten_mode_array\x18\x01 \x01(\x08H\x00\x12\x1b\n\x11\x66latten_mode_both\x18\x02 \x01(\x08H\x00\x12\x1d\n\x13\x66latten_mode_object\x18\x03 \x01(\x08H\x00\x42\t\n\x07variant\"\x8c\x02\n\x08JoinType\x12\x19\n\x0fjoin_type__asof\x18\x01 \x01(\x08H\x00\x12\x1a\n\x10join_type__cross\x18\x02 \x01(\x08H\x00\x12\x1f\n\x15join_type__full_outer\x18\x03 \x01(\x08H\x00\x12\x1a\n\x10join_type__inner\x18\x04 \x01(\x08H\x00\x12\x1e\n\x14join_type__left_anti\x18\x05 \x01(\x08H\x00\x12\x1f\n\x15join_type__left_outer\x18\x06 \x01(\x08H\x00\x12\x1e\n\x14join_type__left_semi\x18\x07 \x01(\x08H\x00\x12 \n\x16join_type__right_outer\x18\x08 \x01(\x08H\x00\x42\t\n\x07variant\"\xa4\x01\n\x08Language\x12*\n\rjava_language\x18\x01 \x01(\x0b\x32\x11.ast.JavaLanguageH\x00\x12.\n\x0fpython_language\x18\x02 \x01(\x0b\x32\x13.ast.PythonLanguageH\x00\x12,\n\x0escala_language\x18\x03 \x01(\x0b\x32\x12.ast.ScalaLanguageH\x00\x42\x0e\n\x0csealed_value\"/\n\x0ePythonLanguage\x12\x1d\n\x07version\x18\x01 \x01(\x0b\x32\x0c.ast.Version\".\n\rScalaLanguage\x12\x1d\n\x07version\x18\x01 \x01(\x0b\x32\x0c.ast.Version\"-\n\x0cJavaLanguage\x12\x1d\n\x07version\x18\x01 \x01(\x0b\x32\x0c.ast.Version\"j\n\x04Name\x12\"\n\tname_flat\x18\x01 \x01(\x0b\x32\r.ast.NameFlatH\x00\x12.\n\x0fname_structured\x18\x02 \x01(\x0b\x32\x13.ast.NameStructuredH\x00\x42\x0e\n\x0csealed_value\"\x18\n\x08NameFlat\x12\x0c\n\x04name\x18\x01 \x01(\t\"\x1e\n\x0eNameStructured\x12\x0c\n\x04name\x18\x01 \x03(\t\"w\n\tNullOrder\x12\x1c\n\x12null_order_default\x18\x01 \x01(\x08H\x00\x12 \n\x16null_order_nulls_first\x18\x02 \x01(\x08H\x00\x12\x1f\n\x15null_order_nulls_last\x18\x03 \x01(\x08H\x00\x42\t\n\x07variant\"T\n\x0ePythonTimeZone\x12*\n\x04name\x18\x01 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12\x16\n\x0eoffset_seconds\x18\x02 \x01(\x03\"\xaf\x01\n\x08SaveMode\x12\x1a\n\x10save_mode_append\x18\x01 \x01(\x08H\x00\x12#\n\x19save_mode_error_if_exists\x18\x02 \x01(\x08H\x00\x12\x1a\n\x10save_mode_ignore\x18\x03 \x01(\x08H\x00\x12\x1d\n\x13save_mode_overwrite\x18\x04 \x01(\x08H\x00\x12\x1c\n\x12save_mode_truncate\x18\x05 \x01(\x08H\x00\x42\t\n\x07variant\"k\n\x0bSrcPosition\x12\x12\n\nend_column\x18\x01 \x01(\x03\x12\x10\n\x08\x65nd_line\x18\x02 \x01(\x03\x12\x0c\n\x04\x66ile\x18\x03 \x01(\x05\x12\x14\n\x0cstart_column\x18\x04 \x01(\x03\x12\x12\n\nstart_line\x18\x05 \x01(\x03\"9\n\x15StagedPandasDataframe\x12 \n\ntemp_table\x18\x01 \x01(\x0b\x32\x0c.ast.NameRef\"H\n\x0cTableVariant\x12\x17\n\rsession_table\x18\x01 \x01(\x08H\x00\x12\x14\n\ntable_init\x18\x02 \x01(\x08H\x00\x42\t\n\x07variant\"\xad\x01\n\x11TimestampTimeZone\x12%\n\x1btimestamp_time_zone_default\x18\x01 \x01(\x08H\x00\x12!\n\x17timestamp_time_zone_ltz\x18\x02 \x01(\x08H\x00\x12!\n\x17timestamp_time_zone_ntz\x18\x03 \x01(\x08H\x00\x12 \n\x16timestamp_time_zone_tz\x18\x04 \x01(\x08H\x00\x42\t\n\x07variant\"\x84\x01\n\nUdtfSchema\x12\x33\n\x12udtf_schema__names\x18\x01 \x01(\x0b\x32\x15.ast.UdtfSchema_NamesH\x00\x12\x31\n\x11udtf_schema__type\x18\x02 \x01(\x0b\x32\x14.ast.UdtfSchema_TypeH\x00\x42\x0e\n\x0csealed_value\"5\n\x0fUdtfSchema_Type\x12\"\n\x0breturn_type\x18\x01 \x01(\x0b\x32\r.ast.DataType\"\"\n\x10UdtfSchema_Names\x12\x0e\n\x06schema\x18\x01 \x03(\t\"E\n\x07Version\x12\r\n\x05label\x18\x01 \x01(\t\x12\r\n\x05major\x18\x02 \x01(\x03\x12\r\n\x05minor\x18\x03 \x01(\x03\x12\r\n\x05patch\x18\x04 \x01(\x03\"\x9a\x02\n\x16WindowRelativePosition\x12/\n%window_relative_position__current_row\x18\x01 \x01(\x08H\x00\x12R\n\"window_relative_position__position\x18\x02 \x01(\x0b\x32$.ast.WindowRelativePosition_PositionH\x00\x12\x37\n-window_relative_position__unbounded_following\x18\x03 \x01(\x08H\x00\x12\x37\n-window_relative_position__unbounded_preceding\x18\x04 \x01(\x08H\x00\x42\t\n\x07variant\"7\n\x1fWindowRelativePosition_Position\x12\x14\n\x01n\x18\x01 \x01(\x0b\x32\t.ast.Expr\"\xeb\x01\n\x11\x41\x62stractExtension\x12\x32\n\x14trait_extension_expr\x18\x01 \x01(\x0b\x32\x12.ast.ExtensionExprH\x00\x12.\n\x0f\x65xtension_error\x18\x02 \x01(\x0b\x32\x13.ast.ExtensionErrorH\x00\x12\x39\n\x15\x65xtension_eval_result\x18\x03 \x01(\x0b\x32\x18.ast.ExtensionEvalResultH\x00\x12,\n\x0e\x65xtension_stmt\x18\x04 \x01(\x0b\x32\x12.ast.ExtensionStmtH\x00\x42\t\n\x07variant\"T\n\x03\x41\x64\x64\x12\x16\n\x03lhs\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x16\n\x03rhs\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"T\n\x03\x41nd\x12\x16\n\x03lhs\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x16\n\x03rhs\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"\x8a\x01\n\tApplyExpr\x12\x15\n\x02\x66n\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12*\n\nnamed_args\x18\x02 \x03(\x0b\x32\x16.ast.Tuple_String_Expr\x12\x1b\n\x08pos_args\x18\x03 \x03(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x04 \x01(\x0b\x32\x10.ast.SrcPosition\"\x84\x01\n\rBigDecimalVal\x12\r\n\x05scale\x18\x01 \x01(\x03\x12-\n\x07special\x18\x02 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x16\n\x0eunscaled_value\x18\x04 \x01(\x0c\"J\n\tBigIntVal\x12\x13\n\x0bis_negative\x18\x01 \x01(\x08\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\x12\t\n\x01v\x18\x03 \x01(\x0c\"\xc6\x03\n\x05\x42inOp\x12\x17\n\x03\x61\x64\x64\x18\x01 \x01(\x0b\x32\x08.ast.AddH\x00\x12\x17\n\x03\x61nd\x18\x02 \x01(\x0b\x32\x08.ast.AndH\x00\x12\x1e\n\x07\x62it_and\x18\x03 \x01(\x0b\x32\x0b.ast.BitAndH\x00\x12\x1c\n\x06\x62it_or\x18\x04 \x01(\x0b\x32\n.ast.BitOrH\x00\x12\x1e\n\x07\x62it_xor\x18\x05 \x01(\x0b\x32\x0b.ast.BitXorH\x00\x12\x17\n\x03\x64iv\x18\x06 \x01(\x0b\x32\x08.ast.DivH\x00\x12\x15\n\x02\x65q\x18\x07 \x01(\x0b\x32\x07.ast.EqH\x00\x12\x17\n\x03geq\x18\x08 \x01(\x0b\x32\x08.ast.GeqH\x00\x12\x15\n\x02gt\x18\t \x01(\x0b\x32\x07.ast.GtH\x00\x12\x17\n\x03leq\x18\n \x01(\x0b\x32\x08.ast.LeqH\x00\x12\x15\n\x02lt\x18\x0b \x01(\x0b\x32\x07.ast.LtH\x00\x12\x17\n\x03mod\x18\x0c \x01(\x0b\x32\x08.ast.ModH\x00\x12\x17\n\x03mul\x18\r \x01(\x0b\x32\x08.ast.MulH\x00\x12\x17\n\x03neq\x18\x0e \x01(\x0b\x32\x08.ast.NeqH\x00\x12\x15\n\x02or\x18\x0f \x01(\x0b\x32\x07.ast.OrH\x00\x12\x17\n\x03pow\x18\x10 \x01(\x0b\x32\x08.ast.PowH\x00\x12\x17\n\x03sub\x18\x11 \x01(\x0b\x32\x08.ast.SubH\x00\x42\t\n\x07variant\"5\n\tBinaryVal\x12\x1d\n\x03src\x18\x01 \x01(\x0b\x32\x10.ast.SrcPosition\x12\t\n\x01v\x18\x02 \x01(\x0c\"t\n\x04\x42ind\x12\x17\n\x04\x65xpr\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x18\n\x10\x66irst_request_id\x18\x02 \x01(\x0c\x12,\n\x06symbol\x18\x03 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12\x0b\n\x03uid\x18\x04 \x01(\x03\"W\n\x06\x42itAnd\x12\x16\n\x03lhs\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x16\n\x03rhs\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"V\n\x05\x42itOr\x12\x16\n\x03lhs\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x16\n\x03rhs\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"W\n\x06\x42itXor\x12\x16\n\x03lhs\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x16\n\x03rhs\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"3\n\x07\x42oolVal\x12\x1d\n\x03src\x18\x01 \x01(\x0b\x32\x10.ast.SrcPosition\x12\t\n\x01v\x18\x02 \x01(\x08\"F\n\tBuiltinFn\x12\x1a\n\x04name\x18\x01 \x01(\x0b\x32\x0c.ast.NameRef\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\"R\n\x15\x43\x61llTableFunctionExpr\x12\x1a\n\x04name\x18\x01 \x01(\x0b\x32\x0c.ast.NameRef\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\"r\n\x0b\x43olumnAlias\x12\x16\n\x03\x63ol\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1e\n\x02\x66n\x18\x02 \x01(\x0b\x32\x12.ast.ColumnAliasFn\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x1d\n\x03src\x18\x04 \x01(\x0b\x32\x10.ast.SrcPosition\"U\n\x0f\x43olumnApply_Int\x12\x16\n\x03\x63ol\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x0b\n\x03idx\x18\x02 \x01(\x03\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"Z\n\x12\x43olumnApply_String\x12\x16\n\x03\x63ol\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\r\n\x05\x66ield\x18\x02 \x01(\t\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"f\n\tColumnAsc\x12\x16\n\x03\x63ol\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\"\n\nnull_order\x18\x02 \x01(\x0b\x32\x0e.ast.NullOrder\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"\x86\x01\n\rColumnBetween\x12\x16\n\x03\x63ol\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1e\n\x0blower_bound\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x1e\n\x0bupper_bound\x18\x04 \x01(\x0b\x32\t.ast.Expr\"Y\n\x0e\x43olumnCaseExpr\x12(\n\x05\x63\x61ses\x18\x01 \x03(\x0b\x32\x19.ast.ColumnCaseExprClause\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\"m\n\x14\x43olumnCaseExprClause\x12\x1c\n\tcondition\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x18\n\x05value\x18\x03 \x01(\x0b\x32\t.ast.Expr\"^\n\nColumnCast\x12\x16\n\x03\x63ol\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x19\n\x02to\x18\x03 \x01(\x0b\x32\r.ast.DataType\"g\n\nColumnDesc\x12\x16\n\x03\x63ol\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\"\n\nnull_order\x18\x02 \x01(\x0b\x32\x0e.ast.NullOrder\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"G\n\x0e\x43olumnEqualNan\x12\x16\n\x03\x63ol\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\"`\n\x0f\x43olumnEqualNull\x12\x16\n\x03lhs\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x16\n\x03rhs\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"\xa5\x08\n\x08\x43olumnFn\x12(\n\x0c\x63olumn_alias\x18\x01 \x01(\x0b\x32\x10.ast.ColumnAliasH\x00\x12\x31\n\x11\x63olumn_apply__int\x18\x02 \x01(\x0b\x32\x14.ast.ColumnApply_IntH\x00\x12\x37\n\x14\x63olumn_apply__string\x18\x03 \x01(\x0b\x32\x17.ast.ColumnApply_StringH\x00\x12$\n\ncolumn_asc\x18\x04 \x01(\x0b\x32\x0e.ast.ColumnAscH\x00\x12,\n\x0e\x63olumn_between\x18\x05 \x01(\x0b\x32\x12.ast.ColumnBetweenH\x00\x12&\n\x0b\x63olumn_cast\x18\x06 \x01(\x0b\x32\x0f.ast.ColumnCastH\x00\x12&\n\x0b\x63olumn_desc\x18\x07 \x01(\x0b\x32\x0f.ast.ColumnDescH\x00\x12/\n\x10\x63olumn_equal_nan\x18\x08 \x01(\x0b\x32\x13.ast.ColumnEqualNanH\x00\x12\"\n\tcolumn_in\x18\t \x01(\x0b\x32\r.ast.ColumnInH\x00\x12\x32\n\x12\x63olumn_is_not_null\x18\n \x01(\x0b\x32\x14.ast.ColumnIsNotNullH\x00\x12+\n\x0e\x63olumn_is_null\x18\x0b \x01(\x0b\x32\x11.ast.ColumnIsNullH\x00\x12&\n\x0b\x63olumn_over\x18\x0c \x01(\x0b\x32\x0f.ast.ColumnOverH\x00\x12*\n\rcolumn_regexp\x18\r \x01(\x0b\x32\x11.ast.ColumnRegexpH\x00\x12\x39\n\x15\x63olumn_string_collate\x18\x0e \x01(\x0b\x32\x18.ast.ColumnStringCollateH\x00\x12;\n\x16\x63olumn_string_contains\x18\x0f \x01(\x0b\x32\x19.ast.ColumnStringContainsH\x00\x12<\n\x17\x63olumn_string_ends_with\x18\x10 \x01(\x0b\x32\x19.ast.ColumnStringEndsWithH\x00\x12\x33\n\x12\x63olumn_string_like\x18\x11 \x01(\x0b\x32\x15.ast.ColumnStringLikeH\x00\x12@\n\x19\x63olumn_string_starts_with\x18\x12 \x01(\x0b\x32\x1b.ast.ColumnStringStartsWithH\x00\x12\x37\n\x14\x63olumn_string_substr\x18\x13 \x01(\x0b\x32\x17.ast.ColumnStringSubstrH\x00\x12-\n\x0f\x63olumn_try_cast\x18\x14 \x01(\x0b\x32\x12.ast.ColumnTryCastH\x00\x12\x35\n\x13\x63olumn_within_group\x18\x15 \x01(\x0b\x32\x16.ast.ColumnWithinGroupH\x00\x42\t\n\x07variant\"\\\n\x08\x43olumnIn\x12\x16\n\x03\x63ol\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x19\n\x06values\x18\x03 \x03(\x0b\x32\t.ast.Expr\"H\n\x0f\x43olumnIsNotNull\x12\x16\n\x03\x63ol\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\"E\n\x0c\x43olumnIsNull\x12\x16\n\x03\x63ol\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\"m\n\nColumnOver\x12\x16\n\x03\x63ol\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\x12(\n\x0bwindow_spec\x18\x03 \x01(\x0b\x32\x13.ast.WindowSpecExpr\"\x80\x01\n\x0c\x43olumnRegexp\x12\x16\n\x03\x63ol\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\nparameters\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1a\n\x07pattern\x18\x03 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x04 \x01(\x0b\x32\x10.ast.SrcPosition\"o\n\x13\x43olumnStringCollate\x12\x16\n\x03\x63ol\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12!\n\x0e\x63ollation_spec\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"i\n\x14\x43olumnStringContains\x12\x16\n\x03\x63ol\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1a\n\x07pattern\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"h\n\x14\x43olumnStringEndsWith\x12\x16\n\x03\x63ol\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x19\n\x06suffix\x18\x03 \x01(\x0b\x32\t.ast.Expr\"e\n\x10\x43olumnStringLike\x12\x16\n\x03\x63ol\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1a\n\x07pattern\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"j\n\x16\x43olumnStringStartsWith\x12\x16\n\x03\x63ol\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x19\n\x06prefix\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"{\n\x12\x43olumnStringSubstr\x12\x16\n\x03\x63ol\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x16\n\x03len\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x16\n\x03pos\x18\x03 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x04 \x01(\x0b\x32\x10.ast.SrcPosition\"a\n\rColumnTryCast\x12\x16\n\x03\x63ol\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x19\n\x02to\x18\x03 \x01(\x0b\x32\r.ast.DataType\"j\n\x11\x43olumnWithinGroup\x12\x16\n\x03\x63ol\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1e\n\x04\x63ols\x18\x02 \x01(\x0b\x32\x10.ast.ExprArgList\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"\xb3\x04\n\x05\x43onst\x12-\n\x0f\x62ig_decimal_val\x18\x01 \x01(\x0b\x32\x12.ast.BigDecimalValH\x00\x12%\n\x0b\x62ig_int_val\x18\x02 \x01(\x0b\x32\x0e.ast.BigIntValH\x00\x12$\n\nbinary_val\x18\x03 \x01(\x0b\x32\x0e.ast.BinaryValH\x00\x12 \n\x08\x62ool_val\x18\x04 \x01(\x0b\x32\x0c.ast.BoolValH\x00\x12(\n\x0c\x64\x61tatype_val\x18\x05 \x01(\x0b\x32\x10.ast.DatatypeValH\x00\x12&\n\x0b\x66loat64_val\x18\x06 \x01(\x0b\x32\x0f.ast.Float64ValH\x00\x12\"\n\tint64_val\x18\x07 \x01(\x0b\x32\r.ast.Int64ValH\x00\x12 \n\x08null_val\x18\x08 \x01(\x0b\x32\x0c.ast.NullValH\x00\x12-\n\x0fpython_date_val\x18\t \x01(\x0b\x32\x12.ast.PythonDateValH\x00\x12-\n\x0fpython_time_val\x18\n \x01(\x0b\x32\x12.ast.PythonTimeValH\x00\x12\x37\n\x14python_timestamp_val\x18\x0b \x01(\x0b\x32\x17.ast.PythonTimestampValH\x00\x12,\n\x0eredacted_const\x18\x0c \x01(\x0b\x32\x12.ast.RedactedConstH\x00\x12$\n\nstring_val\x18\r \x01(\x0b\x32\x0e.ast.StringValH\x00\x42\t\n\x07variant\"x\n\x0f\x43reateDataframe\x12 \n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\x12.ast.DataframeData\x12$\n\x06schema\x18\x02 \x01(\x0b\x32\x14.ast.DataframeSchema\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"e\n\x0c\x44\x61taframeAgg\x12\x15\n\x02\x64\x66\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1f\n\x05\x65xprs\x18\x02 \x01(\x0b\x32\x10.ast.ExprArgList\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"T\n\x0e\x44\x61taframeAlias\x12\x15\n\x02\x64\x66\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"\xbc\x01\n\x1c\x44\x61taframeAnalyticsComputeLag\x12\x17\n\x04\x63ols\x18\x01 \x03(\x0b\x32\t.ast.Expr\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1b\n\x13\x66ormatted_col_names\x18\x03 \x03(\t\x12\x10\n\x08group_by\x18\x04 \x03(\t\x12\x0c\n\x04lags\x18\x05 \x03(\x03\x12\x10\n\x08order_by\x18\x06 \x03(\t\x12\x1d\n\x03src\x18\x07 \x01(\x0b\x32\x10.ast.SrcPosition\"\xbe\x01\n\x1d\x44\x61taframeAnalyticsComputeLead\x12\x17\n\x04\x63ols\x18\x01 \x03(\x0b\x32\t.ast.Expr\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1b\n\x13\x66ormatted_col_names\x18\x03 \x03(\t\x12\x10\n\x08group_by\x18\x04 \x03(\t\x12\r\n\x05leads\x18\x05 \x03(\x03\x12\x10\n\x08order_by\x18\x06 \x03(\t\x12\x1d\n\x03src\x18\x07 \x01(\x0b\x32\x10.ast.SrcPosition\"\xd9\x01\n\x1f\x44\x61taframeAnalyticsCumulativeAgg\x12+\n\x04\x61ggs\x18\x01 \x03(\x0b\x32\x1d.ast.Tuple_String_List_String\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1b\n\x13\x66ormatted_col_names\x18\x03 \x03(\t\x12\x10\n\x08group_by\x18\x04 \x03(\t\x12\x12\n\nis_forward\x18\x05 \x01(\x08\x12\x10\n\x08order_by\x18\x06 \x03(\t\x12\x1d\n\x03src\x18\x07 \x01(\x0b\x32\x10.ast.SrcPosition\"\xd7\x01\n\x1b\x44\x61taframeAnalyticsMovingAgg\x12+\n\x04\x61ggs\x18\x01 \x03(\x0b\x32\x1d.ast.Tuple_String_List_String\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1b\n\x13\x66ormatted_col_names\x18\x03 \x03(\t\x12\x10\n\x08group_by\x18\x04 \x03(\t\x12\x10\n\x08order_by\x18\x05 \x03(\t\x12\x1d\n\x03src\x18\x06 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x14\n\x0cwindow_sizes\x18\x07 \x03(\x03\"\xf0\x01\n\x1f\x44\x61taframeAnalyticsTimeSeriesAgg\x12+\n\x04\x61ggs\x18\x01 \x03(\x0b\x32\x1d.ast.Tuple_String_List_String\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1b\n\x13\x66ormatted_col_names\x18\x03 \x03(\t\x12\x10\n\x08group_by\x18\x04 \x03(\t\x12\x18\n\x10sliding_interval\x18\x05 \x01(\t\x12\x1d\n\x03src\x18\x06 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x10\n\x08time_col\x18\x07 \x01(\t\x12\x0f\n\x07windows\x18\x08 \x03(\t\"\xa3\x01\n\x14\x44\x61taframeCacheResult\x12\x15\n\x02\x64\x66\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12!\n\x0bobject_name\x18\x02 \x01(\x0b\x32\x0c.ast.NameRef\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x32\n\x10statement_params\x18\x04 \x03(\x0b\x32\x18.ast.Tuple_String_String\"V\n\x0c\x44\x61taframeCol\x12\x10\n\x08\x63ol_name\x18\x01 \x01(\t\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"\xce\x01\n\x10\x44\x61taframeCollect\x12\r\n\x05\x62lock\x18\x01 \x01(\x08\x12\x16\n\x0e\x63\x61se_sensitive\x18\x02 \x01(\x08\x12\x15\n\x02\x64\x66\x18\x03 \x01(\x0b\x32\t.ast.Expr\x12\x18\n\x10log_on_exception\x18\x04 \x01(\x08\x12\x0f\n\x07no_wait\x18\x05 \x01(\x08\x12\x1d\n\x03src\x18\x06 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x32\n\x10statement_params\x18\x07 \x03(\x0b\x32\x18.ast.Tuple_String_String\"\xe8\x03\n\x16\x44\x61taframeCopyIntoTable\x12,\n\x0c\x63opy_options\x18\x01 \x03(\x0b\x32\x16.ast.Tuple_String_Expr\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\r\n\x05\x66iles\x18\x03 \x03(\t\x12\x33\n\x13\x66ormat_type_options\x18\x04 \x03(\x0b\x32\x16.ast.Tuple_String_Expr\x12.\n\x0eiceberg_config\x18\x05 \x03(\x0b\x32\x16.ast.Tuple_String_Expr\x12-\n\x07pattern\x18\x06 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12\x1d\n\x03src\x18\x07 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x32\n\x10statement_params\x18\x08 \x03(\x0b\x32\x18.ast.Tuple_String_String\x12 \n\ntable_name\x18\t \x01(\x0b\x32\x0c.ast.NameRef\x12\x16\n\x0etarget_columns\x18\n \x03(\t\x12\"\n\x0ftransformations\x18\x0b \x03(\x0b\x32\t.ast.Expr\x12\x35\n\x0fvalidation_mode\x18\x0c \x01(\x0b\x32\x1c.google.protobuf.StringValue\"\x89\x01\n\x0e\x44\x61taframeCount\x12\r\n\x05\x62lock\x18\x01 \x01(\x08\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x32\n\x10statement_params\x18\x04 \x03(\x0b\x32\x18.ast.Tuple_String_String\"\xb0\x04\n$DataframeCreateOrReplaceDynamicTable\x12\"\n\x0f\x63lustering_keys\x18\x01 \x03(\x0b\x32\t.ast.Expr\x12-\n\x07\x63omment\x18\x02 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12\x38\n\x13\x64\x61ta_retention_time\x18\x03 \x01(\x0b\x32\x1b.google.protobuf.Int64Value\x12\x15\n\x02\x64\x66\x18\x04 \x01(\x0b\x32\t.ast.Expr\x12\x30\n\ninitialize\x18\x05 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12\x14\n\x0cis_transient\x18\x06 \x01(\x08\x12\x0b\n\x03lag\x18\x07 \x01(\t\x12<\n\x17max_data_extension_time\x18\x08 \x01(\x0b\x32\x1b.google.protobuf.Int64Value\x12\x1b\n\x04mode\x18\t \x01(\x0b\x32\r.ast.SaveMode\x12\x1a\n\x04name\x18\n \x01(\x0b\x32\x0c.ast.NameRef\x12\x32\n\x0crefresh_mode\x18\x0b \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12\x1d\n\x03src\x18\x0c \x01(\x0b\x32\x10.ast.SrcPosition\x12\x32\n\x10statement_params\x18\r \x03(\x0b\x32\x18.ast.Tuple_String_String\x12\x11\n\twarehouse\x18\x0e \x01(\t\"\xe4\x01\n\x1c\x44\x61taframeCreateOrReplaceView\x12-\n\x07\x63omment\x18\x01 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x0f\n\x07is_temp\x18\x03 \x01(\x08\x12\x1a\n\x04name\x18\x04 \x01(\x0b\x32\x0c.ast.NameRef\x12\x1d\n\x03src\x18\x05 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x32\n\x10statement_params\x18\x06 \x03(\x0b\x32\x18.ast.Tuple_String_String\"\xc1\x01\n\x12\x44\x61taframeCrossJoin\x12\x16\n\x03lhs\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12-\n\x07lsuffix\x18\x02 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12\x16\n\x03rhs\x18\x03 \x01(\x0b\x32\t.ast.Expr\x12-\n\x07rsuffix\x18\x04 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12\x1d\n\x03src\x18\x05 \x01(\x0b\x32\x10.ast.SrcPosition\"e\n\rDataframeCube\x12\x1e\n\x04\x63ols\x18\x01 \x01(\x0b\x32\x10.ast.ExprArgList\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"\x8d\x01\n\x11\x44\x61taframeDescribe\x12\x1e\n\x04\x63ols\x18\x01 \x01(\x0b\x32\x10.ast.ExprArgList\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\x12\"\n\x1astrings_include_math_stats\x18\x04 \x01(\x08\"I\n\x11\x44\x61taframeDistinct\x12\x15\n\x02\x64\x66\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\"e\n\rDataframeDrop\x12\x1e\n\x04\x63ols\x18\x01 \x01(\x0b\x32\x10.ast.ExprArgList\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"o\n\x17\x44\x61taframeDropDuplicates\x12\x1e\n\x04\x63ols\x18\x01 \x01(\x0b\x32\x10.ast.ExprArgList\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"a\n\x0f\x44\x61taframeExcept\x12\x15\n\x02\x64\x66\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x18\n\x05other\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"e\n\x0f\x44\x61taframeFilter\x12\x1c\n\tcondition\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"\x96\x01\n\x0e\x44\x61taframeFirst\x12\r\n\x05\x62lock\x18\x01 \x01(\x08\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x0b\n\x03num\x18\x03 \x01(\x03\x12\x1d\n\x03src\x18\x04 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x32\n\x10statement_params\x18\x05 \x03(\x0b\x32\x18.ast.Tuple_String_String\"\xd0\x01\n\x10\x44\x61taframeFlatten\x12\x15\n\x02\x64\x66\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x18\n\x05input\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1e\n\x04mode\x18\x03 \x01(\x0b\x32\x10.ast.FlattenMode\x12\r\n\x05outer\x18\x04 \x01(\x08\x12*\n\x04path\x18\x05 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12\x11\n\trecursive\x18\x06 \x01(\x08\x12\x1d\n\x03src\x18\x07 \x01(\x0b\x32\x10.ast.SrcPosition\"h\n\x10\x44\x61taframeGroupBy\x12\x1e\n\x04\x63ols\x18\x01 \x01(\x0b\x32\x10.ast.ExprArgList\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"}\n\x1c\x44\x61taframeGroupByGroupingSets\x12\x15\n\x02\x64\x66\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\'\n\rgrouping_sets\x18\x02 \x01(\x0b\x32\x10.ast.ExprArgList\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"d\n\x12\x44\x61taframeIntersect\x12\x15\n\x02\x64\x66\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x18\n\x05other\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"\xa0\x02\n\rDataframeJoin\x12\x1c\n\tjoin_expr\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12 \n\tjoin_type\x18\x02 \x01(\x0b\x32\r.ast.JoinType\x12\x16\n\x03lhs\x18\x03 \x01(\x0b\x32\t.ast.Expr\x12-\n\x07lsuffix\x18\x04 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12\"\n\x0fmatch_condition\x18\x05 \x01(\x0b\x32\t.ast.Expr\x12\x16\n\x03rhs\x18\x06 \x01(\x0b\x32\t.ast.Expr\x12-\n\x07rsuffix\x18\x07 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12\x1d\n\x03src\x18\x08 \x01(\x0b\x32\x10.ast.SrcPosition\"j\n\x1a\x44\x61taframeJoinTableFunction\x12\x15\n\x02\x66n\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x16\n\x03lhs\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"a\n\x0e\x44\x61taframeLimit\x12\x15\n\x02\x64\x66\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\t\n\x01n\x18\x02 \x01(\x03\x12\x0e\n\x06offset\x18\x03 \x01(\x03\x12\x1d\n\x03src\x18\x04 \x01(\x0b\x32\x10.ast.SrcPosition\"\xaa\x01\n\x16\x44\x61taframeNaDrop_Python\x12\x15\n\x02\x64\x66\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x0b\n\x03how\x18\x02 \x01(\t\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\x12 \n\x06subset\x18\x04 \x01(\x0b\x32\x10.ast.ExprArgList\x12+\n\x06thresh\x18\x05 \x01(\x0b\x32\x1b.google.protobuf.Int64Value\"z\n\x15\x44\x61taframeNaDrop_Scala\x12\x0c\n\x04\x63ols\x18\x01 \x03(\t\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x15min_non_nulls_per_row\x18\x03 \x01(\x03\x12\x1d\n\x03src\x18\x04 \x01(\x0b\x32\x10.ast.SrcPosition\"\xc7\x01\n\x0f\x44\x61taframeNaFill\x12\x15\n\x02\x64\x66\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x17\n\x0finclude_decimal\x18\x02 \x01(\x08\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\x12 \n\x06subset\x18\x04 \x01(\x0b\x32\x10.ast.ExprArgList\x12\x18\n\x05value\x18\x05 \x01(\x0b\x32\t.ast.Expr\x12)\n\tvalue_map\x18\x06 \x03(\x0b\x32\x16.ast.Tuple_String_Expr\"\xb2\x02\n\x12\x44\x61taframeNaReplace\x12\x15\n\x02\x64\x66\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x17\n\x0finclude_decimal\x18\x02 \x01(\x08\x12-\n\x0freplacement_map\x18\x03 \x03(\x0b\x32\x14.ast.Tuple_Expr_Expr\x12\x1d\n\x03src\x18\x04 \x01(\x0b\x32\x10.ast.SrcPosition\x12 \n\x06subset\x18\x05 \x01(\x0b\x32\x10.ast.ExprArgList\x12\"\n\x0fto_replace_list\x18\x06 \x03(\x0b\x32\t.ast.Expr\x12#\n\x10to_replace_value\x18\x07 \x01(\x0b\x32\t.ast.Expr\x12\x18\n\x05value\x18\x08 \x01(\x0b\x32\t.ast.Expr\x12\x19\n\x06values\x18\t \x03(\x0b\x32\t.ast.Expr\"\x87\x01\n\x14\x44\x61taframeNaturalJoin\x12 \n\tjoin_type\x18\x01 \x01(\x0b\x32\r.ast.JoinType\x12\x16\n\x03lhs\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x16\n\x03rhs\x18\x03 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x04 \x01(\x0b\x32\x10.ast.SrcPosition\"\xa3\x01\n\x0e\x44\x61taframePivot\x12\"\n\x0f\x64\x65\x66\x61ult_on_null\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1c\n\tpivot_col\x18\x03 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x04 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x19\n\x06values\x18\x05 \x01(\x0b\x32\t.ast.Expr\"\xbc\x01\n\x14\x44\x61taframeRandomSplit\x12\x15\n\x02\x64\x66\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12)\n\x04seed\x18\x02 \x01(\x0b\x32\x1b.google.protobuf.Int64Value\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x32\n\x10statement_params\x18\x04 \x03(\x0b\x32\x18.ast.Tuple_String_String\x12\x0f\n\x07weights\x18\x05 \x03(\x01\"\xd4\x01\n\x0f\x44\x61taframeReader\x12,\n\x06\x66ormat\x18\x01 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12*\n\x10metadata_columns\x18\x02 \x01(\x0b\x32\x10.ast.ExprArgList\x12\'\n\x07options\x18\x03 \x03(\x0b\x32\x16.ast.Tuple_String_Expr\x12\x1f\n\x06schema\x18\x04 \x01(\x0b\x32\x0f.ast.StructType\x12\x1d\n\x03src\x18\x05 \x01(\x0b\x32\x10.ast.SrcPosition\"9\n\x0c\x44\x61taframeRef\x12\n\n\x02id\x18\x01 \x01(\x03\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\"\x9b\x01\n\x0f\x44\x61taframeRename\x12 \n\rcol_or_mapper\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x30\n\nnew_column\x18\x03 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12\x1d\n\x03src\x18\x04 \x01(\x0b\x32\x10.ast.SrcPosition\"g\n\x0f\x44\x61taframeRollup\x12\x1e\n\x04\x63ols\x18\x01 \x01(\x0b\x32\x10.ast.ExprArgList\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"\xd8\x01\n\x0f\x44\x61taframeSample\x12\x15\n\x02\x64\x66\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12(\n\x03num\x18\x02 \x01(\x0b\x32\x1b.google.protobuf.Int64Value\x12:\n\x14probability_fraction\x18\x03 \x01(\x0b\x32\x1c.google.protobuf.DoubleValue\x12)\n\x04seed\x18\x04 \x01(\x0b\x32\x1b.google.protobuf.Int64Value\x12\x1d\n\x03src\x18\x05 \x01(\x0b\x32\x10.ast.SrcPosition\"}\n\x0f\x44\x61taframeSelect\x12\x1e\n\x04\x63ols\x18\x01 \x01(\x0b\x32\x10.ast.ExprArgList\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x14\n\x0c\x65xpr_variant\x18\x03 \x01(\x08\x12\x1d\n\x03src\x18\x04 \x01(\x0b\x32\x10.ast.SrcPosition\"P\n\rDataframeShow\x12\x15\n\x02\x64\x66\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\t\n\x01n\x18\x02 \x01(\x03\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"\x83\x01\n\rDataframeSort\x12\x1c\n\tascending\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1e\n\x04\x63ols\x18\x02 \x01(\x0b\x32\x10.ast.ExprArgList\x12\x15\n\x02\x64\x66\x18\x03 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x04 \x01(\x0b\x32\x10.ast.SrcPosition\"\xbb\x01\n\x1b\x44\x61taframeStatApproxQuantile\x12\x1e\n\x04\x63ols\x18\x01 \x01(\x0b\x32\x10.ast.ExprArgList\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x12\n\npercentile\x18\x03 \x03(\x01\x12\x1d\n\x03src\x18\x04 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x32\n\x10statement_params\x18\x05 \x03(\x0b\x32\x18.ast.Tuple_String_String\"\xaf\x01\n\x11\x44\x61taframeStatCorr\x12\x17\n\x04\x63ol1\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x17\n\x04\x63ol2\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x15\n\x02\x64\x66\x18\x03 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x04 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x32\n\x10statement_params\x18\x05 \x03(\x0b\x32\x18.ast.Tuple_String_String\"\xae\x01\n\x10\x44\x61taframeStatCov\x12\x17\n\x04\x63ol1\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x17\n\x04\x63ol2\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x15\n\x02\x64\x66\x18\x03 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x04 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x32\n\x10statement_params\x18\x05 \x03(\x0b\x32\x18.ast.Tuple_String_String\"\xb3\x01\n\x15\x44\x61taframeStatCrossTab\x12\x17\n\x04\x63ol1\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x17\n\x04\x63ol2\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x15\n\x02\x64\x66\x18\x03 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x04 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x32\n\x10statement_params\x18\x05 \x03(\x0b\x32\x18.ast.Tuple_String_String\"\x8f\x01\n\x15\x44\x61taframeStatSampleBy\x12\x16\n\x03\x63ol\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12(\n\tfractions\x18\x03 \x03(\x0b\x32\x15.ast.Tuple_Expr_Float\x12\x1d\n\x03src\x18\x04 \x01(\x0b\x32\x10.ast.SrcPosition\"j\n\rDataframeToDf\x12#\n\tcol_names\x18\x01 \x01(\x0b\x32\x10.ast.ExprArgList\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"\xab\x01\n\x18\x44\x61taframeToLocalIterator\x12\r\n\x05\x62lock\x18\x01 \x01(\x08\x12\x16\n\x0e\x63\x61se_sensitive\x18\x02 \x01(\x08\x12\x15\n\x02\x64\x66\x18\x03 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x04 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x32\n\x10statement_params\x18\x05 \x03(\x0b\x32\x18.ast.Tuple_String_String\"\x8c\x01\n\x11\x44\x61taframeToPandas\x12\r\n\x05\x62lock\x18\x01 \x01(\x08\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x32\n\x10statement_params\x18\x04 \x03(\x0b\x32\x18.ast.Tuple_String_String\"\x93\x01\n\x18\x44\x61taframeToPandasBatches\x12\r\n\x05\x62lock\x18\x01 \x01(\x08\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x32\n\x10statement_params\x18\x04 \x03(\x0b\x32\x18.ast.Tuple_String_String\"\x9d\x01\n\x0e\x44\x61taframeUnion\x12\x0b\n\x03\x61ll\x18\x01 \x01(\x08\x12\x1d\n\x15\x61llow_missing_columns\x18\x02 \x01(\x08\x12\x0f\n\x07\x62y_name\x18\x03 \x01(\x08\x12\x15\n\x02\x64\x66\x18\x04 \x01(\x0b\x32\t.ast.Expr\x12\x18\n\x05other\x18\x05 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x06 \x01(\x0b\x32\x10.ast.SrcPosition\"\xaa\x01\n\x10\x44\x61taframeUnpivot\x12\x1e\n\x0b\x63olumn_list\x18\x01 \x03(\x0b\x32\t.ast.Expr\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x15\n\rinclude_nulls\x18\x03 \x01(\x08\x12\x13\n\x0bname_column\x18\x04 \x01(\t\x12\x1d\n\x03src\x18\x05 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x14\n\x0cvalue_column\x18\x06 \x01(\t\"u\n\x13\x44\x61taframeWithColumn\x12\x16\n\x03\x63ol\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x10\n\x08\x63ol_name\x18\x02 \x01(\t\x12\x15\n\x02\x64\x66\x18\x03 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x04 \x01(\x0b\x32\x10.ast.SrcPosition\"|\n\x1a\x44\x61taframeWithColumnRenamed\x12\x16\n\x03\x63ol\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x10\n\x08new_name\x18\x03 \x01(\t\x12\x1d\n\x03src\x18\x04 \x01(\x0b\x32\x10.ast.SrcPosition\"z\n\x14\x44\x61taframeWithColumns\x12\x11\n\tcol_names\x18\x01 \x03(\t\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x19\n\x06values\x18\x04 \x03(\x0b\x32\t.ast.Expr\"\xe1\x01\n\x0f\x44\x61taframeWriter\x12\x15\n\x02\x64\x66\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12,\n\x06\x66ormat\x18\x02 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12\'\n\x07options\x18\x03 \x03(\x0b\x32\x16.ast.Tuple_String_Expr\x12\x1f\n\x0cpartition_by\x18\x04 \x01(\x0b\x32\t.ast.Expr\x12 \n\tsave_mode\x18\x05 \x01(\x0b\x32\r.ast.SaveMode\x12\x1d\n\x03src\x18\x06 \x01(\x0b\x32\x10.ast.SrcPosition\"M\n\x0b\x44\x61tatypeVal\x12\x1f\n\x08\x64\x61tatype\x18\x01 \x01(\x0b\x32\r.ast.DataType\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\"T\n\x03\x44iv\x12\x16\n\x03lhs\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x16\n\x03rhs\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"S\n\x02\x45q\x12\x16\n\x03lhs\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x16\n\x03rhs\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"\x8a\x01\n\x05\x45rror\x12.\n\x0f\x65xtension_error\x18\x01 \x01(\x0b\x32\x13.ast.ExtensionErrorH\x00\x12\x46\n\x1csession_reset_required_error\x18\x02 \x01(\x0b\x32\x1e.ast.SessionResetRequiredErrorH\x00\x42\t\n\x07variant\"\x17\n\x04\x45val\x12\x0f\n\x07\x62ind_id\x18\x01 \x01(\x03\"E\n\x06\x45valOk\x12\x0f\n\x07\x62ind_id\x18\x01 \x01(\x03\x12\x1d\n\x04\x64\x61ta\x18\x02 \x01(\x0b\x32\x0f.ast.EvalResult\x12\x0b\n\x03uid\x18\x03 \x01(\x03\"\xed\x05\n\nEvalResult\x12!\n\x0btrait_const\x18\x01 \x01(\x0b\x32\n.ast.ConstH\x00\x12-\n\x0f\x62ig_decimal_val\x18\x02 \x01(\x0b\x32\x12.ast.BigDecimalValH\x00\x12%\n\x0b\x62ig_int_val\x18\x03 \x01(\x0b\x32\x0e.ast.BigIntValH\x00\x12$\n\nbinary_val\x18\x04 \x01(\x0b\x32\x0e.ast.BinaryValH\x00\x12 \n\x08\x62ool_val\x18\x05 \x01(\x0b\x32\x0c.ast.BoolValH\x00\x12(\n\x0c\x64\x61tatype_val\x18\x06 \x01(\x0b\x32\x10.ast.DatatypeValH\x00\x12\x39\n\x15\x65xtension_eval_result\x18\x07 \x01(\x0b\x32\x18.ast.ExtensionEvalResultH\x00\x12&\n\x0b\x66loat64_val\x18\x08 \x01(\x0b\x32\x0f.ast.Float64ValH\x00\x12\"\n\tint64_val\x18\t \x01(\x0b\x32\r.ast.Int64ValH\x00\x12 \n\x08null_val\x18\n \x01(\x0b\x32\x0c.ast.NullValH\x00\x12-\n\x0fpython_date_val\x18\x0b \x01(\x0b\x32\x12.ast.PythonDateValH\x00\x12-\n\x0fpython_time_val\x18\x0c \x01(\x0b\x32\x12.ast.PythonTimeValH\x00\x12\x37\n\x14python_timestamp_val\x18\r \x01(\x0b\x32\x17.ast.PythonTimestampValH\x00\x12,\n\x0eredacted_const\x18\x0e \x01(\x0b\x32\x12.ast.RedactedConstH\x00\x12-\n\x0fsf_query_result\x18\x0f \x01(\x0b\x32\x12.ast.SfQueryResultH\x00\x12&\n\x0bshow_result\x18\x10 \x01(\x0b\x32\x0f.ast.ShowResultH\x00\x12$\n\nstring_val\x18\x11 \x01(\x0b\x32\x0e.ast.StringValH\x00\x42\t\n\x07variant\"\x8b\x45\n\x04\x45xpr\x12\"\n\x0ctrait_bin_op\x18\x01 \x01(\x0b\x32\n.ast.BinOpH\x00\x12(\n\x0ftrait_column_fn\x18\x02 \x01(\x0b\x32\r.ast.ColumnFnH\x00\x12!\n\x0btrait_const\x18\x03 \x01(\x0b\x32\n.ast.ConstH\x00\x12\x32\n\x14trait_extension_expr\x18\x04 \x01(\x0b\x32\x12.ast.ExtensionExprH\x00\x12\x30\n\x14trait_fn_id_ref_expr\x18\x05 \x01(\x0b\x32\x10.ast.FnIdRefExprH\x00\x12\x34\n\x16trait_fn_name_ref_expr\x18\x06 \x01(\x0b\x32\x12.ast.FnNameRefExprH\x00\x12(\n\x0ftrait_read_file\x18\x07 \x01(\x0b\x32\r.ast.ReadFileH\x00\x12&\n\x0etrait_unary_op\x18\x08 \x01(\x0b\x32\x0c.ast.UnaryOpH\x00\x12*\n\x10trait_write_file\x18\t \x01(\x0b\x32\x0e.ast.WriteFileH\x00\x12\x17\n\x03\x61\x64\x64\x18\n \x01(\x0b\x32\x08.ast.AddH\x00\x12\x17\n\x03\x61nd\x18\x0b \x01(\x0b\x32\x08.ast.AndH\x00\x12$\n\napply_expr\x18\x0c \x01(\x0b\x32\x0e.ast.ApplyExprH\x00\x12-\n\x0f\x62ig_decimal_val\x18\r \x01(\x0b\x32\x12.ast.BigDecimalValH\x00\x12%\n\x0b\x62ig_int_val\x18\x0e \x01(\x0b\x32\x0e.ast.BigIntValH\x00\x12$\n\nbinary_val\x18\x0f \x01(\x0b\x32\x0e.ast.BinaryValH\x00\x12\x1e\n\x07\x62it_and\x18\x10 \x01(\x0b\x32\x0b.ast.BitAndH\x00\x12\x1c\n\x06\x62it_or\x18\x11 \x01(\x0b\x32\n.ast.BitOrH\x00\x12\x1e\n\x07\x62it_xor\x18\x12 \x01(\x0b\x32\x0b.ast.BitXorH\x00\x12 \n\x08\x62ool_val\x18\x13 \x01(\x0b\x32\x0c.ast.BoolValH\x00\x12$\n\nbuiltin_fn\x18\x14 \x01(\x0b\x32\x0e.ast.BuiltinFnH\x00\x12>\n\x18\x63\x61ll_table_function_expr\x18\x15 \x01(\x0b\x32\x1a.ast.CallTableFunctionExprH\x00\x12(\n\x0c\x63olumn_alias\x18\x16 \x01(\x0b\x32\x10.ast.ColumnAliasH\x00\x12\x31\n\x11\x63olumn_apply__int\x18\x17 \x01(\x0b\x32\x14.ast.ColumnApply_IntH\x00\x12\x37\n\x14\x63olumn_apply__string\x18\x18 \x01(\x0b\x32\x17.ast.ColumnApply_StringH\x00\x12$\n\ncolumn_asc\x18\x19 \x01(\x0b\x32\x0e.ast.ColumnAscH\x00\x12,\n\x0e\x63olumn_between\x18\x1a \x01(\x0b\x32\x12.ast.ColumnBetweenH\x00\x12/\n\x10\x63olumn_case_expr\x18\x1b \x01(\x0b\x32\x13.ast.ColumnCaseExprH\x00\x12&\n\x0b\x63olumn_cast\x18\x1c \x01(\x0b\x32\x0f.ast.ColumnCastH\x00\x12&\n\x0b\x63olumn_desc\x18\x1d \x01(\x0b\x32\x0f.ast.ColumnDescH\x00\x12/\n\x10\x63olumn_equal_nan\x18\x1e \x01(\x0b\x32\x13.ast.ColumnEqualNanH\x00\x12\x31\n\x11\x63olumn_equal_null\x18\x1f \x01(\x0b\x32\x14.ast.ColumnEqualNullH\x00\x12\"\n\tcolumn_in\x18  \x01(\x0b\x32\r.ast.ColumnInH\x00\x12\x32\n\x12\x63olumn_is_not_null\x18! \x01(\x0b\x32\x14.ast.ColumnIsNotNullH\x00\x12+\n\x0e\x63olumn_is_null\x18\" \x01(\x0b\x32\x11.ast.ColumnIsNullH\x00\x12&\n\x0b\x63olumn_over\x18# \x01(\x0b\x32\x0f.ast.ColumnOverH\x00\x12*\n\rcolumn_regexp\x18$ \x01(\x0b\x32\x11.ast.ColumnRegexpH\x00\x12\x39\n\x15\x63olumn_string_collate\x18% \x01(\x0b\x32\x18.ast.ColumnStringCollateH\x00\x12;\n\x16\x63olumn_string_contains\x18& \x01(\x0b\x32\x19.ast.ColumnStringContainsH\x00\x12<\n\x17\x63olumn_string_ends_with\x18\' \x01(\x0b\x32\x19.ast.ColumnStringEndsWithH\x00\x12\x33\n\x12\x63olumn_string_like\x18( \x01(\x0b\x32\x15.ast.ColumnStringLikeH\x00\x12@\n\x19\x63olumn_string_starts_with\x18) \x01(\x0b\x32\x1b.ast.ColumnStringStartsWithH\x00\x12\x37\n\x14\x63olumn_string_substr\x18* \x01(\x0b\x32\x17.ast.ColumnStringSubstrH\x00\x12-\n\x0f\x63olumn_try_cast\x18+ \x01(\x0b\x32\x12.ast.ColumnTryCastH\x00\x12\x35\n\x13\x63olumn_within_group\x18, \x01(\x0b\x32\x16.ast.ColumnWithinGroupH\x00\x12\x30\n\x10\x63reate_dataframe\x18- \x01(\x0b\x32\x14.ast.CreateDataframeH\x00\x12*\n\rdataframe_agg\x18. \x01(\x0b\x32\x11.ast.DataframeAggH\x00\x12.\n\x0f\x64\x61taframe_alias\x18/ \x01(\x0b\x32\x13.ast.DataframeAliasH\x00\x12L\n\x1f\x64\x61taframe_analytics_compute_lag\x18\x30 \x01(\x0b\x32!.ast.DataframeAnalyticsComputeLagH\x00\x12N\n dataframe_analytics_compute_lead\x18\x31 \x01(\x0b\x32\".ast.DataframeAnalyticsComputeLeadH\x00\x12R\n\"dataframe_analytics_cumulative_agg\x18\x32 \x01(\x0b\x32$.ast.DataframeAnalyticsCumulativeAggH\x00\x12J\n\x1e\x64\x61taframe_analytics_moving_agg\x18\x33 \x01(\x0b\x32 .ast.DataframeAnalyticsMovingAggH\x00\x12S\n#dataframe_analytics_time_series_agg\x18\x34 \x01(\x0b\x32$.ast.DataframeAnalyticsTimeSeriesAggH\x00\x12;\n\x16\x64\x61taframe_cache_result\x18\x35 \x01(\x0b\x32\x19.ast.DataframeCacheResultH\x00\x12*\n\rdataframe_col\x18\x36 \x01(\x0b\x32\x11.ast.DataframeColH\x00\x12\x32\n\x11\x64\x61taframe_collect\x18\x37 \x01(\x0b\x32\x15.ast.DataframeCollectH\x00\x12@\n\x19\x64\x61taframe_copy_into_table\x18\x38 \x01(\x0b\x32\x1b.ast.DataframeCopyIntoTableH\x00\x12.\n\x0f\x64\x61taframe_count\x18\x39 \x01(\x0b\x32\x13.ast.DataframeCountH\x00\x12^\n)dataframe_create_or_replace_dynamic_table\x18: \x01(\x0b\x32).ast.DataframeCreateOrReplaceDynamicTableH\x00\x12M\n dataframe_create_or_replace_view\x18; \x01(\x0b\x32!.ast.DataframeCreateOrReplaceViewH\x00\x12\x37\n\x14\x64\x61taframe_cross_join\x18< \x01(\x0b\x32\x17.ast.DataframeCrossJoinH\x00\x12,\n\x0e\x64\x61taframe_cube\x18= \x01(\x0b\x32\x12.ast.DataframeCubeH\x00\x12\x34\n\x12\x64\x61taframe_describe\x18> \x01(\x0b\x32\x16.ast.DataframeDescribeH\x00\x12\x34\n\x12\x64\x61taframe_distinct\x18? \x01(\x0b\x32\x16.ast.DataframeDistinctH\x00\x12,\n\x0e\x64\x61taframe_drop\x18@ \x01(\x0b\x32\x12.ast.DataframeDropH\x00\x12\x41\n\x19\x64\x61taframe_drop_duplicates\x18\x41 \x01(\x0b\x32\x1c.ast.DataframeDropDuplicatesH\x00\x12\x30\n\x10\x64\x61taframe_except\x18\x42 \x01(\x0b\x32\x14.ast.DataframeExceptH\x00\x12\x30\n\x10\x64\x61taframe_filter\x18\x43 \x01(\x0b\x32\x14.ast.DataframeFilterH\x00\x12.\n\x0f\x64\x61taframe_first\x18\x44 \x01(\x0b\x32\x13.ast.DataframeFirstH\x00\x12\x32\n\x11\x64\x61taframe_flatten\x18\x45 \x01(\x0b\x32\x15.ast.DataframeFlattenH\x00\x12\x33\n\x12\x64\x61taframe_group_by\x18\x46 \x01(\x0b\x32\x15.ast.DataframeGroupByH\x00\x12M\n dataframe_group_by_grouping_sets\x18G \x01(\x0b\x32!.ast.DataframeGroupByGroupingSetsH\x00\x12\x36\n\x13\x64\x61taframe_intersect\x18H \x01(\x0b\x32\x17.ast.DataframeIntersectH\x00\x12,\n\x0e\x64\x61taframe_join\x18I \x01(\x0b\x32\x12.ast.DataframeJoinH\x00\x12H\n\x1d\x64\x61taframe_join_table_function\x18J \x01(\x0b\x32\x1f.ast.DataframeJoinTableFunctionH\x00\x12.\n\x0f\x64\x61taframe_limit\x18K \x01(\x0b\x32\x13.ast.DataframeLimitH\x00\x12@\n\x19\x64\x61taframe_na_drop__python\x18L \x01(\x0b\x32\x1b.ast.DataframeNaDrop_PythonH\x00\x12>\n\x18\x64\x61taframe_na_drop__scala\x18M \x01(\x0b\x32\x1a.ast.DataframeNaDrop_ScalaH\x00\x12\x31\n\x11\x64\x61taframe_na_fill\x18N \x01(\x0b\x32\x14.ast.DataframeNaFillH\x00\x12\x37\n\x14\x64\x61taframe_na_replace\x18O \x01(\x0b\x32\x17.ast.DataframeNaReplaceH\x00\x12;\n\x16\x64\x61taframe_natural_join\x18P \x01(\x0b\x32\x19.ast.DataframeNaturalJoinH\x00\x12.\n\x0f\x64\x61taframe_pivot\x18Q \x01(\x0b\x32\x13.ast.DataframePivotH\x00\x12;\n\x16\x64\x61taframe_random_split\x18R \x01(\x0b\x32\x19.ast.DataframeRandomSplitH\x00\x12\x30\n\x10\x64\x61taframe_reader\x18S \x01(\x0b\x32\x14.ast.DataframeReaderH\x00\x12*\n\rdataframe_ref\x18T \x01(\x0b\x32\x11.ast.DataframeRefH\x00\x12\x30\n\x10\x64\x61taframe_rename\x18U \x01(\x0b\x32\x14.ast.DataframeRenameH\x00\x12\x30\n\x10\x64\x61taframe_rollup\x18V \x01(\x0b\x32\x14.ast.DataframeRollupH\x00\x12\x30\n\x10\x64\x61taframe_sample\x18W \x01(\x0b\x32\x14.ast.DataframeSampleH\x00\x12\x30\n\x10\x64\x61taframe_select\x18X \x01(\x0b\x32\x14.ast.DataframeSelectH\x00\x12,\n\x0e\x64\x61taframe_show\x18Y \x01(\x0b\x32\x12.ast.DataframeShowH\x00\x12,\n\x0e\x64\x61taframe_sort\x18Z \x01(\x0b\x32\x12.ast.DataframeSortH\x00\x12J\n\x1e\x64\x61taframe_stat_approx_quantile\x18[ \x01(\x0b\x32 .ast.DataframeStatApproxQuantileH\x00\x12\x35\n\x13\x64\x61taframe_stat_corr\x18\\ \x01(\x0b\x32\x16.ast.DataframeStatCorrH\x00\x12\x33\n\x12\x64\x61taframe_stat_cov\x18] \x01(\x0b\x32\x15.ast.DataframeStatCovH\x00\x12>\n\x18\x64\x61taframe_stat_cross_tab\x18^ \x01(\x0b\x32\x1a.ast.DataframeStatCrossTabH\x00\x12>\n\x18\x64\x61taframe_stat_sample_by\x18_ \x01(\x0b\x32\x1a.ast.DataframeStatSampleByH\x00\x12-\n\x0f\x64\x61taframe_to_df\x18` \x01(\x0b\x32\x12.ast.DataframeToDfH\x00\x12\x44\n\x1b\x64\x61taframe_to_local_iterator\x18\x61 \x01(\x0b\x32\x1d.ast.DataframeToLocalIteratorH\x00\x12\x35\n\x13\x64\x61taframe_to_pandas\x18\x62 \x01(\x0b\x32\x16.ast.DataframeToPandasH\x00\x12\x44\n\x1b\x64\x61taframe_to_pandas_batches\x18\x63 \x01(\x0b\x32\x1d.ast.DataframeToPandasBatchesH\x00\x12.\n\x0f\x64\x61taframe_union\x18\x64 \x01(\x0b\x32\x13.ast.DataframeUnionH\x00\x12\x32\n\x11\x64\x61taframe_unpivot\x18\x65 \x01(\x0b\x32\x15.ast.DataframeUnpivotH\x00\x12\x39\n\x15\x64\x61taframe_with_column\x18\x66 \x01(\x0b\x32\x18.ast.DataframeWithColumnH\x00\x12H\n\x1d\x64\x61taframe_with_column_renamed\x18g \x01(\x0b\x32\x1f.ast.DataframeWithColumnRenamedH\x00\x12;\n\x16\x64\x61taframe_with_columns\x18h \x01(\x0b\x32\x19.ast.DataframeWithColumnsH\x00\x12\x30\n\x10\x64\x61taframe_writer\x18i \x01(\x0b\x32\x14.ast.DataframeWriterH\x00\x12(\n\x0c\x64\x61tatype_val\x18j \x01(\x0b\x32\x10.ast.DatatypeValH\x00\x12\x17\n\x03\x64iv\x18k \x01(\x0b\x32\x08.ast.DivH\x00\x12\x15\n\x02\x65q\x18l \x01(\x0b\x32\x07.ast.EqH\x00\x12\x1f\n\x07\x66latten\x18m \x01(\x0b\x32\x0c.ast.FlattenH\x00\x12&\n\x0b\x66loat64_val\x18n \x01(\x0b\x32\x0f.ast.Float64ValH\x00\x12\x1c\n\x06\x66n_ref\x18o \x01(\x0b\x32\n.ast.FnRefH\x00\x12#\n\tgenerator\x18p \x01(\x0b\x32\x0e.ast.GeneratorH\x00\x12\x17\n\x03geq\x18q \x01(\x0b\x32\x08.ast.GeqH\x00\x12*\n\rgrouping_sets\x18r \x01(\x0b\x32\x11.ast.GroupingSetsH\x00\x12\x15\n\x02gt\x18s \x01(\x0b\x32\x07.ast.GtH\x00\x12=\n\x18indirect_table_fn_id_ref\x18t \x01(\x0b\x32\x19.ast.IndirectTableFnIdRefH\x00\x12\x41\n\x1aindirect_table_fn_name_ref\x18u \x01(\x0b\x32\x1b.ast.IndirectTableFnNameRefH\x00\x12\"\n\tint64_val\x18v \x01(\x0b\x32\r.ast.Int64ValH\x00\x12\x17\n\x03leq\x18w \x01(\x0b\x32\x08.ast.LeqH\x00\x12 \n\x08list_val\x18x \x01(\x0b\x32\x0c.ast.ListValH\x00\x12\x15\n\x02lt\x18y \x01(\x0b\x32\x07.ast.LtH\x00\x12M\n merge_delete_when_matched_clause\x18z \x01(\x0b\x32!.ast.MergeDeleteWhenMatchedClauseH\x00\x12T\n$merge_insert_when_not_matched_clause\x18{ \x01(\x0b\x32$.ast.MergeInsertWhenNotMatchedClauseH\x00\x12M\n merge_update_when_matched_clause\x18| \x01(\x0b\x32!.ast.MergeUpdateWhenMatchedClauseH\x00\x12\x17\n\x03mod\x18} \x01(\x0b\x32\x08.ast.ModH\x00\x12\x17\n\x03mul\x18~ \x01(\x0b\x32\x08.ast.MulH\x00\x12\x17\n\x03neg\x18\x7f \x01(\x0b\x32\x08.ast.NegH\x00\x12\x18\n\x03neq\x18\x80\x01 \x01(\x0b\x32\x08.ast.NeqH\x00\x12\x18\n\x03not\x18\x81\x01 \x01(\x0b\x32\x08.ast.NotH\x00\x12!\n\x08null_val\x18\x82\x01 \x01(\x0b\x32\x0c.ast.NullValH\x00\x12.\n\x0fobject_get_item\x18\x83\x01 \x01(\x0b\x32\x12.ast.ObjectGetItemH\x00\x12\x16\n\x02or\x18\x84\x01 \x01(\x0b\x32\x07.ast.OrH\x00\x12\x18\n\x03pow\x18\x85\x01 \x01(\x0b\x32\x08.ast.PowH\x00\x12.\n\x0fpython_date_val\x18\x86\x01 \x01(\x0b\x32\x12.ast.PythonDateValH\x00\x12.\n\x0fpython_time_val\x18\x87\x01 \x01(\x0b\x32\x12.ast.PythonTimeValH\x00\x12\x38\n\x14python_timestamp_val\x18\x88\x01 \x01(\x0b\x32\x17.ast.PythonTimestampValH\x00\x12\x1c\n\x05range\x18\x89\x01 \x01(\x0b\x32\n.ast.RangeH\x00\x12#\n\tread_avro\x18\x8a\x01 \x01(\x0b\x32\r.ast.ReadAvroH\x00\x12!\n\x08read_csv\x18\x8b\x01 \x01(\x0b\x32\x0c.ast.ReadCsvH\x00\x12#\n\tread_json\x18\x8c\x01 \x01(\x0b\x32\r.ast.ReadJsonH\x00\x12#\n\tread_load\x18\x8d\x01 \x01(\x0b\x32\r.ast.ReadLoadH\x00\x12!\n\x08read_orc\x18\x8e\x01 \x01(\x0b\x32\x0c.ast.ReadOrcH\x00\x12)\n\x0cread_parquet\x18\x8f\x01 \x01(\x0b\x32\x10.ast.ReadParquetH\x00\x12%\n\nread_table\x18\x90\x01 \x01(\x0b\x32\x0e.ast.ReadTableH\x00\x12!\n\x08read_xml\x18\x91\x01 \x01(\x0b\x32\x0c.ast.ReadXmlH\x00\x12-\n\x0eredacted_const\x18\x92\x01 \x01(\x0b\x32\x12.ast.RedactedConstH\x00\x12O\n relational_grouped_dataframe_agg\x18\x93\x01 \x01(\x0b\x32\".ast.RelationalGroupedDataframeAggH\x00\x12\x65\n,relational_grouped_dataframe_apply_in_pandas\x18\x94\x01 \x01(\x0b\x32,.ast.RelationalGroupedDataframeApplyInPandasH\x00\x12W\n$relational_grouped_dataframe_builtin\x18\x95\x01 \x01(\x0b\x32&.ast.RelationalGroupedDataframeBuiltinH\x00\x12S\n\"relational_grouped_dataframe_pivot\x18\x96\x01 \x01(\x0b\x32$.ast.RelationalGroupedDataframePivotH\x00\x12O\n relational_grouped_dataframe_ref\x18\x97\x01 \x01(\x0b\x32\".ast.RelationalGroupedDataframeRefH\x00\x12\x18\n\x03row\x18\x98\x01 \x01(\x0b\x32\x08.ast.RowH\x00\x12&\n\x0bseq_map_val\x18\x99\x01 \x01(\x0b\x32\x0e.ast.SeqMapValH\x00\x12<\n\x16session_table_function\x18\x9a\x01 \x01(\x0b\x32\x19.ast.SessionTableFunctionH\x00\x12\x18\n\x03sql\x18\x9b\x01 \x01(\x0b\x32\x08.ast.SqlH\x00\x12!\n\x08sql_expr\x18\x9c\x01 \x01(\x0b\x32\x0c.ast.SqlExprH\x00\x12\x31\n\x10stored_procedure\x18\x9d\x01 \x01(\x0b\x32\x14.ast.StoredProcedureH\x00\x12%\n\nstring_val\x18\x9e\x01 \x01(\x0b\x32\x0e.ast.StringValH\x00\x12\x18\n\x03sub\x18\x9f\x01 \x01(\x0b\x32\x08.ast.SubH\x00\x12\x1c\n\x05table\x18\xa0\x01 \x01(\x0b\x32\n.ast.TableH\x00\x12)\n\x0ctable_delete\x18\xa1\x01 \x01(\x0b\x32\x10.ast.TableDeleteH\x00\x12\x30\n\x10table_drop_table\x18\xa2\x01 \x01(\x0b\x32\x13.ast.TableDropTableH\x00\x12\x35\n\x13table_fn_call_alias\x18\xa3\x01 \x01(\x0b\x32\x15.ast.TableFnCallAliasH\x00\x12\x33\n\x12table_fn_call_over\x18\xa4\x01 \x01(\x0b\x32\x14.ast.TableFnCallOverH\x00\x12\'\n\x0btable_merge\x18\xa5\x01 \x01(\x0b\x32\x0f.ast.TableMergeH\x00\x12)\n\x0ctable_sample\x18\xa6\x01 \x01(\x0b\x32\x10.ast.TableSampleH\x00\x12)\n\x0ctable_update\x18\xa7\x01 \x01(\x0b\x32\x10.ast.TableUpdateH\x00\x12\x34\n\x12to_snowpark_pandas\x18\xa8\x01 \x01(\x0b\x32\x15.ast.ToSnowparkPandasH\x00\x12-\n\x0etruncated_expr\x18\xa9\x01 \x01(\x0b\x32\x12.ast.TruncatedExprH\x00\x12#\n\ttuple_val\x18\xaa\x01 \x01(\x0b\x32\r.ast.TupleValH\x00\x12\x1a\n\x04udaf\x18\xab\x01 \x01(\x0b\x32\t.ast.UdafH\x00\x12\x18\n\x03udf\x18\xac\x01 \x01(\x0b\x32\x08.ast.UdfH\x00\x12\x1a\n\x04udtf\x18\xad\x01 \x01(\x0b\x32\t.ast.UdtfH\x00\x12?\n\x18write_copy_into_location\x18\xae\x01 \x01(\x0b\x32\x1a.ast.WriteCopyIntoLocationH\x00\x12#\n\twrite_csv\x18\xaf\x01 \x01(\x0b\x32\r.ast.WriteCsvH\x00\x12\x32\n\x11write_insert_into\x18\xb0\x01 \x01(\x0b\x32\x14.ast.WriteInsertIntoH\x00\x12%\n\nwrite_json\x18\xb1\x01 \x01(\x0b\x32\x0e.ast.WriteJsonH\x00\x12)\n\x0cwrite_pandas\x18\xb2\x01 \x01(\x0b\x32\x10.ast.WritePandasH\x00\x12+\n\rwrite_parquet\x18\xb3\x01 \x01(\x0b\x32\x11.ast.WriteParquetH\x00\x12%\n\nwrite_save\x18\xb4\x01 \x01(\x0b\x32\x0e.ast.WriteSaveH\x00\x12\'\n\x0bwrite_table\x18\xb5\x01 \x01(\x0b\x32\x0f.ast.WriteTableH\x00\x42\t\n\x07variant\"8\n\x0b\x45xprArgList\x12\x17\n\x04\x61rgs\x18\x01 \x03(\x0b\x32\t.ast.Expr\x12\x10\n\x08variadic\x18\x02 \x01(\x08\"c\n\x0e\x45xtensionError\x12%\n\x05\x61ttrs\x18\x01 \x03(\x0b\x32\x16.ast.Tuple_String_Expr\x12\x0f\n\x07\x62ind_id\x18\x02 \x01(\x03\x12\x0c\n\x04kind\x18\x03 \x01(\t\x12\x0b\n\x03uid\x18\x04 \x01(\x03\"J\n\x13\x45xtensionEvalResult\x12%\n\x05\x61ttrs\x18\x01 \x03(\x0b\x32\x16.ast.Tuple_String_Expr\x12\x0c\n\x04kind\x18\x02 \x01(\t\"+\n\rExtensionExpr\x12\x0f\n\x05\x64ummy\x18\x01 \x01(\x08H\x00\x42\t\n\x07variant\"D\n\rExtensionStmt\x12%\n\x05\x61ttrs\x18\x01 \x03(\x0b\x32\x16.ast.Tuple_String_Expr\x12\x0c\n\x04kind\x18\x02 \x01(\t\"\xb0\x01\n\x07\x46latten\x12\x18\n\x05input\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1e\n\x04mode\x18\x02 \x01(\x0b\x32\x10.ast.FlattenMode\x12\r\n\x05outer\x18\x03 \x01(\x08\x12*\n\x04path\x18\x04 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12\x11\n\trecursive\x18\x05 \x01(\x08\x12\x1d\n\x03src\x18\x06 \x01(\x0b\x32\x10.ast.SrcPosition\"6\n\nFloat64Val\x12\x1d\n\x03src\x18\x01 \x01(\x0b\x32\x10.ast.SrcPosition\x12\t\n\x01v\x18\x02 \x01(\x01\"u\n\x0b\x46nIdRefExpr\x12\x1c\n\x06\x66n_ref\x18\x01 \x01(\x0b\x32\n.ast.FnRefH\x00\x12=\n\x18indirect_table_fn_id_ref\x18\x02 \x01(\x0b\x32\x19.ast.IndirectTableFnIdRefH\x00\x42\t\n\x07variant\"\xc4\x02\n\rFnNameRefExpr\x12$\n\nbuiltin_fn\x18\x01 \x01(\x0b\x32\x0e.ast.BuiltinFnH\x00\x12>\n\x18\x63\x61ll_table_function_expr\x18\x02 \x01(\x0b\x32\x1a.ast.CallTableFunctionExprH\x00\x12\x41\n\x1aindirect_table_fn_name_ref\x18\x03 \x01(\x0b\x32\x1b.ast.IndirectTableFnNameRefH\x00\x12\x30\n\x10stored_procedure\x18\x04 \x01(\x0b\x32\x14.ast.StoredProcedureH\x00\x12\x19\n\x04udaf\x18\x05 \x01(\x0b\x32\t.ast.UdafH\x00\x12\x17\n\x03udf\x18\x06 \x01(\x0b\x32\x08.ast.UdfH\x00\x12\x19\n\x04udtf\x18\x07 \x01(\x0b\x32\t.ast.UdtfH\x00\x42\t\n\x07variant\"2\n\x05\x46nRef\x12\n\n\x02id\x18\x01 \x01(\x03\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\"|\n\tGenerator\x12!\n\x07\x63olumns\x18\x01 \x01(\x0b\x32\x10.ast.ExprArgList\x12\x11\n\trow_count\x18\x02 \x01(\x03\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x1a\n\x12time_limit_seconds\x18\x04 \x01(\x03\"T\n\x03Geq\x12\x16\n\x03lhs\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x16\n\x03rhs\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"M\n\x0cGroupingSets\x12\x1e\n\x04sets\x18\x01 \x01(\x0b\x32\x10.ast.ExprArgList\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\"S\n\x02Gt\x12\x16\n\x03lhs\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x16\n\x03rhs\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"\x83I\n\x0eHasSrcPosition\x12\"\n\x0ctrait_bin_op\x18\x01 \x01(\x0b\x32\n.ast.BinOpH\x00\x12(\n\x0ftrait_column_fn\x18\x02 \x01(\x0b\x32\r.ast.ColumnFnH\x00\x12!\n\x0btrait_const\x18\x03 \x01(\x0b\x32\n.ast.ConstH\x00\x12\x1f\n\ntrait_expr\x18\x04 \x01(\x0b\x32\t.ast.ExprH\x00\x12\x32\n\x14trait_extension_expr\x18\x05 \x01(\x0b\x32\x12.ast.ExtensionExprH\x00\x12\x30\n\x14trait_fn_id_ref_expr\x18\x06 \x01(\x0b\x32\x10.ast.FnIdRefExprH\x00\x12\x34\n\x16trait_fn_name_ref_expr\x18\x07 \x01(\x0b\x32\x12.ast.FnNameRefExprH\x00\x12(\n\x0ftrait_read_file\x18\x08 \x01(\x0b\x32\r.ast.ReadFileH\x00\x12&\n\x0etrait_unary_op\x18\t \x01(\x0b\x32\x0c.ast.UnaryOpH\x00\x12\x35\n\x16trait_window_spec_expr\x18\n \x01(\x0b\x32\x13.ast.WindowSpecExprH\x00\x12*\n\x10trait_write_file\x18\x0b \x01(\x0b\x32\x0e.ast.WriteFileH\x00\x12\x17\n\x03\x61\x64\x64\x18\x0c \x01(\x0b\x32\x08.ast.AddH\x00\x12\x17\n\x03\x61nd\x18\r \x01(\x0b\x32\x08.ast.AndH\x00\x12$\n\napply_expr\x18\x0e \x01(\x0b\x32\x0e.ast.ApplyExprH\x00\x12-\n\x0f\x62ig_decimal_val\x18\x0f \x01(\x0b\x32\x12.ast.BigDecimalValH\x00\x12%\n\x0b\x62ig_int_val\x18\x10 \x01(\x0b\x32\x0e.ast.BigIntValH\x00\x12$\n\nbinary_val\x18\x11 \x01(\x0b\x32\x0e.ast.BinaryValH\x00\x12\x1e\n\x07\x62it_and\x18\x12 \x01(\x0b\x32\x0b.ast.BitAndH\x00\x12\x1c\n\x06\x62it_or\x18\x13 \x01(\x0b\x32\n.ast.BitOrH\x00\x12\x1e\n\x07\x62it_xor\x18\x14 \x01(\x0b\x32\x0b.ast.BitXorH\x00\x12 \n\x08\x62ool_val\x18\x15 \x01(\x0b\x32\x0c.ast.BoolValH\x00\x12$\n\nbuiltin_fn\x18\x16 \x01(\x0b\x32\x0e.ast.BuiltinFnH\x00\x12>\n\x18\x63\x61ll_table_function_expr\x18\x17 \x01(\x0b\x32\x1a.ast.CallTableFunctionExprH\x00\x12(\n\x0c\x63olumn_alias\x18\x18 \x01(\x0b\x32\x10.ast.ColumnAliasH\x00\x12\x31\n\x11\x63olumn_apply__int\x18\x19 \x01(\x0b\x32\x14.ast.ColumnApply_IntH\x00\x12\x37\n\x14\x63olumn_apply__string\x18\x1a \x01(\x0b\x32\x17.ast.ColumnApply_StringH\x00\x12$\n\ncolumn_asc\x18\x1b \x01(\x0b\x32\x0e.ast.ColumnAscH\x00\x12,\n\x0e\x63olumn_between\x18\x1c \x01(\x0b\x32\x12.ast.ColumnBetweenH\x00\x12/\n\x10\x63olumn_case_expr\x18\x1d \x01(\x0b\x32\x13.ast.ColumnCaseExprH\x00\x12<\n\x17\x63olumn_case_expr_clause\x18\x1e \x01(\x0b\x32\x19.ast.ColumnCaseExprClauseH\x00\x12&\n\x0b\x63olumn_cast\x18\x1f \x01(\x0b\x32\x0f.ast.ColumnCastH\x00\x12&\n\x0b\x63olumn_desc\x18  \x01(\x0b\x32\x0f.ast.ColumnDescH\x00\x12/\n\x10\x63olumn_equal_nan\x18! \x01(\x0b\x32\x13.ast.ColumnEqualNanH\x00\x12\x31\n\x11\x63olumn_equal_null\x18\" \x01(\x0b\x32\x14.ast.ColumnEqualNullH\x00\x12\"\n\tcolumn_in\x18# \x01(\x0b\x32\r.ast.ColumnInH\x00\x12\x32\n\x12\x63olumn_is_not_null\x18$ \x01(\x0b\x32\x14.ast.ColumnIsNotNullH\x00\x12+\n\x0e\x63olumn_is_null\x18% \x01(\x0b\x32\x11.ast.ColumnIsNullH\x00\x12&\n\x0b\x63olumn_over\x18& \x01(\x0b\x32\x0f.ast.ColumnOverH\x00\x12*\n\rcolumn_regexp\x18\' \x01(\x0b\x32\x11.ast.ColumnRegexpH\x00\x12\x39\n\x15\x63olumn_string_collate\x18( \x01(\x0b\x32\x18.ast.ColumnStringCollateH\x00\x12;\n\x16\x63olumn_string_contains\x18) \x01(\x0b\x32\x19.ast.ColumnStringContainsH\x00\x12<\n\x17\x63olumn_string_ends_with\x18* \x01(\x0b\x32\x19.ast.ColumnStringEndsWithH\x00\x12\x33\n\x12\x63olumn_string_like\x18+ \x01(\x0b\x32\x15.ast.ColumnStringLikeH\x00\x12@\n\x19\x63olumn_string_starts_with\x18, \x01(\x0b\x32\x1b.ast.ColumnStringStartsWithH\x00\x12\x37\n\x14\x63olumn_string_substr\x18- \x01(\x0b\x32\x17.ast.ColumnStringSubstrH\x00\x12-\n\x0f\x63olumn_try_cast\x18. \x01(\x0b\x32\x12.ast.ColumnTryCastH\x00\x12\x35\n\x13\x63olumn_within_group\x18/ \x01(\x0b\x32\x16.ast.ColumnWithinGroupH\x00\x12\x30\n\x10\x63reate_dataframe\x18\x30 \x01(\x0b\x32\x14.ast.CreateDataframeH\x00\x12*\n\rdataframe_agg\x18\x31 \x01(\x0b\x32\x11.ast.DataframeAggH\x00\x12.\n\x0f\x64\x61taframe_alias\x18\x32 \x01(\x0b\x32\x13.ast.DataframeAliasH\x00\x12L\n\x1f\x64\x61taframe_analytics_compute_lag\x18\x33 \x01(\x0b\x32!.ast.DataframeAnalyticsComputeLagH\x00\x12N\n dataframe_analytics_compute_lead\x18\x34 \x01(\x0b\x32\".ast.DataframeAnalyticsComputeLeadH\x00\x12R\n\"dataframe_analytics_cumulative_agg\x18\x35 \x01(\x0b\x32$.ast.DataframeAnalyticsCumulativeAggH\x00\x12J\n\x1e\x64\x61taframe_analytics_moving_agg\x18\x36 \x01(\x0b\x32 .ast.DataframeAnalyticsMovingAggH\x00\x12S\n#dataframe_analytics_time_series_agg\x18\x37 \x01(\x0b\x32$.ast.DataframeAnalyticsTimeSeriesAggH\x00\x12;\n\x16\x64\x61taframe_cache_result\x18\x38 \x01(\x0b\x32\x19.ast.DataframeCacheResultH\x00\x12*\n\rdataframe_col\x18\x39 \x01(\x0b\x32\x11.ast.DataframeColH\x00\x12\x32\n\x11\x64\x61taframe_collect\x18: \x01(\x0b\x32\x15.ast.DataframeCollectH\x00\x12@\n\x19\x64\x61taframe_copy_into_table\x18; \x01(\x0b\x32\x1b.ast.DataframeCopyIntoTableH\x00\x12.\n\x0f\x64\x61taframe_count\x18< \x01(\x0b\x32\x13.ast.DataframeCountH\x00\x12^\n)dataframe_create_or_replace_dynamic_table\x18= \x01(\x0b\x32).ast.DataframeCreateOrReplaceDynamicTableH\x00\x12M\n dataframe_create_or_replace_view\x18> \x01(\x0b\x32!.ast.DataframeCreateOrReplaceViewH\x00\x12\x37\n\x14\x64\x61taframe_cross_join\x18? \x01(\x0b\x32\x17.ast.DataframeCrossJoinH\x00\x12,\n\x0e\x64\x61taframe_cube\x18@ \x01(\x0b\x32\x12.ast.DataframeCubeH\x00\x12\x34\n\x12\x64\x61taframe_describe\x18\x41 \x01(\x0b\x32\x16.ast.DataframeDescribeH\x00\x12\x34\n\x12\x64\x61taframe_distinct\x18\x42 \x01(\x0b\x32\x16.ast.DataframeDistinctH\x00\x12,\n\x0e\x64\x61taframe_drop\x18\x43 \x01(\x0b\x32\x12.ast.DataframeDropH\x00\x12\x41\n\x19\x64\x61taframe_drop_duplicates\x18\x44 \x01(\x0b\x32\x1c.ast.DataframeDropDuplicatesH\x00\x12\x30\n\x10\x64\x61taframe_except\x18\x45 \x01(\x0b\x32\x14.ast.DataframeExceptH\x00\x12\x30\n\x10\x64\x61taframe_filter\x18\x46 \x01(\x0b\x32\x14.ast.DataframeFilterH\x00\x12.\n\x0f\x64\x61taframe_first\x18G \x01(\x0b\x32\x13.ast.DataframeFirstH\x00\x12\x32\n\x11\x64\x61taframe_flatten\x18H \x01(\x0b\x32\x15.ast.DataframeFlattenH\x00\x12\x33\n\x12\x64\x61taframe_group_by\x18I \x01(\x0b\x32\x15.ast.DataframeGroupByH\x00\x12M\n dataframe_group_by_grouping_sets\x18J \x01(\x0b\x32!.ast.DataframeGroupByGroupingSetsH\x00\x12\x36\n\x13\x64\x61taframe_intersect\x18K \x01(\x0b\x32\x17.ast.DataframeIntersectH\x00\x12,\n\x0e\x64\x61taframe_join\x18L \x01(\x0b\x32\x12.ast.DataframeJoinH\x00\x12H\n\x1d\x64\x61taframe_join_table_function\x18M \x01(\x0b\x32\x1f.ast.DataframeJoinTableFunctionH\x00\x12.\n\x0f\x64\x61taframe_limit\x18N \x01(\x0b\x32\x13.ast.DataframeLimitH\x00\x12@\n\x19\x64\x61taframe_na_drop__python\x18O \x01(\x0b\x32\x1b.ast.DataframeNaDrop_PythonH\x00\x12>\n\x18\x64\x61taframe_na_drop__scala\x18P \x01(\x0b\x32\x1a.ast.DataframeNaDrop_ScalaH\x00\x12\x31\n\x11\x64\x61taframe_na_fill\x18Q \x01(\x0b\x32\x14.ast.DataframeNaFillH\x00\x12\x37\n\x14\x64\x61taframe_na_replace\x18R \x01(\x0b\x32\x17.ast.DataframeNaReplaceH\x00\x12;\n\x16\x64\x61taframe_natural_join\x18S \x01(\x0b\x32\x19.ast.DataframeNaturalJoinH\x00\x12.\n\x0f\x64\x61taframe_pivot\x18T \x01(\x0b\x32\x13.ast.DataframePivotH\x00\x12;\n\x16\x64\x61taframe_random_split\x18U \x01(\x0b\x32\x19.ast.DataframeRandomSplitH\x00\x12\x30\n\x10\x64\x61taframe_reader\x18V \x01(\x0b\x32\x14.ast.DataframeReaderH\x00\x12*\n\rdataframe_ref\x18W \x01(\x0b\x32\x11.ast.DataframeRefH\x00\x12\x30\n\x10\x64\x61taframe_rename\x18X \x01(\x0b\x32\x14.ast.DataframeRenameH\x00\x12\x30\n\x10\x64\x61taframe_rollup\x18Y \x01(\x0b\x32\x14.ast.DataframeRollupH\x00\x12\x30\n\x10\x64\x61taframe_sample\x18Z \x01(\x0b\x32\x14.ast.DataframeSampleH\x00\x12\x30\n\x10\x64\x61taframe_select\x18[ \x01(\x0b\x32\x14.ast.DataframeSelectH\x00\x12,\n\x0e\x64\x61taframe_show\x18\\ \x01(\x0b\x32\x12.ast.DataframeShowH\x00\x12,\n\x0e\x64\x61taframe_sort\x18] \x01(\x0b\x32\x12.ast.DataframeSortH\x00\x12J\n\x1e\x64\x61taframe_stat_approx_quantile\x18^ \x01(\x0b\x32 .ast.DataframeStatApproxQuantileH\x00\x12\x35\n\x13\x64\x61taframe_stat_corr\x18_ \x01(\x0b\x32\x16.ast.DataframeStatCorrH\x00\x12\x33\n\x12\x64\x61taframe_stat_cov\x18` \x01(\x0b\x32\x15.ast.DataframeStatCovH\x00\x12>\n\x18\x64\x61taframe_stat_cross_tab\x18\x61 \x01(\x0b\x32\x1a.ast.DataframeStatCrossTabH\x00\x12>\n\x18\x64\x61taframe_stat_sample_by\x18\x62 \x01(\x0b\x32\x1a.ast.DataframeStatSampleByH\x00\x12-\n\x0f\x64\x61taframe_to_df\x18\x63 \x01(\x0b\x32\x12.ast.DataframeToDfH\x00\x12\x44\n\x1b\x64\x61taframe_to_local_iterator\x18\x64 \x01(\x0b\x32\x1d.ast.DataframeToLocalIteratorH\x00\x12\x35\n\x13\x64\x61taframe_to_pandas\x18\x65 \x01(\x0b\x32\x16.ast.DataframeToPandasH\x00\x12\x44\n\x1b\x64\x61taframe_to_pandas_batches\x18\x66 \x01(\x0b\x32\x1d.ast.DataframeToPandasBatchesH\x00\x12.\n\x0f\x64\x61taframe_union\x18g \x01(\x0b\x32\x13.ast.DataframeUnionH\x00\x12\x32\n\x11\x64\x61taframe_unpivot\x18h \x01(\x0b\x32\x15.ast.DataframeUnpivotH\x00\x12\x39\n\x15\x64\x61taframe_with_column\x18i \x01(\x0b\x32\x18.ast.DataframeWithColumnH\x00\x12H\n\x1d\x64\x61taframe_with_column_renamed\x18j \x01(\x0b\x32\x1f.ast.DataframeWithColumnRenamedH\x00\x12;\n\x16\x64\x61taframe_with_columns\x18k \x01(\x0b\x32\x19.ast.DataframeWithColumnsH\x00\x12\x30\n\x10\x64\x61taframe_writer\x18l \x01(\x0b\x32\x14.ast.DataframeWriterH\x00\x12(\n\x0c\x64\x61tatype_val\x18m \x01(\x0b\x32\x10.ast.DatatypeValH\x00\x12\x17\n\x03\x64iv\x18n \x01(\x0b\x32\x08.ast.DivH\x00\x12\x15\n\x02\x65q\x18o \x01(\x0b\x32\x07.ast.EqH\x00\x12\x1f\n\x07\x66latten\x18p \x01(\x0b\x32\x0c.ast.FlattenH\x00\x12&\n\x0b\x66loat64_val\x18q \x01(\x0b\x32\x0f.ast.Float64ValH\x00\x12\x1c\n\x06\x66n_ref\x18r \x01(\x0b\x32\n.ast.FnRefH\x00\x12#\n\tgenerator\x18s \x01(\x0b\x32\x0e.ast.GeneratorH\x00\x12\x17\n\x03geq\x18t \x01(\x0b\x32\x08.ast.GeqH\x00\x12*\n\rgrouping_sets\x18u \x01(\x0b\x32\x11.ast.GroupingSetsH\x00\x12\x15\n\x02gt\x18v \x01(\x0b\x32\x07.ast.GtH\x00\x12=\n\x18indirect_table_fn_id_ref\x18w \x01(\x0b\x32\x19.ast.IndirectTableFnIdRefH\x00\x12\x41\n\x1aindirect_table_fn_name_ref\x18x \x01(\x0b\x32\x1b.ast.IndirectTableFnNameRefH\x00\x12\"\n\tint64_val\x18y \x01(\x0b\x32\r.ast.Int64ValH\x00\x12\x17\n\x03leq\x18z \x01(\x0b\x32\x08.ast.LeqH\x00\x12 \n\x08list_val\x18{ \x01(\x0b\x32\x0c.ast.ListValH\x00\x12\x15\n\x02lt\x18| \x01(\x0b\x32\x07.ast.LtH\x00\x12M\n merge_delete_when_matched_clause\x18} \x01(\x0b\x32!.ast.MergeDeleteWhenMatchedClauseH\x00\x12T\n$merge_insert_when_not_matched_clause\x18~ \x01(\x0b\x32$.ast.MergeInsertWhenNotMatchedClauseH\x00\x12M\n merge_update_when_matched_clause\x18\x7f \x01(\x0b\x32!.ast.MergeUpdateWhenMatchedClauseH\x00\x12\x18\n\x03mod\x18\x80\x01 \x01(\x0b\x32\x08.ast.ModH\x00\x12\x18\n\x03mul\x18\x81\x01 \x01(\x0b\x32\x08.ast.MulH\x00\x12!\n\x08name_ref\x18\x82\x01 \x01(\x0b\x32\x0c.ast.NameRefH\x00\x12\x18\n\x03neg\x18\x83\x01 \x01(\x0b\x32\x08.ast.NegH\x00\x12\x18\n\x03neq\x18\x84\x01 \x01(\x0b\x32\x08.ast.NeqH\x00\x12\x18\n\x03not\x18\x85\x01 \x01(\x0b\x32\x08.ast.NotH\x00\x12!\n\x08null_val\x18\x86\x01 \x01(\x0b\x32\x0c.ast.NullValH\x00\x12.\n\x0fobject_get_item\x18\x87\x01 \x01(\x0b\x32\x12.ast.ObjectGetItemH\x00\x12\x16\n\x02or\x18\x88\x01 \x01(\x0b\x32\x07.ast.OrH\x00\x12\x18\n\x03pow\x18\x89\x01 \x01(\x0b\x32\x08.ast.PowH\x00\x12.\n\x0fpython_date_val\x18\x8a\x01 \x01(\x0b\x32\x12.ast.PythonDateValH\x00\x12.\n\x0fpython_time_val\x18\x8b\x01 \x01(\x0b\x32\x12.ast.PythonTimeValH\x00\x12\x38\n\x14python_timestamp_val\x18\x8c\x01 \x01(\x0b\x32\x17.ast.PythonTimestampValH\x00\x12\x1c\n\x05range\x18\x8d\x01 \x01(\x0b\x32\n.ast.RangeH\x00\x12#\n\tread_avro\x18\x8e\x01 \x01(\x0b\x32\r.ast.ReadAvroH\x00\x12!\n\x08read_csv\x18\x8f\x01 \x01(\x0b\x32\x0c.ast.ReadCsvH\x00\x12#\n\tread_json\x18\x90\x01 \x01(\x0b\x32\r.ast.ReadJsonH\x00\x12#\n\tread_load\x18\x91\x01 \x01(\x0b\x32\r.ast.ReadLoadH\x00\x12!\n\x08read_orc\x18\x92\x01 \x01(\x0b\x32\x0c.ast.ReadOrcH\x00\x12)\n\x0cread_parquet\x18\x93\x01 \x01(\x0b\x32\x10.ast.ReadParquetH\x00\x12%\n\nread_table\x18\x94\x01 \x01(\x0b\x32\x0e.ast.ReadTableH\x00\x12!\n\x08read_xml\x18\x95\x01 \x01(\x0b\x32\x0c.ast.ReadXmlH\x00\x12-\n\x0eredacted_const\x18\x96\x01 \x01(\x0b\x32\x12.ast.RedactedConstH\x00\x12O\n relational_grouped_dataframe_agg\x18\x97\x01 \x01(\x0b\x32\".ast.RelationalGroupedDataframeAggH\x00\x12\x65\n,relational_grouped_dataframe_apply_in_pandas\x18\x98\x01 \x01(\x0b\x32,.ast.RelationalGroupedDataframeApplyInPandasH\x00\x12W\n$relational_grouped_dataframe_builtin\x18\x99\x01 \x01(\x0b\x32&.ast.RelationalGroupedDataframeBuiltinH\x00\x12S\n\"relational_grouped_dataframe_pivot\x18\x9a\x01 \x01(\x0b\x32$.ast.RelationalGroupedDataframePivotH\x00\x12O\n relational_grouped_dataframe_ref\x18\x9b\x01 \x01(\x0b\x32\".ast.RelationalGroupedDataframeRefH\x00\x12\x18\n\x03row\x18\x9c\x01 \x01(\x0b\x32\x08.ast.RowH\x00\x12&\n\x0bseq_map_val\x18\x9d\x01 \x01(\x0b\x32\x0e.ast.SeqMapValH\x00\x12<\n\x16session_table_function\x18\x9e\x01 \x01(\x0b\x32\x19.ast.SessionTableFunctionH\x00\x12\x18\n\x03sql\x18\x9f\x01 \x01(\x0b\x32\x08.ast.SqlH\x00\x12!\n\x08sql_expr\x18\xa0\x01 \x01(\x0b\x32\x0c.ast.SqlExprH\x00\x12\x31\n\x10stored_procedure\x18\xa1\x01 \x01(\x0b\x32\x14.ast.StoredProcedureH\x00\x12%\n\nstring_val\x18\xa2\x01 \x01(\x0b\x32\x0e.ast.StringValH\x00\x12\x18\n\x03sub\x18\xa3\x01 \x01(\x0b\x32\x08.ast.SubH\x00\x12\x1c\n\x05table\x18\xa4\x01 \x01(\x0b\x32\n.ast.TableH\x00\x12)\n\x0ctable_delete\x18\xa5\x01 \x01(\x0b\x32\x10.ast.TableDeleteH\x00\x12\x30\n\x10table_drop_table\x18\xa6\x01 \x01(\x0b\x32\x13.ast.TableDropTableH\x00\x12\x35\n\x13table_fn_call_alias\x18\xa7\x01 \x01(\x0b\x32\x15.ast.TableFnCallAliasH\x00\x12\x33\n\x12table_fn_call_over\x18\xa8\x01 \x01(\x0b\x32\x14.ast.TableFnCallOverH\x00\x12\'\n\x0btable_merge\x18\xa9\x01 \x01(\x0b\x32\x0f.ast.TableMergeH\x00\x12)\n\x0ctable_sample\x18\xaa\x01 \x01(\x0b\x32\x10.ast.TableSampleH\x00\x12)\n\x0ctable_update\x18\xab\x01 \x01(\x0b\x32\x10.ast.TableUpdateH\x00\x12\x34\n\x12to_snowpark_pandas\x18\xac\x01 \x01(\x0b\x32\x15.ast.ToSnowparkPandasH\x00\x12-\n\x0etruncated_expr\x18\xad\x01 \x01(\x0b\x32\x12.ast.TruncatedExprH\x00\x12#\n\ttuple_val\x18\xae\x01 \x01(\x0b\x32\r.ast.TupleValH\x00\x12\x1a\n\x04udaf\x18\xaf\x01 \x01(\x0b\x32\t.ast.UdafH\x00\x12\x18\n\x03udf\x18\xb0\x01 \x01(\x0b\x32\x08.ast.UdfH\x00\x12\x1a\n\x04udtf\x18\xb1\x01 \x01(\x0b\x32\t.ast.UdtfH\x00\x12\x32\n\x11window_spec_empty\x18\xb2\x01 \x01(\x0b\x32\x14.ast.WindowSpecEmptyH\x00\x12\x37\n\x14window_spec_order_by\x18\xb3\x01 \x01(\x0b\x32\x16.ast.WindowSpecOrderByH\x00\x12?\n\x18window_spec_partition_by\x18\xb4\x01 \x01(\x0b\x32\x1a.ast.WindowSpecPartitionByH\x00\x12\x41\n\x19window_spec_range_between\x18\xb5\x01 \x01(\x0b\x32\x1b.ast.WindowSpecRangeBetweenH\x00\x12?\n\x18window_spec_rows_between\x18\xb6\x01 \x01(\x0b\x32\x1a.ast.WindowSpecRowsBetweenH\x00\x12?\n\x18write_copy_into_location\x18\xb7\x01 \x01(\x0b\x32\x1a.ast.WriteCopyIntoLocationH\x00\x12#\n\twrite_csv\x18\xb8\x01 \x01(\x0b\x32\r.ast.WriteCsvH\x00\x12\x32\n\x11write_insert_into\x18\xb9\x01 \x01(\x0b\x32\x14.ast.WriteInsertIntoH\x00\x12%\n\nwrite_json\x18\xba\x01 \x01(\x0b\x32\x0e.ast.WriteJsonH\x00\x12)\n\x0cwrite_pandas\x18\xbb\x01 \x01(\x0b\x32\x10.ast.WritePandasH\x00\x12+\n\rwrite_parquet\x18\xbc\x01 \x01(\x0b\x32\x11.ast.WriteParquetH\x00\x12%\n\nwrite_save\x18\xbd\x01 \x01(\x0b\x32\x0e.ast.WriteSaveH\x00\x12\'\n\x0bwrite_table\x18\xbe\x01 \x01(\x0b\x32\x0f.ast.WriteTableH\x00\x42\t\n\x07variant\"A\n\x14IndirectTableFnIdRef\x12\n\n\x02id\x18\x01 \x01(\x03\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\"S\n\x16IndirectTableFnNameRef\x12\x1a\n\x04name\x18\x01 \x01(\x0b\x32\x0c.ast.NameRef\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\"4\n\x08Int64Val\x12\x1d\n\x03src\x18\x01 \x01(\x0b\x32\x10.ast.SrcPosition\x12\t\n\x01v\x18\x02 \x01(\x03\"T\n\x03Leq\x12\x16\n\x03lhs\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x16\n\x03rhs\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"?\n\x07ListVal\x12\x1d\n\x03src\x18\x01 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x15\n\x02vs\x18\x02 \x03(\x0b\x32\t.ast.Expr\"S\n\x02Lt\x12\x16\n\x03lhs\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x16\n\x03rhs\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"[\n\x1cMergeDeleteWhenMatchedClause\x12\x1c\n\tcondition\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\"\xa0\x01\n\x1fMergeInsertWhenNotMatchedClause\x12\x1c\n\tcondition\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1e\n\x0binsert_keys\x18\x02 \x03(\x0b\x32\t.ast.Expr\x12 \n\rinsert_values\x18\x03 \x03(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x04 \x01(\x0b\x32\x10.ast.SrcPosition\"\x8d\x01\n\x1cMergeUpdateWhenMatchedClause\x12\x1c\n\tcondition\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x30\n\x12update_assignments\x18\x03 \x03(\x0b\x32\x14.ast.Tuple_Expr_Expr\"T\n\x03Mod\x12\x16\n\x03lhs\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x16\n\x03rhs\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"T\n\x03Mul\x12\x16\n\x03lhs\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x16\n\x03rhs\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"A\n\x07NameRef\x12\x17\n\x04name\x18\x01 \x01(\x0b\x32\t.ast.Name\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\"@\n\x03Neg\x12\x1a\n\x07operand\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\"T\n\x03Neq\x12\x16\n\x03lhs\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x16\n\x03rhs\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"@\n\x03Not\x12\x1a\n\x07operand\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\"(\n\x07NullVal\x12\x1d\n\x03src\x18\x01 \x01(\x0b\x32\x10.ast.SrcPosition\"T\n\rObjectGetItem\x12\x17\n\x04\x61rgs\x18\x01 \x03(\x0b\x32\t.ast.Expr\x12\x0b\n\x03obj\x18\x02 \x01(\x03\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"S\n\x02Or\x12\x16\n\x03lhs\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x16\n\x03rhs\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"T\n\x03Pow\x12\x16\n\x03lhs\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x16\n\x03rhs\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"X\n\rPythonDateVal\x12\x0b\n\x03\x64\x61y\x18\x01 \x01(\x03\x12\r\n\x05month\x18\x02 \x01(\x03\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x0c\n\x04year\x18\x04 \x01(\x03\"\x92\x01\n\rPythonTimeVal\x12\x0c\n\x04hour\x18\x01 \x01(\x03\x12\x13\n\x0bmicrosecond\x18\x02 \x01(\x03\x12\x0e\n\x06minute\x18\x03 \x01(\x03\x12\x0e\n\x06second\x18\x04 \x01(\x03\x12\x1d\n\x03src\x18\x05 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x1f\n\x02tz\x18\x06 \x01(\x0b\x32\x13.ast.PythonTimeZone\"\xc1\x01\n\x12PythonTimestampVal\x12\x0b\n\x03\x64\x61y\x18\x01 \x01(\x03\x12\x0c\n\x04hour\x18\x02 \x01(\x03\x12\x13\n\x0bmicrosecond\x18\x03 \x01(\x03\x12\x0e\n\x06minute\x18\x04 \x01(\x03\x12\r\n\x05month\x18\x05 \x01(\x03\x12\x0e\n\x06second\x18\x06 \x01(\x03\x12\x1d\n\x03src\x18\x07 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x1f\n\x02tz\x18\x08 \x01(\x0b\x32\x13.ast.PythonTimeZone\x12\x0c\n\x04year\x18\t \x01(\x03\"\x8a\x01\n\x05Range\x12(\n\x03\x65nd\x18\x01 \x01(\x0b\x32\x1b.google.protobuf.Int64Value\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\x12\r\n\x05start\x18\x03 \x01(\x03\x12)\n\x04step\x18\x04 \x01(\x0b\x32\x1b.google.protobuf.Int64Value\"R\n\x08ReadAvro\x12\x0c\n\x04path\x18\x01 \x01(\t\x12\x19\n\x06reader\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"Q\n\x07ReadCsv\x12\x0c\n\x04path\x18\x01 \x01(\t\x12\x19\n\x06reader\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"\x91\x02\n\x08ReadFile\x12\"\n\tread_avro\x18\x01 \x01(\x0b\x32\r.ast.ReadAvroH\x00\x12 \n\x08read_csv\x18\x02 \x01(\x0b\x32\x0c.ast.ReadCsvH\x00\x12\"\n\tread_json\x18\x03 \x01(\x0b\x32\r.ast.ReadJsonH\x00\x12\"\n\tread_load\x18\x04 \x01(\x0b\x32\r.ast.ReadLoadH\x00\x12 \n\x08read_orc\x18\x05 \x01(\x0b\x32\x0c.ast.ReadOrcH\x00\x12(\n\x0cread_parquet\x18\x06 \x01(\x0b\x32\x10.ast.ReadParquetH\x00\x12 \n\x08read_xml\x18\x07 \x01(\x0b\x32\x0c.ast.ReadXmlH\x00\x42\t\n\x07variant\"R\n\x08ReadJson\x12\x0c\n\x04path\x18\x01 \x01(\t\x12\x19\n\x06reader\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"R\n\x08ReadLoad\x12\x0c\n\x04path\x18\x01 \x01(\t\x12\x19\n\x06reader\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"Q\n\x07ReadOrc\x12\x0c\n\x04path\x18\x01 \x01(\t\x12\x19\n\x06reader\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"U\n\x0bReadParquet\x12\x0c\n\x04path\x18\x01 \x01(\t\x12\x19\n\x06reader\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"a\n\tReadTable\x12\x1a\n\x04name\x18\x01 \x01(\x0b\x32\x0c.ast.NameRef\x12\x19\n\x06reader\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"Q\n\x07ReadXml\x12\x0c\n\x04path\x18\x01 \x01(\t\x12\x19\n\x06reader\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"O\n\rRedactedConst\x12\x1f\n\x0bproxy_value\x18\x01 \x01(\x0b\x32\n.ast.Const\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\"~\n\x1dRelationalGroupedDataframeAgg\x12\x1f\n\x05\x65xprs\x18\x01 \x01(\x0b\x32\x10.ast.ExprArgList\x12\x1d\n\ngrouped_df\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"\xd4\x01\n\'RelationalGroupedDataframeApplyInPandas\x12\x1b\n\x04\x66unc\x18\x01 \x01(\x0b\x32\r.ast.Callable\x12\x1d\n\ngrouped_df\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12&\n\x06kwargs\x18\x03 \x03(\x0b\x32\x16.ast.Tuple_String_Expr\x12&\n\routput_schema\x18\x04 \x01(\x0b\x32\x0f.ast.StructType\x12\x1d\n\x03src\x18\x05 \x01(\x0b\x32\x10.ast.SrcPosition\"\x93\x01\n!RelationalGroupedDataframeBuiltin\x12\x10\n\x08\x61gg_name\x18\x01 \x01(\t\x12\x1e\n\x04\x63ols\x18\x02 \x01(\x0b\x32\x10.ast.ExprArgList\x12\x1d\n\ngrouped_df\x18\x03 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x04 \x01(\x0b\x32\x10.ast.SrcPosition\"\xbc\x01\n\x1fRelationalGroupedDataframePivot\x12\"\n\x0f\x64\x65\x66\x61ult_on_null\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\ngrouped_df\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1c\n\tpivot_col\x18\x03 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x04 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x19\n\x06values\x18\x05 \x01(\x0b\x32\t.ast.Expr\"J\n\x1dRelationalGroupedDataframeRef\x12\n\n\x02id\x18\x01 \x01(\x03\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\"\xcf\x01\n\x07Request\x12\x35\n\x14interned_value_table\x18\x01 \x01(\x0b\x32\x17.ast.InternedValueTable\x12\x17\n\x04\x62ody\x18\x02 \x03(\x0b\x32\t.ast.Stmt\x12\x1a\n\x12\x63lient_ast_version\x18\x03 \x01(\x03\x12&\n\x0f\x63lient_language\x18\x04 \x01(\x0b\x32\r.ast.Language\x12$\n\x0e\x63lient_version\x18\x05 \x01(\x0b\x32\x0c.ast.Version\x12\n\n\x02id\x18\x06 \x01(\x0c\"\\\n\x08Response\x12\x35\n\x14interned_value_table\x18\x01 \x01(\x0b\x32\x17.ast.InternedValueTable\x12\x19\n\x04\x62ody\x18\x02 \x03(\x0b\x32\x0b.ast.Result\"\xce\x01\n\x06Result\x12!\n\x0btrait_error\x18\x01 \x01(\x0b\x32\n.ast.ErrorH\x00\x12\x1e\n\x07\x65val_ok\x18\x02 \x01(\x0b\x32\x0b.ast.EvalOkH\x00\x12.\n\x0f\x65xtension_error\x18\x03 \x01(\x0b\x32\x13.ast.ExtensionErrorH\x00\x12\x46\n\x1csession_reset_required_error\x18\x04 \x01(\x0b\x32\x1e.ast.SessionResetRequiredErrorH\x00\x42\t\n\x07variant\"J\n\x03Row\x12\r\n\x05names\x18\x01 \x03(\t\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x15\n\x02vs\x18\x03 \x03(\x0b\x32\t.ast.Expr\"F\n\tSeqMapVal\x12\x1a\n\x03kvs\x18\x01 \x03(\x0b\x32\r.ast.TupleVal\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\"9\n\x19SessionResetRequiredError\x12\x0f\n\x07\x62ind_id\x18\x01 \x01(\x03\x12\x0b\n\x03uid\x18\x02 \x01(\x03\"L\n\x14SessionTableFunction\x12\x15\n\x02\x66n\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\"\x1d\n\rSfQueryResult\x12\x0c\n\x04uuid\x18\x01 \x01(\t\"\x0c\n\nShowResult\"N\n\x03Sql\x12\x19\n\x06params\x18\x01 \x03(\x0b\x32\t.ast.Expr\x12\r\n\x05query\x18\x02 \x01(\t\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"e\n\x07SqlExpr\x12.\n\x08\x64\x66_alias\x18\x01 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12\x0b\n\x03sql\x18\x02 \x01(\t\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"\xa3\x01\n\x04Stmt\x12\x19\n\x04\x62ind\x18\x01 \x01(\x0b\x32\t.ast.BindH\x00\x12\x19\n\x04\x65val\x18\x02 \x01(\x0b\x32\t.ast.EvalH\x00\x12,\n\x0e\x65xtension_stmt\x18\x03 \x01(\x0b\x32\x12.ast.ExtensionStmtH\x00\x12,\n\x0etruncated_stmt\x18\x04 \x01(\x0b\x32\x12.ast.TruncatedStmtH\x00\x42\t\n\x07variant\"\x8f\x06\n\x0fStoredProcedure\x12\x39\n\x13\x61rtifact_repository\x18\x01 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12-\n\x07\x63omment\x18\x02 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12\x12\n\nexecute_as\x18\x03 \x01(\t\x12$\n\x1c\x65xternal_access_integrations\x18\x04 \x03(\t\x12\x1b\n\x04\x66unc\x18\x05 \x01(\x0b\x32\r.ast.Callable\x12\x15\n\rif_not_exists\x18\x06 \x01(\x08\x12\x1d\n\x07imports\x18\x07 \x03(\x0b\x32\x0c.ast.NameRef\x12\"\n\x0binput_types\x18\x08 \x03(\x0b\x32\r.ast.DataType\x12\x14\n\x0cis_permanent\x18\t \x01(\x08\x12&\n\x06kwargs\x18\n \x03(\x0b\x32\x16.ast.Tuple_String_Expr\x12\x34\n\x10log_on_exception\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.BoolValue\x12\x1a\n\x04name\x18\x0c \x01(\x0b\x32\x0c.ast.NameRef\x12\x10\n\x08packages\x18\r \x03(\t\x12\x10\n\x08parallel\x18\x0e \x01(\x03\x12\x0f\n\x07replace\x18\x0f \x01(\x08\x12\x35\n\x13resource_constraint\x18\x10 \x03(\x0b\x32\x18.ast.Tuple_String_String\x12\"\n\x0breturn_type\x18\x11 \x01(\x0b\x32\r.ast.DataType\x12)\n\x07secrets\x18\x12 \x03(\x0b\x32\x18.ast.Tuple_String_String\x12\x1b\n\x13source_code_display\x18\x13 \x01(\x08\x12\x1d\n\x03src\x18\x14 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x16\n\x0estage_location\x18\x15 \x01(\t\x12\x32\n\x10statement_params\x18\x16 \x03(\x0b\x32\x18.ast.Tuple_String_String\x12\x0e\n\x06strict\x18\x17 \x01(\x08\"5\n\tStringVal\x12\x1d\n\x03src\x18\x01 \x01(\x0b\x32\x10.ast.SrcPosition\x12\t\n\x01v\x18\x02 \x01(\t\"T\n\x03Sub\x12\x16\n\x03lhs\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x16\n\x03rhs\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"\x89\x01\n\x05Table\x12!\n\x19is_temp_table_for_cleanup\x18\x01 \x01(\x08\x12\x1a\n\x04name\x18\x02 \x01(\x0b\x32\x0c.ast.NameRef\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\x12\"\n\x07variant\x18\x04 \x01(\x0b\x32\x11.ast.TableVariant\"\xbf\x01\n\x0bTableDelete\x12\r\n\x05\x62lock\x18\x01 \x01(\x08\x12\x1c\n\tcondition\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x15\n\x02\x64\x66\x18\x03 \x01(\x0b\x32\t.ast.Expr\x12\x19\n\x06source\x18\x04 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x05 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x32\n\x10statement_params\x18\x06 \x03(\x0b\x32\x18.ast.Tuple_String_String\"F\n\x0eTableDropTable\x12\x15\n\x02\x64\x66\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\"l\n\x10TableFnCallAlias\x12!\n\x07\x61liases\x18\x01 \x01(\x0b\x32\x10.ast.ExprArgList\x12\x16\n\x03lhs\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x03 \x01(\x0b\x32\x10.ast.SrcPosition\"\x94\x01\n\x0fTableFnCallOver\x12\x16\n\x03lhs\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12\"\n\x08order_by\x18\x02 \x01(\x0b\x32\x10.ast.ExprArgList\x12&\n\x0cpartition_by\x18\x03 \x01(\x0b\x32\x10.ast.ExprArgList\x12\x1d\n\x03src\x18\x04 \x01(\x0b\x32\x10.ast.SrcPosition\"\xda\x01\n\nTableMerge\x12\r\n\x05\x62lock\x18\x01 \x01(\x08\x12\x1a\n\x07\x63lauses\x18\x02 \x03(\x0b\x32\t.ast.Expr\x12\x15\n\x02\x64\x66\x18\x03 \x01(\x0b\x32\t.ast.Expr\x12\x1c\n\tjoin_expr\x18\x04 \x01(\x0b\x32\t.ast.Expr\x12\x19\n\x06source\x18\x05 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x06 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x32\n\x10statement_params\x18\x07 \x03(\x0b\x32\x18.ast.Tuple_String_String\"\x8b\x02\n\x0bTableSample\x12\x15\n\x02\x64\x66\x18\x01 \x01(\x0b\x32\t.ast.Expr\x12(\n\x03num\x18\x02 \x01(\x0b\x32\x1b.google.protobuf.Int64Value\x12:\n\x14probability_fraction\x18\x03 \x01(\x0b\x32\x1c.google.protobuf.DoubleValue\x12\x35\n\x0fsampling_method\x18\x04 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12)\n\x04seed\x18\x05 \x01(\x0b\x32\x1b.google.protobuf.Int64Value\x12\x1d\n\x03src\x18\x06 \x01(\x0b\x32\x10.ast.SrcPosition\"\xec\x01\n\x0bTableUpdate\x12+\n\x0b\x61ssignments\x18\x01 \x03(\x0b\x32\x16.ast.Tuple_String_Expr\x12\r\n\x05\x62lock\x18\x02 \x01(\x08\x12\x1c\n\tcondition\x18\x03 \x01(\x0b\x32\t.ast.Expr\x12\x15\n\x02\x64\x66\x18\x04 \x01(\x0b\x32\t.ast.Expr\x12\x19\n\x06source\x18\x05 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x06 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x32\n\x10statement_params\x18\x07 \x03(\x0b\x32\x18.ast.Tuple_String_String\"l\n\x10ToSnowparkPandas\x12\x0f\n\x07\x63olumns\x18\x01 \x03(\t\x12\x15\n\x02\x64\x66\x18\x02 \x01(\x0b\x32\t.ast.Expr\x12\x11\n\tindex_col\x18\x03 \x03(\t\x12\x1d\n\x03src\x18\x04 \x01(\x0b\x32\x10.ast.SrcPosition\"Y\n\rTruncatedExpr\x12)\n\x04self\x18\x01 \x01(\x0b\x32\x1b.google.protobuf.Int64Value\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\"!\n\rTruncatedStmt\x12\x10\n\x08\x62ind_ids\x18\x01 \x03(\x03\"@\n\x08TupleVal\x12\x1d\n\x03src\x18\x01 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x15\n\x02vs\x18\x02 \x03(\x0b\x32\t.ast.Expr\"\xc1\x05\n\x04Udaf\x12\x39\n\x13\x61rtifact_repository\x18\x01 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12-\n\x07\x63omment\x18\x02 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12$\n\x1c\x65xternal_access_integrations\x18\x03 \x03(\t\x12\x1e\n\x07handler\x18\x04 \x01(\x0b\x32\r.ast.Callable\x12\x15\n\rif_not_exists\x18\x05 \x01(\x08\x12\x11\n\timmutable\x18\x06 \x01(\x08\x12\x1d\n\x07imports\x18\x07 \x03(\x0b\x32\x0c.ast.NameRef\x12\"\n\x0binput_types\x18\x08 \x03(\x0b\x32\r.ast.DataType\x12\x14\n\x0cis_permanent\x18\t \x01(\x08\x12&\n\x06kwargs\x18\n \x03(\x0b\x32\x16.ast.Tuple_String_Expr\x12\x1a\n\x04name\x18\x0b \x01(\x0b\x32\x0c.ast.NameRef\x12\x10\n\x08packages\x18\x0c \x03(\t\x12\x10\n\x08parallel\x18\r \x01(\x03\x12\x0f\n\x07replace\x18\x0e \x01(\x08\x12\x35\n\x13resource_constraint\x18\x0f \x03(\x0b\x32\x18.ast.Tuple_String_String\x12\"\n\x0breturn_type\x18\x10 \x01(\x0b\x32\r.ast.DataType\x12)\n\x07secrets\x18\x11 \x03(\x0b\x32\x18.ast.Tuple_String_String\x12\x1d\n\x03src\x18\x12 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x34\n\x0estage_location\x18\x13 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12\x32\n\x10statement_params\x18\x14 \x03(\x0b\x32\x18.ast.Tuple_String_String\"\x91\x06\n\x03Udf\x12\x39\n\x13\x61rtifact_repository\x18\x01 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12-\n\x07\x63omment\x18\x02 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12$\n\x1c\x65xternal_access_integrations\x18\x03 \x03(\t\x12\x1b\n\x04\x66unc\x18\x04 \x01(\x0b\x32\r.ast.Callable\x12\x15\n\rif_not_exists\x18\x05 \x01(\x08\x12\x11\n\timmutable\x18\x06 \x01(\x08\x12\x1d\n\x07imports\x18\x07 \x03(\x0b\x32\x0c.ast.NameRef\x12\"\n\x0binput_types\x18\x08 \x03(\x0b\x32\r.ast.DataType\x12\x14\n\x0cis_permanent\x18\t \x01(\x08\x12&\n\x06kwargs\x18\n \x03(\x0b\x32\x16.ast.Tuple_String_Expr\x12\x33\n\x0emax_batch_size\x18\x0b \x01(\x0b\x32\x1b.google.protobuf.Int64Value\x12\x1a\n\x04name\x18\x0c \x01(\x0b\x32\x0c.ast.NameRef\x12\x10\n\x08packages\x18\r \x03(\t\x12\x10\n\x08parallel\x18\x0e \x01(\x03\x12\x0f\n\x07replace\x18\x0f \x01(\x08\x12\x35\n\x13resource_constraint\x18\x10 \x03(\x0b\x32\x18.ast.Tuple_String_String\x12\"\n\x0breturn_type\x18\x11 \x01(\x0b\x32\r.ast.DataType\x12)\n\x07secrets\x18\x12 \x03(\x0b\x32\x18.ast.Tuple_String_String\x12\x0e\n\x06secure\x18\x13 \x01(\x08\x12\x1b\n\x13source_code_display\x18\x14 \x01(\x08\x12\x1d\n\x03src\x18\x15 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x16\n\x0estage_location\x18\x16 \x01(\t\x12\x32\n\x10statement_params\x18\x17 \x03(\x0b\x32\x18.ast.Tuple_String_String\x12\x0e\n\x06strict\x18\x18 \x01(\x08\"\xc7\x05\n\x04Udtf\x12\x39\n\x13\x61rtifact_repository\x18\x01 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12-\n\x07\x63omment\x18\x02 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12$\n\x1c\x65xternal_access_integrations\x18\x03 \x03(\t\x12\x1e\n\x07handler\x18\x04 \x01(\x0b\x32\r.ast.Callable\x12\x15\n\rif_not_exists\x18\x05 \x01(\x08\x12\x11\n\timmutable\x18\x06 \x01(\x08\x12\x1d\n\x07imports\x18\x07 \x03(\x0b\x32\x0c.ast.NameRef\x12\"\n\x0binput_types\x18\x08 \x03(\x0b\x32\r.ast.DataType\x12\x14\n\x0cis_permanent\x18\t \x01(\x08\x12&\n\x06kwargs\x18\n \x03(\x0b\x32\x16.ast.Tuple_String_Expr\x12\x1a\n\x04name\x18\x0b \x01(\x0b\x32\x0c.ast.NameRef\x12&\n\routput_schema\x18\x0c \x01(\x0b\x32\x0f.ast.UdtfSchema\x12\x10\n\x08packages\x18\r \x03(\t\x12\x10\n\x08parallel\x18\x0e \x01(\x03\x12\x0f\n\x07replace\x18\x0f \x01(\x08\x12\x35\n\x13resource_constraint\x18\x10 \x03(\x0b\x32\x18.ast.Tuple_String_String\x12)\n\x07secrets\x18\x11 \x03(\x0b\x32\x18.ast.Tuple_String_String\x12\x0e\n\x06secure\x18\x12 \x01(\x08\x12\x1d\n\x03src\x18\x13 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x16\n\x0estage_location\x18\x14 \x01(\t\x12\x32\n\x10statement_params\x18\x15 \x03(\x0b\x32\x18.ast.Tuple_String_String\x12\x0e\n\x06strict\x18\x16 \x01(\x08\"F\n\x07UnaryOp\x12\x17\n\x03neg\x18\x01 \x01(\x0b\x32\x08.ast.NegH\x00\x12\x17\n\x03not\x18\x02 \x01(\x0b\x32\x08.ast.NotH\x00\x42\t\n\x07variant\"R\n\x0fWindowSpecEmpty\x12\x1d\n\x03src\x18\x01 \x01(\x0b\x32\x10.ast.SrcPosition\x12 \n\x03wnd\x18\x02 \x01(\x0b\x32\x13.ast.WindowSpecExpr\"\xc8\x02\n\x0eWindowSpecExpr\x12\x31\n\x11window_spec_empty\x18\x01 \x01(\x0b\x32\x14.ast.WindowSpecEmptyH\x00\x12\x36\n\x14window_spec_order_by\x18\x02 \x01(\x0b\x32\x16.ast.WindowSpecOrderByH\x00\x12>\n\x18window_spec_partition_by\x18\x03 \x01(\x0b\x32\x1a.ast.WindowSpecPartitionByH\x00\x12@\n\x19window_spec_range_between\x18\x04 \x01(\x0b\x32\x1b.ast.WindowSpecRangeBetweenH\x00\x12>\n\x18window_spec_rows_between\x18\x05 \x01(\x0b\x32\x1a.ast.WindowSpecRowsBetweenH\x00\x42\t\n\x07variant\"m\n\x11WindowSpecOrderBy\x12\x17\n\x04\x63ols\x18\x01 \x03(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\x12 \n\x03wnd\x18\x03 \x01(\x0b\x32\x13.ast.WindowSpecExpr\"q\n\x15WindowSpecPartitionBy\x12\x17\n\x04\x63ols\x18\x01 \x03(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\x12 \n\x03wnd\x18\x03 \x01(\x0b\x32\x13.ast.WindowSpecExpr\"\xaf\x01\n\x16WindowSpecRangeBetween\x12(\n\x03\x65nd\x18\x01 \x01(\x0b\x32\x1b.ast.WindowRelativePosition\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\x12*\n\x05start\x18\x03 \x01(\x0b\x32\x1b.ast.WindowRelativePosition\x12 \n\x03wnd\x18\x04 \x01(\x0b\x32\x13.ast.WindowSpecExpr\"\xae\x01\n\x15WindowSpecRowsBetween\x12(\n\x03\x65nd\x18\x01 \x01(\x0b\x32\x1b.ast.WindowRelativePosition\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\x12*\n\x05start\x18\x03 \x01(\x0b\x32\x1b.ast.WindowRelativePosition\x12 \n\x03wnd\x18\x04 \x01(\x0b\x32\x13.ast.WindowSpecExpr\"\xac\x03\n\x15WriteCopyIntoLocation\x12\r\n\x05\x62lock\x18\x01 \x01(\x08\x12,\n\x0c\x63opy_options\x18\x02 \x03(\x0b\x32\x16.ast.Tuple_String_Expr\x12\x36\n\x10\x66ile_format_name\x18\x03 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12\x36\n\x10\x66ile_format_type\x18\x04 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12\x35\n\x13\x66ormat_type_options\x18\x05 \x03(\x0b\x32\x18.ast.Tuple_String_String\x12\x0e\n\x06header\x18\x06 \x01(\x08\x12\x10\n\x08location\x18\x07 \x01(\t\x12\x1f\n\x0cpartition_by\x18\x08 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\t \x01(\x0b\x32\x10.ast.SrcPosition\x12\x32\n\x10statement_params\x18\n \x03(\x0b\x32\x18.ast.Tuple_String_String\x12\x19\n\x06writer\x18\x0b \x01(\x0b\x32\t.ast.Expr\"\xaf\x02\n\x08WriteCsv\x12\r\n\x05\x62lock\x18\x01 \x01(\x08\x12,\n\x0c\x63opy_options\x18\x02 \x03(\x0b\x32\x16.ast.Tuple_String_Expr\x12\x35\n\x13\x66ormat_type_options\x18\x03 \x03(\x0b\x32\x18.ast.Tuple_String_String\x12\x0e\n\x06header\x18\x04 \x01(\x08\x12\x10\n\x08location\x18\x05 \x01(\t\x12\x1f\n\x0cpartition_by\x18\x06 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x07 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x32\n\x10statement_params\x18\x08 \x03(\x0b\x32\x18.ast.Tuple_String_String\x12\x19\n\x06writer\x18\t \x01(\x0b\x32\t.ast.Expr\"\xf2\x01\n\tWriteFile\x12>\n\x18write_copy_into_location\x18\x01 \x01(\x0b\x32\x1a.ast.WriteCopyIntoLocationH\x00\x12\"\n\twrite_csv\x18\x02 \x01(\x0b\x32\r.ast.WriteCsvH\x00\x12$\n\nwrite_json\x18\x03 \x01(\x0b\x32\x0e.ast.WriteJsonH\x00\x12*\n\rwrite_parquet\x18\x04 \x01(\x0b\x32\x11.ast.WriteParquetH\x00\x12$\n\nwrite_save\x18\x05 \x01(\x0b\x32\x0e.ast.WriteSaveH\x00\x42\t\n\x07variant\"\x80\x01\n\x0fWriteInsertInto\x12\x11\n\toverwrite\x18\x01 \x01(\x08\x12\x1d\n\x03src\x18\x02 \x01(\x0b\x32\x10.ast.SrcPosition\x12 \n\ntable_name\x18\x03 \x01(\x0b\x32\x0c.ast.NameRef\x12\x19\n\x06writer\x18\x04 \x01(\x0b\x32\t.ast.Expr\"\xb0\x02\n\tWriteJson\x12\r\n\x05\x62lock\x18\x01 \x01(\x08\x12,\n\x0c\x63opy_options\x18\x02 \x03(\x0b\x32\x16.ast.Tuple_String_Expr\x12\x35\n\x13\x66ormat_type_options\x18\x03 \x03(\x0b\x32\x18.ast.Tuple_String_String\x12\x0e\n\x06header\x18\x04 \x01(\x08\x12\x10\n\x08location\x18\x05 \x01(\t\x12\x1f\n\x0cpartition_by\x18\x06 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x07 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x32\n\x10statement_params\x18\x08 \x03(\x0b\x32\x18.ast.Tuple_String_String\x12\x19\n\x06writer\x18\t \x01(\x0b\x32\t.ast.Expr\"\xf8\x02\n\x0bWritePandas\x12\x19\n\x11\x61uto_create_table\x18\x01 \x01(\x08\x12/\n\nchunk_size\x18\x02 \x01(\x0b\x32\x1b.google.protobuf.Int64Value\x12\x13\n\x0b\x63ompression\x18\x03 \x01(\t\x12\x19\n\x11\x63reate_temp_table\x18\x04 \x01(\x08\x12\x1e\n\x02\x64\x66\x18\x05 \x01(\x0b\x32\x12.ast.DataframeData\x12&\n\x06kwargs\x18\x06 \x03(\x0b\x32\x16.ast.Tuple_String_Expr\x12\x10\n\x08on_error\x18\x07 \x01(\t\x12\x11\n\toverwrite\x18\x08 \x01(\x08\x12\x10\n\x08parallel\x18\t \x01(\x03\x12\x19\n\x11quote_identifiers\x18\n \x01(\x08\x12\x1d\n\x03src\x18\x0b \x01(\x0b\x32\x10.ast.SrcPosition\x12 \n\ntable_name\x18\x0c \x01(\x0b\x32\x0c.ast.NameRef\x12\x12\n\ntable_type\x18\r \x01(\t\"\xb3\x02\n\x0cWriteParquet\x12\r\n\x05\x62lock\x18\x01 \x01(\x08\x12,\n\x0c\x63opy_options\x18\x02 \x03(\x0b\x32\x16.ast.Tuple_String_Expr\x12\x35\n\x13\x66ormat_type_options\x18\x03 \x03(\x0b\x32\x18.ast.Tuple_String_String\x12\x0e\n\x06header\x18\x04 \x01(\x08\x12\x10\n\x08location\x18\x05 \x01(\t\x12\x1f\n\x0cpartition_by\x18\x06 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x07 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x32\n\x10statement_params\x18\x08 \x03(\x0b\x32\x18.ast.Tuple_String_String\x12\x19\n\x06writer\x18\t \x01(\x0b\x32\t.ast.Expr\"\xb0\x02\n\tWriteSave\x12\r\n\x05\x62lock\x18\x01 \x01(\x08\x12,\n\x0c\x63opy_options\x18\x02 \x03(\x0b\x32\x16.ast.Tuple_String_Expr\x12\x35\n\x13\x66ormat_type_options\x18\x03 \x03(\x0b\x32\x18.ast.Tuple_String_String\x12\x0e\n\x06header\x18\x04 \x01(\x08\x12\x10\n\x08location\x18\x05 \x01(\t\x12\x1f\n\x0cpartition_by\x18\x06 \x01(\x0b\x32\t.ast.Expr\x12\x1d\n\x03src\x18\x07 \x01(\x0b\x32\x10.ast.SrcPosition\x12\x32\n\x10statement_params\x18\x08 \x03(\x0b\x32\x18.ast.Tuple_String_String\x12\x19\n\x06writer\x18\t \x01(\x0b\x32\t.ast.Expr\"\x8f\x05\n\nWriteTable\x12\r\n\x05\x62lock\x18\x01 \x01(\x08\x12\x33\n\x0f\x63hange_tracking\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.BoolValue\x12\"\n\x0f\x63lustering_keys\x18\x03 \x03(\x0b\x32\t.ast.Expr\x12\x14\n\x0c\x63olumn_order\x18\x04 \x01(\t\x12-\n\x07\x63omment\x18\x05 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12\x13\n\x0b\x63opy_grants\x18\x06 \x01(\x08\x12\x19\n\x11\x63reate_temp_table\x18\x07 \x01(\x08\x12\x38\n\x13\x64\x61ta_retention_time\x18\x08 \x01(\x0b\x32\x1b.google.protobuf.Int64Value\x12;\n\x17\x65nable_schema_evolution\x18\t \x01(\x0b\x32\x1a.google.protobuf.BoolValue\x12.\n\x0eiceberg_config\x18\n \x03(\x0b\x32\x16.ast.Tuple_String_Expr\x12<\n\x17max_data_extension_time\x18\x0b \x01(\x0b\x32\x1b.google.protobuf.Int64Value\x12\x1b\n\x04mode\x18\x0c \x01(\x0b\x32\r.ast.SaveMode\x12\x1d\n\x03src\x18\r \x01(\x0b\x32\x10.ast.SrcPosition\x12\x32\n\x10statement_params\x18\x0e \x03(\x0b\x32\x18.ast.Tuple_String_String\x12 \n\ntable_name\x18\x0f \x01(\x0b\x32\x0c.ast.NameRef\x12\x12\n\ntable_type\x18\x10 \x01(\t\x12\x19\n\x06writer\x18\x11 \x01(\x0b\x32\t.ast.Expr*8\n\x0b__Version__\x12\x18\n\x14PROTO3_REQUIRES_THIS\x10\x00\x12\x0f\n\x0bMAX_VERSION\x10\x01\x42)\n\x1c\x63om.snowflake.snowpark.protoB\tJavaProtob\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ast_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\034com.snowflake.snowpark.protoB\tJavaProto'
  _INTERNEDVALUETABLE_STRINGVALUESENTRY._options = None
  _INTERNEDVALUETABLE_STRINGVALUESENTRY._serialized_options = b'8\001'
  ___VERSION__._serialized_start=56459
  ___VERSION__._serialized_end=56515
  _INTERNEDVALUETABLE._serialized_start=51
  _INTERNEDVALUETABLE._serialized_end=190
  _INTERNEDVALUETABLE_STRINGVALUESENTRY._serialized_start=139
  _INTERNEDVALUETABLE_STRINGVALUESENTRY._serialized_end=190
  _LIST_STRING._serialized_start=192
  _LIST_STRING._serialized_end=219
  _TUPLE_EXPR_EXPR._serialized_start=221
  _TUPLE_EXPR_EXPR._serialized_end=284
  _TUPLE_EXPR_FLOAT._serialized_start=286
  _TUPLE_EXPR_FLOAT._serialized_end=339
  _TUPLE_STRING_EXPR._serialized_start=341
  _TUPLE_STRING_EXPR._serialized_end=395
  _TUPLE_STRING_LIST_STRING._serialized_start=397
  _TUPLE_STRING_LIST_STRING._serialized_end=447
  _TUPLE_STRING_STRING._serialized_start=449
  _TUPLE_STRING_STRING._serialized_end=494
  _CALLABLE._serialized_start=496
  _CALLABLE._serialized_end=567
  _COLUMNALIASFN._serialized_start=569
  _COLUMNALIASFN._serialized_end=690
  _COLUMNREF._serialized_start=692
  _COLUMNREF._serialized_end=811
  _COLUMNIDENTIFIER._serialized_start=813
  _COLUMNIDENTIFIER._serialized_end=845
  _COLUMNNAME._serialized_start=847
  _COLUMNNAME._serialized_end=873
  _DATATYPE._serialized_start=876
  _DATATYPE._serialized_end=1671
  _ARRAYTYPE._serialized_start=1673
  _ARRAYTYPE._serialized_end=1731
  _DECIMALTYPE._serialized_start=1733
  _DECIMALTYPE._serialized_end=1780
  _MAPTYPE._serialized_start=1782
  _MAPTYPE._serialized_end=1875
  _STRINGTYPE._serialized_start=1877
  _STRINGTYPE._serialized_end=1934
  _STRUCTFIELD._serialized_start=1936
  _STRUCTFIELD._serialized_end=2044
  _STRUCTTYPE._serialized_start=2046
  _STRUCTTYPE._serialized_end=2112
  _TIMESTAMPTYPE._serialized_start=2114
  _TIMESTAMPTYPE._serialized_end=2172
  _VECTORTYPE._serialized_start=2174
  _VECTORTYPE._serialized_end=2232
  _PANDASSERIESTYPE._serialized_start=2234
  _PANDASSERIESTYPE._serialized_end=2282
  _PANDASDATAFRAMETYPE._serialized_start=2284
  _PANDASDATAFRAMETYPE._serialized_end=2358
  _DATAFRAMEDATA._serialized_start=2361
  _DATAFRAMEDATA._serialized_end=2569
  _DATAFRAMEDATA_LIST._serialized_start=2571
  _DATAFRAMEDATA_LIST._serialized_end=2614
  _DATAFRAMEDATA_TUPLE._serialized_start=2616
  _DATAFRAMEDATA_TUPLE._serialized_end=2660
  _DATAFRAMEDATA_PANDAS._serialized_start=2662
  _DATAFRAMEDATA_PANDAS._serialized_end=2723
  _DATAFRAMESCHEMA._serialized_start=2726
  _DATAFRAMESCHEMA._serialized_end=2885
  _DATAFRAMESCHEMA_LIST._serialized_start=2887
  _DATAFRAMESCHEMA_LIST._serialized_end=2921
  _DATAFRAMESCHEMA_STRUCT._serialized_start=2923
  _DATAFRAMESCHEMA_STRUCT._serialized_end=2975
  _FLATTENMODE._serialized_start=2977
  _FLATTENMODE._serialized_end=3091
  _JOINTYPE._serialized_start=3094
  _JOINTYPE._serialized_end=3362
  _LANGUAGE._serialized_start=3365
  _LANGUAGE._serialized_end=3529
  _PYTHONLANGUAGE._serialized_start=3531
  _PYTHONLANGUAGE._serialized_end=3578
  _SCALALANGUAGE._serialized_start=3580
  _SCALALANGUAGE._serialized_end=3626
  _JAVALANGUAGE._serialized_start=3628
  _JAVALANGUAGE._serialized_end=3673
  _NAME._serialized_start=3675
  _NAME._serialized_end=3781
  _NAMEFLAT._serialized_start=3783
  _NAMEFLAT._serialized_end=3807
  _NAMESTRUCTURED._serialized_start=3809
  _NAMESTRUCTURED._serialized_end=3839
  _NULLORDER._serialized_start=3841
  _NULLORDER._serialized_end=3960
  _PYTHONTIMEZONE._serialized_start=3962
  _PYTHONTIMEZONE._serialized_end=4046
  _SAVEMODE._serialized_start=4049
  _SAVEMODE._serialized_end=4224
  _SRCPOSITION._serialized_start=4226
  _SRCPOSITION._serialized_end=4333
  _STAGEDPANDASDATAFRAME._serialized_start=4335
  _STAGEDPANDASDATAFRAME._serialized_end=4392
  _TABLEVARIANT._serialized_start=4394
  _TABLEVARIANT._serialized_end=4466
  _TIMESTAMPTIMEZONE._serialized_start=4469
  _TIMESTAMPTIMEZONE._serialized_end=4642
  _UDTFSCHEMA._serialized_start=4645
  _UDTFSCHEMA._serialized_end=4777
  _UDTFSCHEMA_TYPE._serialized_start=4779
  _UDTFSCHEMA_TYPE._serialized_end=4832
  _UDTFSCHEMA_NAMES._serialized_start=4834
  _UDTFSCHEMA_NAMES._serialized_end=4868
  _VERSION._serialized_start=4870
  _VERSION._serialized_end=4939
  _WINDOWRELATIVEPOSITION._serialized_start=4942
  _WINDOWRELATIVEPOSITION._serialized_end=5224
  _WINDOWRELATIVEPOSITION_POSITION._serialized_start=5226
  _WINDOWRELATIVEPOSITION_POSITION._serialized_end=5281
  _ABSTRACTEXTENSION._serialized_start=5284
  _ABSTRACTEXTENSION._serialized_end=5519
  _ADD._serialized_start=5521
  _ADD._serialized_end=5605
  _AND._serialized_start=5607
  _AND._serialized_end=5691
  _APPLYEXPR._serialized_start=5694
  _APPLYEXPR._serialized_end=5832
  _BIGDECIMALVAL._serialized_start=5835
  _BIGDECIMALVAL._serialized_end=5967
  _BIGINTVAL._serialized_start=5969
  _BIGINTVAL._serialized_end=6043
  _BINOP._serialized_start=6046
  _BINOP._serialized_end=6500
  _BINARYVAL._serialized_start=6502
  _BINARYVAL._serialized_end=6555
  _BIND._serialized_start=6557
  _BIND._serialized_end=6673
  _BITAND._serialized_start=6675
  _BITAND._serialized_end=6762
  _BITOR._serialized_start=6764
  _BITOR._serialized_end=6850
  _BITXOR._serialized_start=6852
  _BITXOR._serialized_end=6939
  _BOOLVAL._serialized_start=6941
  _BOOLVAL._serialized_end=6992
  _BUILTINFN._serialized_start=6994
  _BUILTINFN._serialized_end=7064
  _CALLTABLEFUNCTIONEXPR._serialized_start=7066
  _CALLTABLEFUNCTIONEXPR._serialized_end=7148
  _COLUMNALIAS._serialized_start=7150
  _COLUMNALIAS._serialized_end=7264
  _COLUMNAPPLY_INT._serialized_start=7266
  _COLUMNAPPLY_INT._serialized_end=7351
  _COLUMNAPPLY_STRING._serialized_start=7353
  _COLUMNAPPLY_STRING._serialized_end=7443
  _COLUMNASC._serialized_start=7445
  _COLUMNASC._serialized_end=7547
  _COLUMNBETWEEN._serialized_start=7550
  _COLUMNBETWEEN._serialized_end=7684
  _COLUMNCASEEXPR._serialized_start=7686
  _COLUMNCASEEXPR._serialized_end=7775
  _COLUMNCASEEXPRCLAUSE._serialized_start=7777
  _COLUMNCASEEXPRCLAUSE._serialized_end=7886
  _COLUMNCAST._serialized_start=7888
  _COLUMNCAST._serialized_end=7982
  _COLUMNDESC._serialized_start=7984
  _COLUMNDESC._serialized_end=8087
  _COLUMNEQUALNAN._serialized_start=8089
  _COLUMNEQUALNAN._serialized_end=8160
  _COLUMNEQUALNULL._serialized_start=8162
  _COLUMNEQUALNULL._serialized_end=8258
  _COLUMNFN._serialized_start=8261
  _COLUMNFN._serialized_end=9322
  _COLUMNIN._serialized_start=9324
  _COLUMNIN._serialized_end=9416
  _COLUMNISNOTNULL._serialized_start=9418
  _COLUMNISNOTNULL._serialized_end=9490
  _COLUMNISNULL._serialized_start=9492
  _COLUMNISNULL._serialized_end=9561
  _COLUMNOVER._serialized_start=9563
  _COLUMNOVER._serialized_end=9672
  _COLUMNREGEXP._serialized_start=9675
  _COLUMNREGEXP._serialized_end=9803
  _COLUMNSTRINGCOLLATE._serialized_start=9805
  _COLUMNSTRINGCOLLATE._serialized_end=9916
  _COLUMNSTRINGCONTAINS._serialized_start=9918
  _COLUMNSTRINGCONTAINS._serialized_end=10023
  _COLUMNSTRINGENDSWITH._serialized_start=10025
  _COLUMNSTRINGENDSWITH._serialized_end=10129
  _COLUMNSTRINGLIKE._serialized_start=10131
  _COLUMNSTRINGLIKE._serialized_end=10232
  _COLUMNSTRINGSTARTSWITH._serialized_start=10234
  _COLUMNSTRINGSTARTSWITH._serialized_end=10340
  _COLUMNSTRINGSUBSTR._serialized_start=10342
  _COLUMNSTRINGSUBSTR._serialized_end=10465
  _COLUMNTRYCAST._serialized_start=10467
  _COLUMNTRYCAST._serialized_end=10564
  _COLUMNWITHINGROUP._serialized_start=10566
  _COLUMNWITHINGROUP._serialized_end=10672
  _CONST._serialized_start=10675
  _CONST._serialized_end=11238
  _CREATEDATAFRAME._serialized_start=11240
  _CREATEDATAFRAME._serialized_end=11360
  _DATAFRAMEAGG._serialized_start=11362
  _DATAFRAMEAGG._serialized_end=11463
  _DATAFRAMEALIAS._serialized_start=11465
  _DATAFRAMEALIAS._serialized_end=11549
  _DATAFRAMEANALYTICSCOMPUTELAG._serialized_start=11552
  _DATAFRAMEANALYTICSCOMPUTELAG._serialized_end=11740
  _DATAFRAMEANALYTICSCOMPUTELEAD._serialized_start=11743
  _DATAFRAMEANALYTICSCOMPUTELEAD._serialized_end=11933
  _DATAFRAMEANALYTICSCUMULATIVEAGG._serialized_start=11936
  _DATAFRAMEANALYTICSCUMULATIVEAGG._serialized_end=12153
  _DATAFRAMEANALYTICSMOVINGAGG._serialized_start=12156
  _DATAFRAMEANALYTICSMOVINGAGG._serialized_end=12371
  _DATAFRAMEANALYTICSTIMESERIESAGG._serialized_start=12374
  _DATAFRAMEANALYTICSTIMESERIESAGG._serialized_end=12614
  _DATAFRAMECACHERESULT._serialized_start=12617
  _DATAFRAMECACHERESULT._serialized_end=12780
  _DATAFRAMECOL._serialized_start=12782
  _DATAFRAMECOL._serialized_end=12868
  _DATAFRAMECOLLECT._serialized_start=12871
  _DATAFRAMECOLLECT._serialized_end=13077
  _DATAFRAMECOPYINTOTABLE._serialized_start=13080
  _DATAFRAMECOPYINTOTABLE._serialized_end=13568
  _DATAFRAMECOUNT._serialized_start=13571
  _DATAFRAMECOUNT._serialized_end=13708
  _DATAFRAMECREATEORREPLACEDYNAMICTABLE._serialized_start=13711
  _DATAFRAMECREATEORREPLACEDYNAMICTABLE._serialized_end=14271
  _DATAFRAMECREATEORREPLACEVIEW._serialized_start=14274
  _DATAFRAMECREATEORREPLACEVIEW._serialized_end=14502
  _DATAFRAMECROSSJOIN._serialized_start=14505
  _DATAFRAMECROSSJOIN._serialized_end=14698
  _DATAFRAMECUBE._serialized_start=14700
  _DATAFRAMECUBE._serialized_end=14801
  _DATAFRAMEDESCRIBE._serialized_start=14804
  _DATAFRAMEDESCRIBE._serialized_end=14945
  _DATAFRAMEDISTINCT._serialized_start=14947
  _DATAFRAMEDISTINCT._serialized_end=15020
  _DATAFRAMEDROP._serialized_start=15022
  _DATAFRAMEDROP._serialized_end=15123
  _DATAFRAMEDROPDUPLICATES._serialized_start=15125
  _DATAFRAMEDROPDUPLICATES._serialized_end=15236
  _DATAFRAMEEXCEPT._serialized_start=15238
  _DATAFRAMEEXCEPT._serialized_end=15335
  _DATAFRAMEFILTER._serialized_start=15337
  _DATAFRAMEFILTER._serialized_end=15438
  _DATAFRAMEFIRST._serialized_start=15441
  _DATAFRAMEFIRST._serialized_end=15591
  _DATAFRAMEFLATTEN._serialized_start=15594
  _DATAFRAMEFLATTEN._serialized_end=15802
  _DATAFRAMEGROUPBY._serialized_start=15804
  _DATAFRAMEGROUPBY._serialized_end=15908
  _DATAFRAMEGROUPBYGROUPINGSETS._serialized_start=15910
  _DATAFRAMEGROUPBYGROUPINGSETS._serialized_end=16035
  _DATAFRAMEINTERSECT._serialized_start=16037
  _DATAFRAMEINTERSECT._serialized_end=16137
  _DATAFRAMEJOIN._serialized_start=16140
  _DATAFRAMEJOIN._serialized_end=16428
  _DATAFRAMEJOINTABLEFUNCTION._serialized_start=16430
  _DATAFRAMEJOINTABLEFUNCTION._serialized_end=16536
  _DATAFRAMELIMIT._serialized_start=16538
  _DATAFRAMELIMIT._serialized_end=16635
  _DATAFRAMENADROP_PYTHON._serialized_start=16638
  _DATAFRAMENADROP_PYTHON._serialized_end=16808
  _DATAFRAMENADROP_SCALA._serialized_start=16810
  _DATAFRAMENADROP_SCALA._serialized_end=16932
  _DATAFRAMENAFILL._serialized_start=16935
  _DATAFRAMENAFILL._serialized_end=17134
  _DATAFRAMENAREPLACE._serialized_start=17137
  _DATAFRAMENAREPLACE._serialized_end=17443
  _DATAFRAMENATURALJOIN._serialized_start=17446
  _DATAFRAMENATURALJOIN._serialized_end=17581
  _DATAFRAMEPIVOT._serialized_start=17584
  _DATAFRAMEPIVOT._serialized_end=17747
  _DATAFRAMERANDOMSPLIT._serialized_start=17750
  _DATAFRAMERANDOMSPLIT._serialized_end=17938
  _DATAFRAMEREADER._serialized_start=17941
  _DATAFRAMEREADER._serialized_end=18153
  _DATAFRAMEREF._serialized_start=18155
  _DATAFRAMEREF._serialized_end=18212
  _DATAFRAMERENAME._serialized_start=18215
  _DATAFRAMERENAME._serialized_end=18370
  _DATAFRAMEROLLUP._serialized_start=18372
  _DATAFRAMEROLLUP._serialized_end=18475
  _DATAFRAMESAMPLE._serialized_start=18478
  _DATAFRAMESAMPLE._serialized_end=18694
  _DATAFRAMESELECT._serialized_start=18696
  _DATAFRAMESELECT._serialized_end=18821
  _DATAFRAMESHOW._serialized_start=18823
  _DATAFRAMESHOW._serialized_end=18903
  _DATAFRAMESORT._serialized_start=18906
  _DATAFRAMESORT._serialized_end=19037
  _DATAFRAMESTATAPPROXQUANTILE._serialized_start=19040
  _DATAFRAMESTATAPPROXQUANTILE._serialized_end=19227
  _DATAFRAMESTATCORR._serialized_start=19230
  _DATAFRAMESTATCORR._serialized_end=19405
  _DATAFRAMESTATCOV._serialized_start=19408
  _DATAFRAMESTATCOV._serialized_end=19582
  _DATAFRAMESTATCROSSTAB._serialized_start=19585
  _DATAFRAMESTATCROSSTAB._serialized_end=19764
  _DATAFRAMESTATSAMPLEBY._serialized_start=19767
  _DATAFRAMESTATSAMPLEBY._serialized_end=19910
  _DATAFRAMETODF._serialized_start=19912
  _DATAFRAMETODF._serialized_end=20018
  _DATAFRAMETOLOCALITERATOR._serialized_start=20021
  _DATAFRAMETOLOCALITERATOR._serialized_end=20192
  _DATAFRAMETOPANDAS._serialized_start=20195
  _DATAFRAMETOPANDAS._serialized_end=20335
  _DATAFRAMETOPANDASBATCHES._serialized_start=20338
  _DATAFRAMETOPANDASBATCHES._serialized_end=20485
  _DATAFRAMEUNION._serialized_start=20488
  _DATAFRAMEUNION._serialized_end=20645
  _DATAFRAMEUNPIVOT._serialized_start=20648
  _DATAFRAMEUNPIVOT._serialized_end=20818
  _DATAFRAMEWITHCOLUMN._serialized_start=20820
  _DATAFRAMEWITHCOLUMN._serialized_end=20937
  _DATAFRAMEWITHCOLUMNRENAMED._serialized_start=20939
  _DATAFRAMEWITHCOLUMNRENAMED._serialized_end=21063
  _DATAFRAMEWITHCOLUMNS._serialized_start=21065
  _DATAFRAMEWITHCOLUMNS._serialized_end=21187
  _DATAFRAMEWRITER._serialized_start=21190
  _DATAFRAMEWRITER._serialized_end=21415
  _DATATYPEVAL._serialized_start=21417
  _DATATYPEVAL._serialized_end=21494
  _DIV._serialized_start=21496
  _DIV._serialized_end=21580
  _EQ._serialized_start=21582
  _EQ._serialized_end=21665
  _ERROR._serialized_start=21668
  _ERROR._serialized_end=21806
  _EVAL._serialized_start=21808
  _EVAL._serialized_end=21831
  _EVALOK._serialized_start=21833
  _EVALOK._serialized_end=21902
  _EVALRESULT._serialized_start=21905
  _EVALRESULT._serialized_end=22654
  _EXPR._serialized_start=22657
  _EXPR._serialized_end=31500
  _EXPRARGLIST._serialized_start=31502
  _EXPRARGLIST._serialized_end=31558
  _EXTENSIONERROR._serialized_start=31560
  _EXTENSIONERROR._serialized_end=31659
  _EXTENSIONEVALRESULT._serialized_start=31661
  _EXTENSIONEVALRESULT._serialized_end=31735
  _EXTENSIONEXPR._serialized_start=31737
  _EXTENSIONEXPR._serialized_end=31780
  _EXTENSIONSTMT._serialized_start=31782
  _EXTENSIONSTMT._serialized_end=31850
  _FLATTEN._serialized_start=31853
  _FLATTEN._serialized_end=32029
  _FLOAT64VAL._serialized_start=32031
  _FLOAT64VAL._serialized_end=32085
  _FNIDREFEXPR._serialized_start=32087
  _FNIDREFEXPR._serialized_end=32204
  _FNNAMEREFEXPR._serialized_start=32207
  _FNNAMEREFEXPR._serialized_end=32531
  _FNREF._serialized_start=32533
  _FNREF._serialized_end=32583
  _GENERATOR._serialized_start=32585
  _GENERATOR._serialized_end=32709
  _GEQ._serialized_start=32711
  _GEQ._serialized_end=32795
  _GROUPINGSETS._serialized_start=32797
  _GROUPINGSETS._serialized_end=32874
  _GT._serialized_start=32876
  _GT._serialized_end=32959
  _HASSRCPOSITION._serialized_start=32962
  _HASSRCPOSITION._serialized_end=42309
  _INDIRECTTABLEFNIDREF._serialized_start=42311
  _INDIRECTTABLEFNIDREF._serialized_end=42376
  _INDIRECTTABLEFNNAMEREF._serialized_start=42378
  _INDIRECTTABLEFNNAMEREF._serialized_end=42461
  _INT64VAL._serialized_start=42463
  _INT64VAL._serialized_end=42515
  _LEQ._serialized_start=42517
  _LEQ._serialized_end=42601
  _LISTVAL._serialized_start=42603
  _LISTVAL._serialized_end=42666
  _LT._serialized_start=42668
  _LT._serialized_end=42751
  _MERGEDELETEWHENMATCHEDCLAUSE._serialized_start=42753
  _MERGEDELETEWHENMATCHEDCLAUSE._serialized_end=42844
  _MERGEINSERTWHENNOTMATCHEDCLAUSE._serialized_start=42847
  _MERGEINSERTWHENNOTMATCHEDCLAUSE._serialized_end=43007
  _MERGEUPDATEWHENMATCHEDCLAUSE._serialized_start=43010
  _MERGEUPDATEWHENMATCHEDCLAUSE._serialized_end=43151
  _MOD._serialized_start=43153
  _MOD._serialized_end=43237
  _MUL._serialized_start=43239
  _MUL._serialized_end=43323
  _NAMEREF._serialized_start=43325
  _NAMEREF._serialized_end=43390
  _NEG._serialized_start=43392
  _NEG._serialized_end=43456
  _NEQ._serialized_start=43458
  _NEQ._serialized_end=43542
  _NOT._serialized_start=43544
  _NOT._serialized_end=43608
  _NULLVAL._serialized_start=43610
  _NULLVAL._serialized_end=43650
  _OBJECTGETITEM._serialized_start=43652
  _OBJECTGETITEM._serialized_end=43736
  _OR._serialized_start=43738
  _OR._serialized_end=43821
  _POW._serialized_start=43823
  _POW._serialized_end=43907
  _PYTHONDATEVAL._serialized_start=43909
  _PYTHONDATEVAL._serialized_end=43997
  _PYTHONTIMEVAL._serialized_start=44000
  _PYTHONTIMEVAL._serialized_end=44146
  _PYTHONTIMESTAMPVAL._serialized_start=44149
  _PYTHONTIMESTAMPVAL._serialized_end=44342
  _RANGE._serialized_start=44345
  _RANGE._serialized_end=44483
  _READAVRO._serialized_start=44485
  _READAVRO._serialized_end=44567
  _READCSV._serialized_start=44569
  _READCSV._serialized_end=44650
  _READFILE._serialized_start=44653
  _READFILE._serialized_end=44926
  _READJSON._serialized_start=44928
  _READJSON._serialized_end=45010
  _READLOAD._serialized_start=45012
  _READLOAD._serialized_end=45094
  _READORC._serialized_start=45096
  _READORC._serialized_end=45177
  _READPARQUET._serialized_start=45179
  _READPARQUET._serialized_end=45264
  _READTABLE._serialized_start=45266
  _READTABLE._serialized_end=45363
  _READXML._serialized_start=45365
  _READXML._serialized_end=45446
  _REDACTEDCONST._serialized_start=45448
  _REDACTEDCONST._serialized_end=45527
  _RELATIONALGROUPEDDATAFRAMEAGG._serialized_start=45529
  _RELATIONALGROUPEDDATAFRAMEAGG._serialized_end=45655
  _RELATIONALGROUPEDDATAFRAMEAPPLYINPANDAS._serialized_start=45658
  _RELATIONALGROUPEDDATAFRAMEAPPLYINPANDAS._serialized_end=45870
  _RELATIONALGROUPEDDATAFRAMEBUILTIN._serialized_start=45873
  _RELATIONALGROUPEDDATAFRAMEBUILTIN._serialized_end=46020
  _RELATIONALGROUPEDDATAFRAMEPIVOT._serialized_start=46023
  _RELATIONALGROUPEDDATAFRAMEPIVOT._serialized_end=46211
  _RELATIONALGROUPEDDATAFRAMEREF._serialized_start=46213
  _RELATIONALGROUPEDDATAFRAMEREF._serialized_end=46287
  _REQUEST._serialized_start=46290
  _REQUEST._serialized_end=46497
  _RESPONSE._serialized_start=46499
  _RESPONSE._serialized_end=46591
  _RESULT._serialized_start=46594
  _RESULT._serialized_end=46800
  _ROW._serialized_start=46802
  _ROW._serialized_end=46876
  _SEQMAPVAL._serialized_start=46878
  _SEQMAPVAL._serialized_end=46948
  _SESSIONRESETREQUIREDERROR._serialized_start=46950
  _SESSIONRESETREQUIREDERROR._serialized_end=47007
  _SESSIONTABLEFUNCTION._serialized_start=47009
  _SESSIONTABLEFUNCTION._serialized_end=47085
  _SFQUERYRESULT._serialized_start=47087
  _SFQUERYRESULT._serialized_end=47116
  _SHOWRESULT._serialized_start=47118
  _SHOWRESULT._serialized_end=47130
  _SQL._serialized_start=47132
  _SQL._serialized_end=47210
  _SQLEXPR._serialized_start=47212
  _SQLEXPR._serialized_end=47313
  _STMT._serialized_start=47316
  _STMT._serialized_end=47479
  _STOREDPROCEDURE._serialized_start=47482
  _STOREDPROCEDURE._serialized_end=48265
  _STRINGVAL._serialized_start=48267
  _STRINGVAL._serialized_end=48320
  _SUB._serialized_start=48322
  _SUB._serialized_end=48406
  _TABLE._serialized_start=48409
  _TABLE._serialized_end=48546
  _TABLEDELETE._serialized_start=48549
  _TABLEDELETE._serialized_end=48740
  _TABLEDROPTABLE._serialized_start=48742
  _TABLEDROPTABLE._serialized_end=48812
  _TABLEFNCALLALIAS._serialized_start=48814
  _TABLEFNCALLALIAS._serialized_end=48922
  _TABLEFNCALLOVER._serialized_start=48925
  _TABLEFNCALLOVER._serialized_end=49073
  _TABLEMERGE._serialized_start=49076
  _TABLEMERGE._serialized_end=49294
  _TABLESAMPLE._serialized_start=49297
  _TABLESAMPLE._serialized_end=49564
  _TABLEUPDATE._serialized_start=49567
  _TABLEUPDATE._serialized_end=49803
  _TOSNOWPARKPANDAS._serialized_start=49805
  _TOSNOWPARKPANDAS._serialized_end=49913
  _TRUNCATEDEXPR._serialized_start=49915
  _TRUNCATEDEXPR._serialized_end=50004
  _TRUNCATEDSTMT._serialized_start=50006
  _TRUNCATEDSTMT._serialized_end=50039
  _TUPLEVAL._serialized_start=50041
  _TUPLEVAL._serialized_end=50105
  _UDAF._serialized_start=50108
  _UDAF._serialized_end=50813
  _UDF._serialized_start=50816
  _UDF._serialized_end=51601
  _UDTF._serialized_start=51604
  _UDTF._serialized_end=52315
  _UNARYOP._serialized_start=52317
  _UNARYOP._serialized_end=52387
  _WINDOWSPECEMPTY._serialized_start=52389
  _WINDOWSPECEMPTY._serialized_end=52471
  _WINDOWSPECEXPR._serialized_start=52474
  _WINDOWSPECEXPR._serialized_end=52802
  _WINDOWSPECORDERBY._serialized_start=52804
  _WINDOWSPECORDERBY._serialized_end=52913
  _WINDOWSPECPARTITIONBY._serialized_start=52915
  _WINDOWSPECPARTITIONBY._serialized_end=53028
  _WINDOWSPECRANGEBETWEEN._serialized_start=53031
  _WINDOWSPECRANGEBETWEEN._serialized_end=53206
  _WINDOWSPECROWSBETWEEN._serialized_start=53209
  _WINDOWSPECROWSBETWEEN._serialized_end=53383
  _WRITECOPYINTOLOCATION._serialized_start=53386
  _WRITECOPYINTOLOCATION._serialized_end=53814
  _WRITECSV._serialized_start=53817
  _WRITECSV._serialized_end=54120
  _WRITEFILE._serialized_start=54123
  _WRITEFILE._serialized_end=54365
  _WRITEINSERTINTO._serialized_start=54368
  _WRITEINSERTINTO._serialized_end=54496
  _WRITEJSON._serialized_start=54499
  _WRITEJSON._serialized_end=54803
  _WRITEPANDAS._serialized_start=54806
  _WRITEPANDAS._serialized_end=55182
  _WRITEPARQUET._serialized_start=55185
  _WRITEPARQUET._serialized_end=55492
  _WRITESAVE._serialized_start=55495
  _WRITESAVE._serialized_end=55799
  _WRITETABLE._serialized_start=55802
  _WRITETABLE._serialized_end=56457
# @@protoc_insertion_point(module_scope)
