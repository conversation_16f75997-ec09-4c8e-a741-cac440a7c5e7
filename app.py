import streamlit as st
import pandas as pd
import re
import pyperclip

# 设置页面配置
st.set_page_config(
    page_title="密保卡查询",
    page_icon="🔐",
    layout="centered",
    initial_sidebar_state="collapsed"
)

def load_card_data():
    """加载密保卡数据"""
    try:
        # 读取CSV文件
        df = pd.read_csv('card.csv', index_col=0)
        return df
    except FileNotFoundError:
        st.error("找不到card.csv文件，请确保文件在当前目录下")
        return None
    except Exception as e:
        st.error(f"读取文件时出错: {e}")
        return None

def parse_code(code):
    """解析用户输入的代码，如a3b4"""
    # 移除空格并转换为大写
    code = code.strip().upper()

    # 使用正则表达式匹配格式：字母+数字+字母+数字
    pattern = r'^([A-H])([1-8])([A-H])([1-8])$'
    match = re.match(pattern, code)

    if not match:
        return None

    row1, col1, row2, col2 = match.groups()
    return [(row1, int(col1)), (row2, int(col2))]

def get_values(df, positions):
    """根据位置获取对应的数值"""
    values = []
    for row, col in positions:
        try:
            value = df.loc[row, str(col)]
            values.append(str(value))
        except KeyError:
            return None
    return ''.join(values)

def main():
    st.title("🔐 密保卡查询系统")

    # 加载数据
    df = load_card_data()
    if df is None:
        return

    # 初始化session state
    if 'result' not in st.session_state:
        st.session_state.result = ""
    if 'last_input' not in st.session_state:
        st.session_state.last_input = ""

    # 创建主要输入区域
    st.markdown("### 输入4位代码查询密保码")

    # 输入区域
    st.markdown("### 🔍 输入查询代码")
    user_input = st.text_input(
        "输入代码：",
        placeholder="例如：a3b4",
        help="格式：字母(A-H) + 数字(1-8) + 字母(A-H) + 数字(1-8)",
        key="input_code"
    )

    # 处理输入和显示结果
    if user_input and user_input != st.session_state.last_input:
        st.session_state.last_input = user_input

        # 解析代码
        positions = parse_code(user_input)
        if positions is None:
            st.session_state.result = "❌ 格式错误"
        else:
            # 获取对应值
            result = get_values(df, positions)
            if result is None:
                st.session_state.result = "❌ 查询失败"
            else:
                st.session_state.result = result

                # 自动复制到剪贴板
                try:
                    pyperclip.copy(result)
                    st.session_state.auto_copied = True
                except:
                    st.session_state.auto_copied = False

    # 显示结果（在输入框下方）
    if st.session_state.result:
        if st.session_state.result.startswith("❌"):
            st.error(st.session_state.result)
        else:
            # 简化的结果显示 - 紧凑设计
            st.markdown(f"""
            <div style="
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                padding: 1rem;
                border-radius: 10px;
                text-align: center;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                margin: 1rem 0;
                border: 1px solid #FFD700;
            ">
                <h1 style="
                    color: #00FF7F;
                    margin: 0;
                    font-size: 2.5rem;
                    font-weight: bold;
                    letter-spacing: 0.2rem;
                    user-select: all;
                    font-family: 'Courier New', monospace;
                    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
                ">{st.session_state.result}</h1>
                <p style="color: #FFD700; margin: 0.5rem 0 0 0; font-size: 0.9rem;">
                    {'🎉 已自动复制到剪贴板' if st.session_state.get('auto_copied', False) else '👆 点击数字可选中复制'}
                </p>
            </div>
            """, unsafe_allow_html=True)

    # 简化的使用说明
    st.markdown("---")
    st.markdown("""
    **💡 使用提示：** 输入格式如 `a3b4`（字母A-H + 数字1-8 + 字母A-H + 数字1-8），按回车查询

    **📝 示例：** `a3b4` → `611286` | `A1B2` → `781329` | 不区分大小写
    """)

if __name__ == "__main__":
    main()
