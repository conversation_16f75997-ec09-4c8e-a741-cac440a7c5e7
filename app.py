import streamlit as st
import pandas as pd
import re
import pyperclip

# 设置页面配置
st.set_page_config(
    page_title="密保卡查询",
    page_icon="🔐",
    layout="centered",
    initial_sidebar_state="collapsed"
)

def load_card_data():
    """加载密保卡数据"""
    try:
        # 读取CSV文件
        df = pd.read_csv('card.csv', index_col=0)
        return df
    except FileNotFoundError:
        st.error("找不到card.csv文件，请确保文件在当前目录下")
        return None
    except Exception as e:
        st.error(f"读取文件时出错: {e}")
        return None

def parse_code(code):
    """解析用户输入的代码，如a3b4"""
    # 移除空格并转换为大写
    code = code.strip().upper()

    # 使用正则表达式匹配格式：字母+数字+字母+数字
    pattern = r'^([A-H])([1-8])([A-H])([1-8])$'
    match = re.match(pattern, code)

    if not match:
        return None

    row1, col1, row2, col2 = match.groups()
    return [(row1, int(col1)), (row2, int(col2))]

def get_values(df, positions):
    """根据位置获取对应的数值"""
    values = []
    for row, col in positions:
        try:
            value = df.loc[row, str(col)]
            values.append(str(value))
        except KeyError:
            return None
    return ''.join(values)

def main():
    st.title("🔐 密保卡查询系统")

    # 加载数据
    df = load_card_data()
    if df is None:
        return

    # 初始化session state
    if 'result' not in st.session_state:
        st.session_state.result = ""
    if 'last_input' not in st.session_state:
        st.session_state.last_input = ""

    # 创建主要输入区域
    st.markdown("### 输入4位代码查询密保码")

    # 创建输入和结果显示区域 - 调整比例让结果更突出
    col1, col2 = st.columns([1, 2])

    with col1:
        user_input = st.text_input(
            "输入代码：",
            placeholder="例如：a3b4",
            help="格式：字母(A-H) + 数字(1-8) + 字母(A-H) + 数字(1-8)",
            key="input_code"
        )

    with col2:
        # 结果显示区域
        if user_input and user_input != st.session_state.last_input:
            st.session_state.last_input = user_input

            # 解析代码
            positions = parse_code(user_input)
            if positions is None:
                st.session_state.result = "❌ 格式错误"
            else:
                # 获取对应值
                result = get_values(df, positions)
                if result is None:
                    st.session_state.result = "❌ 查询失败"
                else:
                    st.session_state.result = result

                    # 自动复制到剪贴板
                    try:
                        pyperclip.copy(result)
                        st.session_state.auto_copied = True
                    except:
                        st.session_state.auto_copied = False

        # 显示结果
        if st.session_state.result:
            if st.session_state.result.startswith("❌"):
                st.error(st.session_state.result)
            else:
                # 美化的结果显示 - 更大更突出
                st.markdown(f"""
                <div style="
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    padding: 2rem;
                    border-radius: 15px;
                    text-align: center;
                    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
                    margin: 1rem 0;
                    border: 2px solid #FFD700;
                ">
                    <h2 style="color: white; margin: 0; font-size: 1.2rem; margin-bottom: 1rem;">🔐 密保码</h2>
                    <div style="
                        background: rgba(255,255,255,0.1);
                        padding: 1rem;
                        border-radius: 10px;
                        margin: 1rem 0;
                        border: 1px dashed #FFD700;
                    ">
                        <h1 style="
                            color: #00FF7F;
                            margin: 0;
                            font-size: 3.5rem;
                            font-weight: bold;
                            letter-spacing: 0.3rem;
                            user-select: all;
                            font-family: 'Courier New', monospace;
                            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
                        ">{st.session_state.result}</h1>
                    </div>
                    <p style="color: #FFD700; margin: 0; font-size: 1rem;">
                        {'🎉 已自动复制到剪贴板！' if st.session_state.get('auto_copied', False) else '👆 点击数字选中复制 | 或使用下方按钮'}
                    </p>
                </div>
                """, unsafe_allow_html=True)

                # 添加复制按钮和文本框
                copy_col1, copy_col2 = st.columns([1, 1])
                with copy_col1:
                    # 提供一个文本框方便复制
                    st.text_input("复制此内容:", value=st.session_state.result, key="copy_text", help="全选此文本框内容进行复制")
                with copy_col2:
                    if st.button("📋 复制到剪贴板", key="copy_btn", type="primary"):
                        try:
                            pyperclip.copy(st.session_state.result)
                            st.success("✅ 已复制到剪贴板！")
                            st.balloons()
                        except Exception as e:
                            st.error(f"复制失败: {str(e)}")
                            st.info("💡 请手动选中上方文本框中的内容进行复制")

                # 显示详细信息（可折叠）
                if user_input:
                    positions = parse_code(user_input)
                    if positions:
                        with st.expander("📋 查看详情", expanded=False):
                            detail_col1, detail_col2 = st.columns(2)
                            with detail_col1:
                                st.info(f"**{positions[0][0]}{positions[0][1]}** → {df.loc[positions[0][0], str(positions[0][1])]}")
                            with detail_col2:
                                st.info(f"**{positions[1][0]}{positions[1][1]}** → {df.loc[positions[1][0], str(positions[1][1])]}")

    # 简化的使用说明
    st.markdown("---")
    st.markdown("""
    **💡 使用提示：** 输入格式如 `a3b4`（字母A-H + 数字1-8 + 字母A-H + 数字1-8），按回车查询

    **📝 示例：** `a3b4` → `611286` | `A1B2` → `781329` | 不区分大小写
    """)

if __name__ == "__main__":
    main()
