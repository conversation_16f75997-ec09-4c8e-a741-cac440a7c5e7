import streamlit as st
import pandas as pd
import re

def load_card_data():
    """加载密保卡数据"""
    try:
        # 读取CSV文件
        df = pd.read_csv('card.csv', index_col=0)
        return df
    except FileNotFoundError:
        st.error("找不到card.csv文件，请确保文件在当前目录下")
        return None
    except Exception as e:
        st.error(f"读取文件时出错: {e}")
        return None

def parse_code(code):
    """解析用户输入的代码，如a3b4"""
    # 移除空格并转换为大写
    code = code.strip().upper()
    
    # 使用正则表达式匹配格式：字母+数字+字母+数字
    pattern = r'^([A-H])([1-8])([A-H])([1-8])$'
    match = re.match(pattern, code)
    
    if not match:
        return None
    
    row1, col1, row2, col2 = match.groups()
    return [(row1, int(col1)), (row2, int(col2))]

def get_values(df, positions):
    """根据位置获取对应的数值"""
    values = []
    for row, col in positions:
        try:
            value = df.loc[row, str(col)]
            values.append(str(value))
        except KeyError:
            return None
    return ''.join(values)

def main():
    st.title("🔐 密保卡查询系统")
    st.markdown("---")
    
    # 加载数据
    df = load_card_data()
    if df is None:
        return
    
    # 显示密保卡表格
    st.subheader("密保卡数据")
    st.dataframe(df, use_container_width=True)
    
    st.markdown("---")
    
    # 用户输入
    st.subheader("查询密保码")
    st.markdown("请输入4位代码，格式如：**a3b4**（字母+数字+字母+数字）")
    
    col1, col2 = st.columns([3, 1])
    
    with col1:
        user_input = st.text_input(
            "输入代码：", 
            placeholder="例如：a3b4",
            help="格式：字母(A-H) + 数字(1-8) + 字母(A-H) + 数字(1-8)"
        )
    
    with col2:
        query_button = st.button("🔍 查询", type="primary")
    
    # 处理查询
    if query_button or user_input:
        if not user_input:
            st.warning("请输入查询代码")
            return
            
        # 解析代码
        positions = parse_code(user_input)
        if positions is None:
            st.error("❌ 代码格式错误！请输入正确格式，如：a3b4")
            st.info("格式说明：字母(A-H) + 数字(1-8) + 字母(A-H) + 数字(1-8)")
            return
        
        # 获取对应值
        result = get_values(df, positions)
        if result is None:
            st.error("❌ 查询失败，请检查代码是否在有效范围内")
            return
        
        # 显示结果
        st.success(f"✅ 查询成功！")
        
        # 创建结果展示区域
        result_container = st.container()
        with result_container:
            col1, col2, col3 = st.columns([1, 2, 1])
            with col2:
                st.markdown(f"""
                <div style="
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    padding: 2rem;
                    border-radius: 15px;
                    text-align: center;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                    margin: 1rem 0;
                ">
                    <h2 style="color: white; margin: 0; font-size: 1.2rem;">查询代码</h2>
                    <h1 style="color: #FFD700; margin: 0.5rem 0; font-size: 2rem; font-weight: bold;">{user_input.upper()}</h1>
                    <h2 style="color: white; margin: 0; font-size: 1.2rem;">对应密保码</h2>
                    <h1 style="color: #00FF7F; margin: 0.5rem 0; font-size: 3rem; font-weight: bold; letter-spacing: 0.2rem;">{result}</h1>
                </div>
                """, unsafe_allow_html=True)
        
        # 显示查询详情
        st.markdown("### 📋 查询详情")
        detail_col1, detail_col2 = st.columns(2)
        
        with detail_col1:
            st.info(f"**第一个位置：** {positions[0][0]}{positions[0][1]} → {df.loc[positions[0][0], str(positions[0][1])]}")
        
        with detail_col2:
            st.info(f"**第二个位置：** {positions[1][0]}{positions[1][1]} → {df.loc[positions[1][0], str(positions[1][1])]}")

    # 添加使用说明
    st.markdown("---")
    with st.expander("📖 使用说明"):
        st.markdown("""
        ### 如何使用密保卡查询系统：
        
        1. **输入格式**：输入4位代码，格式为 `字母数字字母数字`
           - 字母范围：A-H（对应密保卡的行）
           - 数字范围：1-8（对应密保卡的列）
           
        2. **示例**：
           - 输入 `a3b4` 表示查询 A行3列 和 B行4列 的数值
           - 系统会将这两个位置的数字连接起来作为最终结果
           
        3. **注意事项**：
           - 输入不区分大小写
           - 系统会自动去除多余空格
           - 确保输入的位置在密保卡范围内
        """)

if __name__ == "__main__":
    main()
