#!/usr/bin/env python3
"""
测试剪贴板功能
"""

import pyperclip

def test_clipboard():
    """测试剪贴板复制功能"""
    print("🧪 测试剪贴板功能...")
    
    test_text = "611286"
    
    try:
        # 复制到剪贴板
        pyperclip.copy(test_text)
        print(f"✅ 已复制到剪贴板: {test_text}")
        
        # 从剪贴板读取
        clipboard_content = pyperclip.paste()
        print(f"📋 剪贴板内容: {clipboard_content}")
        
        # 验证
        if clipboard_content == test_text:
            print("🎉 剪贴板功能正常！")
            return True
        else:
            print("❌ 剪贴板内容不匹配")
            return False
            
    except Exception as e:
        print(f"❌ 剪贴板功能异常: {str(e)}")
        print("💡 这可能是因为在无图形界面环境中运行")
        return False

if __name__ == "__main__":
    test_clipboard()
