"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
N.B. This file is generated by `//Snowpark/ir-dsl-c`. DO NOT EDIT!
Generated from `{**************:snowflakedb/snowflake.git}/Snowpark/ast`.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import google.protobuf.wrappers_pb2
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class ___Version__:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class ___Version__EnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[___Version__.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    PROTO3_REQUIRES_THIS: ___Version__.ValueType  # 0
    MAX_VERSION: ___Version__.ValueType  # 1

class __Version__(___Version__, metaclass=___Version__EnumTypeWrapper): ...

PROTO3_REQUIRES_THIS: __Version__.ValueType  # 0
MAX_VERSION: __Version__.ValueType  # 1
global_____Version__ = __Version__

@typing.final
class InternedValueTable(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class StringValuesEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.int
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.int = ...,
            value: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    STRING_VALUES_FIELD_NUMBER: builtins.int
    @property
    def string_values(self) -> google.protobuf.internal.containers.ScalarMap[builtins.int, builtins.str]: ...
    def __init__(
        self,
        *,
        string_values: collections.abc.Mapping[builtins.int, builtins.str] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["string_values", b"string_values"]) -> None: ...

global___InternedValueTable = InternedValueTable

@typing.final
class List_String(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LIST_FIELD_NUMBER: builtins.int
    @property
    def list(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    def __init__(
        self,
        *,
        list: collections.abc.Iterable[builtins.str] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["list", b"list"]) -> None: ...

global___List_String = List_String

@typing.final
class Tuple_Expr_Expr(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    _1_FIELD_NUMBER: builtins.int
    _2_FIELD_NUMBER: builtins.int
    @property
    def _1(self) -> global___Expr: ...
    @property
    def _2(self) -> global___Expr: ...
    def __init__(
        self,
        *,
        _1: global___Expr | None = ...,
        _2: global___Expr | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_1", b"_1", "_2", b"_2"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_1", b"_1", "_2", b"_2"]) -> None: ...

global___Tuple_Expr_Expr = Tuple_Expr_Expr

@typing.final
class Tuple_Expr_Float(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    _1_FIELD_NUMBER: builtins.int
    _2_FIELD_NUMBER: builtins.int
    _2: builtins.float
    @property
    def _1(self) -> global___Expr: ...
    def __init__(
        self,
        *,
        _1: global___Expr | None = ...,
        _2: builtins.float = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_1", b"_1"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_1", b"_1", "_2", b"_2"]) -> None: ...

global___Tuple_Expr_Float = Tuple_Expr_Float

@typing.final
class Tuple_String_Expr(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    _1_FIELD_NUMBER: builtins.int
    _2_FIELD_NUMBER: builtins.int
    _1: builtins.str
    @property
    def _2(self) -> global___Expr: ...
    def __init__(
        self,
        *,
        _1: builtins.str = ...,
        _2: global___Expr | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_2", b"_2"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_1", b"_1", "_2", b"_2"]) -> None: ...

global___Tuple_String_Expr = Tuple_String_Expr

@typing.final
class Tuple_String_List_String(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    _1_FIELD_NUMBER: builtins.int
    _2_FIELD_NUMBER: builtins.int
    _1: builtins.str
    @property
    def _2(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    def __init__(
        self,
        *,
        _1: builtins.str = ...,
        _2: collections.abc.Iterable[builtins.str] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["_1", b"_1", "_2", b"_2"]) -> None: ...

global___Tuple_String_List_String = Tuple_String_List_String

@typing.final
class Tuple_String_String(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    _1_FIELD_NUMBER: builtins.int
    _2_FIELD_NUMBER: builtins.int
    _1: builtins.str
    _2: builtins.str
    def __init__(
        self,
        *,
        _1: builtins.str = ...,
        _2: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["_1", b"_1", "_2", b"_2"]) -> None: ...

global___Tuple_String_String = Tuple_String_String

@typing.final
class Callable(google.protobuf.message.Message):
    """fn.ir:25"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    OBJECT_NAME_FIELD_NUMBER: builtins.int
    id: builtins.int
    name: builtins.str
    @property
    def object_name(self) -> global___NameRef: ...
    def __init__(
        self,
        *,
        id: builtins.int = ...,
        name: builtins.str = ...,
        object_name: global___NameRef | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["object_name", b"object_name"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["id", b"id", "name", b"name", "object_name", b"object_name"]) -> None: ...

global___Callable = Callable

@typing.final
class ColumnAliasFn(google.protobuf.message.Message):
    """column.ir:31"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLUMN_ALIAS_FN_ALIAS_FIELD_NUMBER: builtins.int
    COLUMN_ALIAS_FN_AS_FIELD_NUMBER: builtins.int
    COLUMN_ALIAS_FN_NAME_FIELD_NUMBER: builtins.int
    column_alias_fn_alias: builtins.bool
    column_alias_fn_as: builtins.bool
    column_alias_fn_name: builtins.bool
    def __init__(
        self,
        *,
        column_alias_fn_alias: builtins.bool = ...,
        column_alias_fn_as: builtins.bool = ...,
        column_alias_fn_name: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["column_alias_fn_alias", b"column_alias_fn_alias", "column_alias_fn_as", b"column_alias_fn_as", "column_alias_fn_name", b"column_alias_fn_name", "variant", b"variant"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["column_alias_fn_alias", b"column_alias_fn_alias", "column_alias_fn_as", b"column_alias_fn_as", "column_alias_fn_name", b"column_alias_fn_name", "variant", b"variant"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["variant", b"variant"]) -> typing.Literal["column_alias_fn_alias", "column_alias_fn_as", "column_alias_fn_name"] | None: ...

global___ColumnAliasFn = ColumnAliasFn

@typing.final
class ColumnRef(google.protobuf.message.Message):
    """type.ir:32"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLUMN_IDENTIFIER_FIELD_NUMBER: builtins.int
    COLUMN_NAME_FIELD_NUMBER: builtins.int
    @property
    def column_identifier(self) -> global___ColumnIdentifier: ...
    @property
    def column_name(self) -> global___ColumnName: ...
    def __init__(
        self,
        *,
        column_identifier: global___ColumnIdentifier | None = ...,
        column_name: global___ColumnName | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["column_identifier", b"column_identifier", "column_name", b"column_name", "sealed_value", b"sealed_value"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["column_identifier", b"column_identifier", "column_name", b"column_name", "sealed_value", b"sealed_value"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["sealed_value", b"sealed_value"]) -> typing.Literal["column_identifier", "column_name"] | None: ...

global___ColumnRef = ColumnRef

@typing.final
class ColumnIdentifier(google.protobuf.message.Message):
    """type.ir:33"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    name: builtins.str
    def __init__(
        self,
        *,
        name: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["name", b"name"]) -> None: ...

global___ColumnIdentifier = ColumnIdentifier

@typing.final
class ColumnName(google.protobuf.message.Message):
    """type.ir:34"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    name: builtins.str
    def __init__(
        self,
        *,
        name: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["name", b"name"]) -> None: ...

global___ColumnName = ColumnName

@typing.final
class DataType(google.protobuf.message.Message):
    """type.ir:1"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ARRAY_TYPE_FIELD_NUMBER: builtins.int
    BINARY_TYPE_FIELD_NUMBER: builtins.int
    BOOLEAN_TYPE_FIELD_NUMBER: builtins.int
    BYTE_TYPE_FIELD_NUMBER: builtins.int
    DATE_TYPE_FIELD_NUMBER: builtins.int
    DECIMAL_TYPE_FIELD_NUMBER: builtins.int
    DOUBLE_TYPE_FIELD_NUMBER: builtins.int
    FILE_TYPE_FIELD_NUMBER: builtins.int
    FLOAT_TYPE_FIELD_NUMBER: builtins.int
    GEOGRAPHY_TYPE_FIELD_NUMBER: builtins.int
    GEOMETRY_TYPE_FIELD_NUMBER: builtins.int
    INTEGER_TYPE_FIELD_NUMBER: builtins.int
    LONG_TYPE_FIELD_NUMBER: builtins.int
    MAP_TYPE_FIELD_NUMBER: builtins.int
    NULL_TYPE_FIELD_NUMBER: builtins.int
    PANDAS_DATA_FRAME_TYPE_FIELD_NUMBER: builtins.int
    PANDAS_SERIES_TYPE_FIELD_NUMBER: builtins.int
    SHORT_TYPE_FIELD_NUMBER: builtins.int
    STRING_TYPE_FIELD_NUMBER: builtins.int
    STRUCT_FIELD_FIELD_NUMBER: builtins.int
    STRUCT_TYPE_FIELD_NUMBER: builtins.int
    TIME_TYPE_FIELD_NUMBER: builtins.int
    TIMESTAMP_TYPE_FIELD_NUMBER: builtins.int
    VARIANT_TYPE_FIELD_NUMBER: builtins.int
    VECTOR_TYPE_FIELD_NUMBER: builtins.int
    binary_type: builtins.bool
    boolean_type: builtins.bool
    byte_type: builtins.bool
    date_type: builtins.bool
    double_type: builtins.bool
    file_type: builtins.bool
    float_type: builtins.bool
    geography_type: builtins.bool
    geometry_type: builtins.bool
    integer_type: builtins.bool
    long_type: builtins.bool
    null_type: builtins.bool
    short_type: builtins.bool
    time_type: builtins.bool
    variant_type: builtins.bool
    @property
    def array_type(self) -> global___ArrayType: ...
    @property
    def decimal_type(self) -> global___DecimalType: ...
    @property
    def map_type(self) -> global___MapType: ...
    @property
    def pandas_data_frame_type(self) -> global___PandasDataFrameType: ...
    @property
    def pandas_series_type(self) -> global___PandasSeriesType: ...
    @property
    def string_type(self) -> global___StringType: ...
    @property
    def struct_field(self) -> global___StructField: ...
    @property
    def struct_type(self) -> global___StructType: ...
    @property
    def timestamp_type(self) -> global___TimestampType: ...
    @property
    def vector_type(self) -> global___VectorType: ...
    def __init__(
        self,
        *,
        array_type: global___ArrayType | None = ...,
        binary_type: builtins.bool = ...,
        boolean_type: builtins.bool = ...,
        byte_type: builtins.bool = ...,
        date_type: builtins.bool = ...,
        decimal_type: global___DecimalType | None = ...,
        double_type: builtins.bool = ...,
        file_type: builtins.bool = ...,
        float_type: builtins.bool = ...,
        geography_type: builtins.bool = ...,
        geometry_type: builtins.bool = ...,
        integer_type: builtins.bool = ...,
        long_type: builtins.bool = ...,
        map_type: global___MapType | None = ...,
        null_type: builtins.bool = ...,
        pandas_data_frame_type: global___PandasDataFrameType | None = ...,
        pandas_series_type: global___PandasSeriesType | None = ...,
        short_type: builtins.bool = ...,
        string_type: global___StringType | None = ...,
        struct_field: global___StructField | None = ...,
        struct_type: global___StructType | None = ...,
        time_type: builtins.bool = ...,
        timestamp_type: global___TimestampType | None = ...,
        variant_type: builtins.bool = ...,
        vector_type: global___VectorType | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["array_type", b"array_type", "binary_type", b"binary_type", "boolean_type", b"boolean_type", "byte_type", b"byte_type", "date_type", b"date_type", "decimal_type", b"decimal_type", "double_type", b"double_type", "file_type", b"file_type", "float_type", b"float_type", "geography_type", b"geography_type", "geometry_type", b"geometry_type", "integer_type", b"integer_type", "long_type", b"long_type", "map_type", b"map_type", "null_type", b"null_type", "pandas_data_frame_type", b"pandas_data_frame_type", "pandas_series_type", b"pandas_series_type", "short_type", b"short_type", "string_type", b"string_type", "struct_field", b"struct_field", "struct_type", b"struct_type", "time_type", b"time_type", "timestamp_type", b"timestamp_type", "variant", b"variant", "variant_type", b"variant_type", "vector_type", b"vector_type"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["array_type", b"array_type", "binary_type", b"binary_type", "boolean_type", b"boolean_type", "byte_type", b"byte_type", "date_type", b"date_type", "decimal_type", b"decimal_type", "double_type", b"double_type", "file_type", b"file_type", "float_type", b"float_type", "geography_type", b"geography_type", "geometry_type", b"geometry_type", "integer_type", b"integer_type", "long_type", b"long_type", "map_type", b"map_type", "null_type", b"null_type", "pandas_data_frame_type", b"pandas_data_frame_type", "pandas_series_type", b"pandas_series_type", "short_type", b"short_type", "string_type", b"string_type", "struct_field", b"struct_field", "struct_type", b"struct_type", "time_type", b"time_type", "timestamp_type", b"timestamp_type", "variant", b"variant", "variant_type", b"variant_type", "vector_type", b"vector_type"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["variant", b"variant"]) -> typing.Literal["array_type", "binary_type", "boolean_type", "byte_type", "date_type", "decimal_type", "double_type", "file_type", "float_type", "geography_type", "geometry_type", "integer_type", "long_type", "map_type", "null_type", "pandas_data_frame_type", "pandas_series_type", "short_type", "string_type", "struct_field", "struct_type", "time_type", "timestamp_type", "variant_type", "vector_type"] | None: ...

global___DataType = DataType

@typing.final
class ArrayType(google.protobuf.message.Message):
    """type.ir:3"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    STRUCTURED_FIELD_NUMBER: builtins.int
    TY_FIELD_NUMBER: builtins.int
    structured: builtins.bool
    @property
    def ty(self) -> global___DataType: ...
    def __init__(
        self,
        *,
        structured: builtins.bool = ...,
        ty: global___DataType | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["ty", b"ty"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["structured", b"structured", "ty", b"ty"]) -> None: ...

global___ArrayType = ArrayType

@typing.final
class DecimalType(google.protobuf.message.Message):
    """type.ir:8"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PRECISION_FIELD_NUMBER: builtins.int
    SCALE_FIELD_NUMBER: builtins.int
    precision: builtins.int
    scale: builtins.int
    def __init__(
        self,
        *,
        precision: builtins.int = ...,
        scale: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["precision", b"precision", "scale", b"scale"]) -> None: ...

global___DecimalType = DecimalType

@typing.final
class MapType(google.protobuf.message.Message):
    """type.ir:16"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    KEY_TY_FIELD_NUMBER: builtins.int
    STRUCTURED_FIELD_NUMBER: builtins.int
    VALUE_TY_FIELD_NUMBER: builtins.int
    structured: builtins.bool
    @property
    def key_ty(self) -> global___DataType: ...
    @property
    def value_ty(self) -> global___DataType: ...
    def __init__(
        self,
        *,
        key_ty: global___DataType | None = ...,
        structured: builtins.bool = ...,
        value_ty: global___DataType | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["key_ty", b"key_ty", "value_ty", b"value_ty"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["key_ty", b"key_ty", "structured", b"structured", "value_ty", b"value_ty"]) -> None: ...

global___MapType = MapType

@typing.final
class StringType(google.protobuf.message.Message):
    """type.ir:19"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LENGTH_FIELD_NUMBER: builtins.int
    @property
    def length(self) -> google.protobuf.wrappers_pb2.Int64Value: ...
    def __init__(
        self,
        *,
        length: google.protobuf.wrappers_pb2.Int64Value | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["length", b"length"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["length", b"length"]) -> None: ...

global___StringType = StringType

@typing.final
class StructField(google.protobuf.message.Message):
    """type.ir:20"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLUMN_IDENTIFIER_FIELD_NUMBER: builtins.int
    DATA_TYPE_FIELD_NUMBER: builtins.int
    NULLABLE_FIELD_NUMBER: builtins.int
    nullable: builtins.bool
    @property
    def column_identifier(self) -> global___ColumnRef: ...
    @property
    def data_type(self) -> global___DataType: ...
    def __init__(
        self,
        *,
        column_identifier: global___ColumnRef | None = ...,
        data_type: global___DataType | None = ...,
        nullable: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["column_identifier", b"column_identifier", "data_type", b"data_type"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["column_identifier", b"column_identifier", "data_type", b"data_type", "nullable", b"nullable"]) -> None: ...

global___StructField = StructField

@typing.final
class StructType(google.protobuf.message.Message):
    """type.ir:21"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FIELDS_FIELD_NUMBER: builtins.int
    STRUCTURED_FIELD_NUMBER: builtins.int
    structured: builtins.bool
    @property
    def fields(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___StructField]: ...
    def __init__(
        self,
        *,
        fields: collections.abc.Iterable[global___StructField] | None = ...,
        structured: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["fields", b"fields", "structured", b"structured"]) -> None: ...

global___StructType = StructType

@typing.final
class TimestampType(google.protobuf.message.Message):
    """type.ir:23"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TIME_ZONE_FIELD_NUMBER: builtins.int
    @property
    def time_zone(self) -> global___TimestampTimeZone: ...
    def __init__(
        self,
        *,
        time_zone: global___TimestampTimeZone | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["time_zone", b"time_zone"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["time_zone", b"time_zone"]) -> None: ...

global___TimestampType = TimestampType

@typing.final
class VectorType(google.protobuf.message.Message):
    """type.ir:25"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DIMENSION_FIELD_NUMBER: builtins.int
    TY_FIELD_NUMBER: builtins.int
    dimension: builtins.int
    @property
    def ty(self) -> global___DataType: ...
    def __init__(
        self,
        *,
        dimension: builtins.int = ...,
        ty: global___DataType | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["ty", b"ty"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["dimension", b"dimension", "ty", b"ty"]) -> None: ...

global___VectorType = VectorType

@typing.final
class PandasSeriesType(google.protobuf.message.Message):
    """type.ir:27"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    EL_TY_FIELD_NUMBER: builtins.int
    @property
    def el_ty(self) -> global___DataType: ...
    def __init__(
        self,
        *,
        el_ty: global___DataType | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["el_ty", b"el_ty"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["el_ty", b"el_ty"]) -> None: ...

global___PandasSeriesType = PandasSeriesType

@typing.final
class PandasDataFrameType(google.protobuf.message.Message):
    """type.ir:28"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_NAMES_FIELD_NUMBER: builtins.int
    COL_TYPES_FIELD_NUMBER: builtins.int
    @property
    def col_names(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def col_types(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___DataType]: ...
    def __init__(
        self,
        *,
        col_names: collections.abc.Iterable[builtins.str] | None = ...,
        col_types: collections.abc.Iterable[global___DataType] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["col_names", b"col_names", "col_types", b"col_types"]) -> None: ...

global___PandasDataFrameType = PandasDataFrameType

@typing.final
class DataframeData(google.protobuf.message.Message):
    """dataframe.ir:59"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATAFRAME_DATA__LIST_FIELD_NUMBER: builtins.int
    DATAFRAME_DATA__PANDAS_FIELD_NUMBER: builtins.int
    DATAFRAME_DATA__TUPLE_FIELD_NUMBER: builtins.int
    @property
    def dataframe_data__list(self) -> global___DataframeData_List: ...
    @property
    def dataframe_data__pandas(self) -> global___DataframeData_Pandas: ...
    @property
    def dataframe_data__tuple(self) -> global___DataframeData_Tuple: ...
    def __init__(
        self,
        *,
        dataframe_data__list: global___DataframeData_List | None = ...,
        dataframe_data__pandas: global___DataframeData_Pandas | None = ...,
        dataframe_data__tuple: global___DataframeData_Tuple | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["dataframe_data__list", b"dataframe_data__list", "dataframe_data__pandas", b"dataframe_data__pandas", "dataframe_data__tuple", b"dataframe_data__tuple", "sealed_value", b"sealed_value"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["dataframe_data__list", b"dataframe_data__list", "dataframe_data__pandas", b"dataframe_data__pandas", "dataframe_data__tuple", b"dataframe_data__tuple", "sealed_value", b"sealed_value"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["sealed_value", b"sealed_value"]) -> typing.Literal["dataframe_data__list", "dataframe_data__pandas", "dataframe_data__tuple"] | None: ...

global___DataframeData = DataframeData

@typing.final
class DataframeData_List(google.protobuf.message.Message):
    """dataframe.ir:60"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VS_FIELD_NUMBER: builtins.int
    @property
    def vs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Expr]: ...
    def __init__(
        self,
        *,
        vs: collections.abc.Iterable[global___Expr] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["vs", b"vs"]) -> None: ...

global___DataframeData_List = DataframeData_List

@typing.final
class DataframeData_Tuple(google.protobuf.message.Message):
    """dataframe.ir:61"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VS_FIELD_NUMBER: builtins.int
    @property
    def vs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Expr]: ...
    def __init__(
        self,
        *,
        vs: collections.abc.Iterable[global___Expr] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["vs", b"vs"]) -> None: ...

global___DataframeData_Tuple = DataframeData_Tuple

@typing.final
class DataframeData_Pandas(google.protobuf.message.Message):
    """dataframe.ir:62"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    V_FIELD_NUMBER: builtins.int
    @property
    def v(self) -> global___StagedPandasDataframe: ...
    def __init__(
        self,
        *,
        v: global___StagedPandasDataframe | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["v", b"v"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["v", b"v"]) -> None: ...

global___DataframeData_Pandas = DataframeData_Pandas

@typing.final
class DataframeSchema(google.protobuf.message.Message):
    """type.ir:44"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATAFRAME_SCHEMA__LIST_FIELD_NUMBER: builtins.int
    DATAFRAME_SCHEMA__STRUCT_FIELD_NUMBER: builtins.int
    @property
    def dataframe_schema__list(self) -> global___DataframeSchema_List: ...
    @property
    def dataframe_schema__struct(self) -> global___DataframeSchema_Struct: ...
    def __init__(
        self,
        *,
        dataframe_schema__list: global___DataframeSchema_List | None = ...,
        dataframe_schema__struct: global___DataframeSchema_Struct | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["dataframe_schema__list", b"dataframe_schema__list", "dataframe_schema__struct", b"dataframe_schema__struct", "sealed_value", b"sealed_value"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["dataframe_schema__list", b"dataframe_schema__list", "dataframe_schema__struct", b"dataframe_schema__struct", "sealed_value", b"sealed_value"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["sealed_value", b"sealed_value"]) -> typing.Literal["dataframe_schema__list", "dataframe_schema__struct"] | None: ...

global___DataframeSchema = DataframeSchema

@typing.final
class DataframeSchema_List(google.protobuf.message.Message):
    """type.ir:45"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VS_FIELD_NUMBER: builtins.int
    @property
    def vs(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    def __init__(
        self,
        *,
        vs: collections.abc.Iterable[builtins.str] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["vs", b"vs"]) -> None: ...

global___DataframeSchema_List = DataframeSchema_List

@typing.final
class DataframeSchema_Struct(google.protobuf.message.Message):
    """type.ir:46"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    V_FIELD_NUMBER: builtins.int
    @property
    def v(self) -> global___StructType: ...
    def __init__(
        self,
        *,
        v: global___StructType | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["v", b"v"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["v", b"v"]) -> None: ...

global___DataframeSchema_Struct = DataframeSchema_Struct

@typing.final
class FlattenMode(google.protobuf.message.Message):
    """dataframe.ir:77"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FLATTEN_MODE_ARRAY_FIELD_NUMBER: builtins.int
    FLATTEN_MODE_BOTH_FIELD_NUMBER: builtins.int
    FLATTEN_MODE_OBJECT_FIELD_NUMBER: builtins.int
    flatten_mode_array: builtins.bool
    flatten_mode_both: builtins.bool
    flatten_mode_object: builtins.bool
    def __init__(
        self,
        *,
        flatten_mode_array: builtins.bool = ...,
        flatten_mode_both: builtins.bool = ...,
        flatten_mode_object: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["flatten_mode_array", b"flatten_mode_array", "flatten_mode_both", b"flatten_mode_both", "flatten_mode_object", b"flatten_mode_object", "variant", b"variant"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["flatten_mode_array", b"flatten_mode_array", "flatten_mode_both", b"flatten_mode_both", "flatten_mode_object", b"flatten_mode_object", "variant", b"variant"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["variant", b"variant"]) -> typing.Literal["flatten_mode_array", "flatten_mode_both", "flatten_mode_object"] | None: ...

global___FlattenMode = FlattenMode

@typing.final
class JoinType(google.protobuf.message.Message):
    """dataframe.ir:233"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    JOIN_TYPE__ASOF_FIELD_NUMBER: builtins.int
    JOIN_TYPE__CROSS_FIELD_NUMBER: builtins.int
    JOIN_TYPE__FULL_OUTER_FIELD_NUMBER: builtins.int
    JOIN_TYPE__INNER_FIELD_NUMBER: builtins.int
    JOIN_TYPE__LEFT_ANTI_FIELD_NUMBER: builtins.int
    JOIN_TYPE__LEFT_OUTER_FIELD_NUMBER: builtins.int
    JOIN_TYPE__LEFT_SEMI_FIELD_NUMBER: builtins.int
    JOIN_TYPE__RIGHT_OUTER_FIELD_NUMBER: builtins.int
    join_type__asof: builtins.bool
    join_type__cross: builtins.bool
    join_type__full_outer: builtins.bool
    join_type__inner: builtins.bool
    join_type__left_anti: builtins.bool
    join_type__left_outer: builtins.bool
    join_type__left_semi: builtins.bool
    join_type__right_outer: builtins.bool
    def __init__(
        self,
        *,
        join_type__asof: builtins.bool = ...,
        join_type__cross: builtins.bool = ...,
        join_type__full_outer: builtins.bool = ...,
        join_type__inner: builtins.bool = ...,
        join_type__left_anti: builtins.bool = ...,
        join_type__left_outer: builtins.bool = ...,
        join_type__left_semi: builtins.bool = ...,
        join_type__right_outer: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["join_type__asof", b"join_type__asof", "join_type__cross", b"join_type__cross", "join_type__full_outer", b"join_type__full_outer", "join_type__inner", b"join_type__inner", "join_type__left_anti", b"join_type__left_anti", "join_type__left_outer", b"join_type__left_outer", "join_type__left_semi", b"join_type__left_semi", "join_type__right_outer", b"join_type__right_outer", "variant", b"variant"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["join_type__asof", b"join_type__asof", "join_type__cross", b"join_type__cross", "join_type__full_outer", b"join_type__full_outer", "join_type__inner", b"join_type__inner", "join_type__left_anti", b"join_type__left_anti", "join_type__left_outer", b"join_type__left_outer", "join_type__left_semi", b"join_type__left_semi", "join_type__right_outer", b"join_type__right_outer", "variant", b"variant"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["variant", b"variant"]) -> typing.Literal["join_type__asof", "join_type__cross", "join_type__full_outer", "join_type__inner", "join_type__left_anti", "join_type__left_outer", "join_type__left_semi", "join_type__right_outer"] | None: ...

global___JoinType = JoinType

@typing.final
class Language(google.protobuf.message.Message):
    """ast.ir:26"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    JAVA_LANGUAGE_FIELD_NUMBER: builtins.int
    PYTHON_LANGUAGE_FIELD_NUMBER: builtins.int
    SCALA_LANGUAGE_FIELD_NUMBER: builtins.int
    @property
    def java_language(self) -> global___JavaLanguage: ...
    @property
    def python_language(self) -> global___PythonLanguage: ...
    @property
    def scala_language(self) -> global___ScalaLanguage: ...
    def __init__(
        self,
        *,
        java_language: global___JavaLanguage | None = ...,
        python_language: global___PythonLanguage | None = ...,
        scala_language: global___ScalaLanguage | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["java_language", b"java_language", "python_language", b"python_language", "scala_language", b"scala_language", "sealed_value", b"sealed_value"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["java_language", b"java_language", "python_language", b"python_language", "scala_language", b"scala_language", "sealed_value", b"sealed_value"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["sealed_value", b"sealed_value"]) -> typing.Literal["java_language", "python_language", "scala_language"] | None: ...

global___Language = Language

@typing.final
class PythonLanguage(google.protobuf.message.Message):
    """ast.ir:27"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VERSION_FIELD_NUMBER: builtins.int
    @property
    def version(self) -> global___Version: ...
    def __init__(
        self,
        *,
        version: global___Version | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["version", b"version"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["version", b"version"]) -> None: ...

global___PythonLanguage = PythonLanguage

@typing.final
class ScalaLanguage(google.protobuf.message.Message):
    """ast.ir:28"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VERSION_FIELD_NUMBER: builtins.int
    @property
    def version(self) -> global___Version: ...
    def __init__(
        self,
        *,
        version: global___Version | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["version", b"version"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["version", b"version"]) -> None: ...

global___ScalaLanguage = ScalaLanguage

@typing.final
class JavaLanguage(google.protobuf.message.Message):
    """ast.ir:29"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VERSION_FIELD_NUMBER: builtins.int
    @property
    def version(self) -> global___Version: ...
    def __init__(
        self,
        *,
        version: global___Version | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["version", b"version"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["version", b"version"]) -> None: ...

global___JavaLanguage = JavaLanguage

@typing.final
class Name(google.protobuf.message.Message):
    """ast.ir:121"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FLAT_FIELD_NUMBER: builtins.int
    NAME_STRUCTURED_FIELD_NUMBER: builtins.int
    @property
    def name_flat(self) -> global___NameFlat: ...
    @property
    def name_structured(self) -> global___NameStructured: ...
    def __init__(
        self,
        *,
        name_flat: global___NameFlat | None = ...,
        name_structured: global___NameStructured | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["name_flat", b"name_flat", "name_structured", b"name_structured", "sealed_value", b"sealed_value"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["name_flat", b"name_flat", "name_structured", b"name_structured", "sealed_value", b"sealed_value"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["sealed_value", b"sealed_value"]) -> typing.Literal["name_flat", "name_structured"] | None: ...

global___Name = Name

@typing.final
class NameFlat(google.protobuf.message.Message):
    """ast.ir:122"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    name: builtins.str
    def __init__(
        self,
        *,
        name: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["name", b"name"]) -> None: ...

global___NameFlat = NameFlat

@typing.final
class NameStructured(google.protobuf.message.Message):
    """ast.ir:123"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    @property
    def name(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    def __init__(
        self,
        *,
        name: collections.abc.Iterable[builtins.str] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["name", b"name"]) -> None: ...

global___NameStructured = NameStructured

@typing.final
class NullOrder(google.protobuf.message.Message):
    """column.ir:62"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NULL_ORDER_DEFAULT_FIELD_NUMBER: builtins.int
    NULL_ORDER_NULLS_FIRST_FIELD_NUMBER: builtins.int
    NULL_ORDER_NULLS_LAST_FIELD_NUMBER: builtins.int
    null_order_default: builtins.bool
    null_order_nulls_first: builtins.bool
    null_order_nulls_last: builtins.bool
    def __init__(
        self,
        *,
        null_order_default: builtins.bool = ...,
        null_order_nulls_first: builtins.bool = ...,
        null_order_nulls_last: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["null_order_default", b"null_order_default", "null_order_nulls_first", b"null_order_nulls_first", "null_order_nulls_last", b"null_order_nulls_last", "variant", b"variant"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["null_order_default", b"null_order_default", "null_order_nulls_first", b"null_order_nulls_first", "null_order_nulls_last", b"null_order_nulls_last", "variant", b"variant"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["variant", b"variant"]) -> typing.Literal["null_order_default", "null_order_nulls_first", "null_order_nulls_last"] | None: ...

global___NullOrder = NullOrder

@typing.final
class PythonTimeZone(google.protobuf.message.Message):
    """const.ir:84"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    OFFSET_SECONDS_FIELD_NUMBER: builtins.int
    offset_seconds: builtins.int
    @property
    def name(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    def __init__(
        self,
        *,
        name: google.protobuf.wrappers_pb2.StringValue | None = ...,
        offset_seconds: builtins.int = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["name", b"name"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["name", b"name", "offset_seconds", b"offset_seconds"]) -> None: ...

global___PythonTimeZone = PythonTimeZone

@typing.final
class SaveMode(google.protobuf.message.Message):
    """dataframe-io.ir:49"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SAVE_MODE_APPEND_FIELD_NUMBER: builtins.int
    SAVE_MODE_ERROR_IF_EXISTS_FIELD_NUMBER: builtins.int
    SAVE_MODE_IGNORE_FIELD_NUMBER: builtins.int
    SAVE_MODE_OVERWRITE_FIELD_NUMBER: builtins.int
    SAVE_MODE_TRUNCATE_FIELD_NUMBER: builtins.int
    save_mode_append: builtins.bool
    save_mode_error_if_exists: builtins.bool
    save_mode_ignore: builtins.bool
    save_mode_overwrite: builtins.bool
    save_mode_truncate: builtins.bool
    def __init__(
        self,
        *,
        save_mode_append: builtins.bool = ...,
        save_mode_error_if_exists: builtins.bool = ...,
        save_mode_ignore: builtins.bool = ...,
        save_mode_overwrite: builtins.bool = ...,
        save_mode_truncate: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["save_mode_append", b"save_mode_append", "save_mode_error_if_exists", b"save_mode_error_if_exists", "save_mode_ignore", b"save_mode_ignore", "save_mode_overwrite", b"save_mode_overwrite", "save_mode_truncate", b"save_mode_truncate", "variant", b"variant"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["save_mode_append", b"save_mode_append", "save_mode_error_if_exists", b"save_mode_error_if_exists", "save_mode_ignore", b"save_mode_ignore", "save_mode_overwrite", b"save_mode_overwrite", "save_mode_truncate", b"save_mode_truncate", "variant", b"variant"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["variant", b"variant"]) -> typing.Literal["save_mode_append", "save_mode_error_if_exists", "save_mode_ignore", "save_mode_overwrite", "save_mode_truncate"] | None: ...

global___SaveMode = SaveMode

@typing.final
class SrcPosition(google.protobuf.message.Message):
    """src.ir:1"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    END_COLUMN_FIELD_NUMBER: builtins.int
    END_LINE_FIELD_NUMBER: builtins.int
    FILE_FIELD_NUMBER: builtins.int
    START_COLUMN_FIELD_NUMBER: builtins.int
    START_LINE_FIELD_NUMBER: builtins.int
    end_column: builtins.int
    end_line: builtins.int
    file: builtins.int
    start_column: builtins.int
    start_line: builtins.int
    def __init__(
        self,
        *,
        end_column: builtins.int = ...,
        end_line: builtins.int = ...,
        file: builtins.int = ...,
        start_column: builtins.int = ...,
        start_line: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["end_column", b"end_column", "end_line", b"end_line", "file", b"file", "start_column", b"start_column", "start_line", b"start_line"]) -> None: ...

global___SrcPosition = SrcPosition

@typing.final
class StagedPandasDataframe(google.protobuf.message.Message):
    """dataframe.ir:65"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TEMP_TABLE_FIELD_NUMBER: builtins.int
    @property
    def temp_table(self) -> global___NameRef: ...
    def __init__(
        self,
        *,
        temp_table: global___NameRef | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["temp_table", b"temp_table"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["temp_table", b"temp_table"]) -> None: ...

global___StagedPandasDataframe = StagedPandasDataframe

@typing.final
class TableVariant(google.protobuf.message.Message):
    """dataframe.ir:96"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SESSION_TABLE_FIELD_NUMBER: builtins.int
    TABLE_INIT_FIELD_NUMBER: builtins.int
    session_table: builtins.bool
    table_init: builtins.bool
    def __init__(
        self,
        *,
        session_table: builtins.bool = ...,
        table_init: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["session_table", b"session_table", "table_init", b"table_init", "variant", b"variant"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["session_table", b"session_table", "table_init", b"table_init", "variant", b"variant"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["variant", b"variant"]) -> typing.Literal["session_table", "table_init"] | None: ...

global___TableVariant = TableVariant

@typing.final
class TimestampTimeZone(google.protobuf.message.Message):
    """type.ir:37"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TIMESTAMP_TIME_ZONE_DEFAULT_FIELD_NUMBER: builtins.int
    TIMESTAMP_TIME_ZONE_LTZ_FIELD_NUMBER: builtins.int
    TIMESTAMP_TIME_ZONE_NTZ_FIELD_NUMBER: builtins.int
    TIMESTAMP_TIME_ZONE_TZ_FIELD_NUMBER: builtins.int
    timestamp_time_zone_default: builtins.bool
    timestamp_time_zone_ltz: builtins.bool
    timestamp_time_zone_ntz: builtins.bool
    timestamp_time_zone_tz: builtins.bool
    def __init__(
        self,
        *,
        timestamp_time_zone_default: builtins.bool = ...,
        timestamp_time_zone_ltz: builtins.bool = ...,
        timestamp_time_zone_ntz: builtins.bool = ...,
        timestamp_time_zone_tz: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["timestamp_time_zone_default", b"timestamp_time_zone_default", "timestamp_time_zone_ltz", b"timestamp_time_zone_ltz", "timestamp_time_zone_ntz", b"timestamp_time_zone_ntz", "timestamp_time_zone_tz", b"timestamp_time_zone_tz", "variant", b"variant"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["timestamp_time_zone_default", b"timestamp_time_zone_default", "timestamp_time_zone_ltz", b"timestamp_time_zone_ltz", "timestamp_time_zone_ntz", b"timestamp_time_zone_ntz", "timestamp_time_zone_tz", b"timestamp_time_zone_tz", "variant", b"variant"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["variant", b"variant"]) -> typing.Literal["timestamp_time_zone_default", "timestamp_time_zone_ltz", "timestamp_time_zone_ntz", "timestamp_time_zone_tz"] | None: ...

global___TimestampTimeZone = TimestampTimeZone

@typing.final
class UdtfSchema(google.protobuf.message.Message):
    """fn.ir:87"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    UDTF_SCHEMA__NAMES_FIELD_NUMBER: builtins.int
    UDTF_SCHEMA__TYPE_FIELD_NUMBER: builtins.int
    @property
    def udtf_schema__names(self) -> global___UdtfSchema_Names: ...
    @property
    def udtf_schema__type(self) -> global___UdtfSchema_Type: ...
    def __init__(
        self,
        *,
        udtf_schema__names: global___UdtfSchema_Names | None = ...,
        udtf_schema__type: global___UdtfSchema_Type | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["sealed_value", b"sealed_value", "udtf_schema__names", b"udtf_schema__names", "udtf_schema__type", b"udtf_schema__type"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["sealed_value", b"sealed_value", "udtf_schema__names", b"udtf_schema__names", "udtf_schema__type", b"udtf_schema__type"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["sealed_value", b"sealed_value"]) -> typing.Literal["udtf_schema__names", "udtf_schema__type"] | None: ...

global___UdtfSchema = UdtfSchema

@typing.final
class UdtfSchema_Type(google.protobuf.message.Message):
    """fn.ir:88"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RETURN_TYPE_FIELD_NUMBER: builtins.int
    @property
    def return_type(self) -> global___DataType: ...
    def __init__(
        self,
        *,
        return_type: global___DataType | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["return_type", b"return_type"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["return_type", b"return_type"]) -> None: ...

global___UdtfSchema_Type = UdtfSchema_Type

@typing.final
class UdtfSchema_Names(google.protobuf.message.Message):
    """fn.ir:89"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SCHEMA_FIELD_NUMBER: builtins.int
    @property
    def schema(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    def __init__(
        self,
        *,
        schema: collections.abc.Iterable[builtins.str] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["schema", b"schema"]) -> None: ...

global___UdtfSchema_Names = UdtfSchema_Names

@typing.final
class Version(google.protobuf.message.Message):
    """ast.ir:32"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LABEL_FIELD_NUMBER: builtins.int
    MAJOR_FIELD_NUMBER: builtins.int
    MINOR_FIELD_NUMBER: builtins.int
    PATCH_FIELD_NUMBER: builtins.int
    label: builtins.str
    major: builtins.int
    minor: builtins.int
    patch: builtins.int
    def __init__(
        self,
        *,
        label: builtins.str = ...,
        major: builtins.int = ...,
        minor: builtins.int = ...,
        patch: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["label", b"label", "major", b"major", "minor", b"minor", "patch", b"patch"]) -> None: ...

global___Version = Version

@typing.final
class WindowRelativePosition(google.protobuf.message.Message):
    """window.ir:6"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    WINDOW_RELATIVE_POSITION__CURRENT_ROW_FIELD_NUMBER: builtins.int
    WINDOW_RELATIVE_POSITION__POSITION_FIELD_NUMBER: builtins.int
    WINDOW_RELATIVE_POSITION__UNBOUNDED_FOLLOWING_FIELD_NUMBER: builtins.int
    WINDOW_RELATIVE_POSITION__UNBOUNDED_PRECEDING_FIELD_NUMBER: builtins.int
    window_relative_position__current_row: builtins.bool
    window_relative_position__unbounded_following: builtins.bool
    window_relative_position__unbounded_preceding: builtins.bool
    @property
    def window_relative_position__position(self) -> global___WindowRelativePosition_Position: ...
    def __init__(
        self,
        *,
        window_relative_position__current_row: builtins.bool = ...,
        window_relative_position__position: global___WindowRelativePosition_Position | None = ...,
        window_relative_position__unbounded_following: builtins.bool = ...,
        window_relative_position__unbounded_preceding: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["variant", b"variant", "window_relative_position__current_row", b"window_relative_position__current_row", "window_relative_position__position", b"window_relative_position__position", "window_relative_position__unbounded_following", b"window_relative_position__unbounded_following", "window_relative_position__unbounded_preceding", b"window_relative_position__unbounded_preceding"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["variant", b"variant", "window_relative_position__current_row", b"window_relative_position__current_row", "window_relative_position__position", b"window_relative_position__position", "window_relative_position__unbounded_following", b"window_relative_position__unbounded_following", "window_relative_position__unbounded_preceding", b"window_relative_position__unbounded_preceding"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["variant", b"variant"]) -> typing.Literal["window_relative_position__current_row", "window_relative_position__position", "window_relative_position__unbounded_following", "window_relative_position__unbounded_preceding"] | None: ...

global___WindowRelativePosition = WindowRelativePosition

@typing.final
class WindowRelativePosition_Position(google.protobuf.message.Message):
    """window.ir:10"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    N_FIELD_NUMBER: builtins.int
    @property
    def n(self) -> global___Expr: ...
    def __init__(
        self,
        *,
        n: global___Expr | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["n", b"n"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["n", b"n"]) -> None: ...

global___WindowRelativePosition_Position = WindowRelativePosition_Position

@typing.final
class AbstractExtension(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TRAIT_EXTENSION_EXPR_FIELD_NUMBER: builtins.int
    EXTENSION_ERROR_FIELD_NUMBER: builtins.int
    EXTENSION_EVAL_RESULT_FIELD_NUMBER: builtins.int
    EXTENSION_STMT_FIELD_NUMBER: builtins.int
    @property
    def trait_extension_expr(self) -> global___ExtensionExpr: ...
    @property
    def extension_error(self) -> global___ExtensionError: ...
    @property
    def extension_eval_result(self) -> global___ExtensionEvalResult: ...
    @property
    def extension_stmt(self) -> global___ExtensionStmt: ...
    def __init__(
        self,
        *,
        trait_extension_expr: global___ExtensionExpr | None = ...,
        extension_error: global___ExtensionError | None = ...,
        extension_eval_result: global___ExtensionEvalResult | None = ...,
        extension_stmt: global___ExtensionStmt | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["extension_error", b"extension_error", "extension_eval_result", b"extension_eval_result", "extension_stmt", b"extension_stmt", "trait_extension_expr", b"trait_extension_expr", "variant", b"variant"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["extension_error", b"extension_error", "extension_eval_result", b"extension_eval_result", "extension_stmt", b"extension_stmt", "trait_extension_expr", b"trait_extension_expr", "variant", b"variant"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["variant", b"variant"]) -> typing.Literal["trait_extension_expr", "extension_error", "extension_eval_result", "extension_stmt"] | None: ...

global___AbstractExtension = AbstractExtension

@typing.final
class Add(google.protobuf.message.Message):
    """op.ir:42"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LHS_FIELD_NUMBER: builtins.int
    RHS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def lhs(self) -> global___Expr: ...
    @property
    def rhs(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        lhs: global___Expr | None = ...,
        rhs: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> None: ...

global___Add = Add

@typing.final
class And(google.protobuf.message.Message):
    """op.ir:16"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LHS_FIELD_NUMBER: builtins.int
    RHS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def lhs(self) -> global___Expr: ...
    @property
    def rhs(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        lhs: global___Expr | None = ...,
        rhs: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> None: ...

global___And = And

@typing.final
class ApplyExpr(google.protobuf.message.Message):
    """fn.ir:2"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FN_FIELD_NUMBER: builtins.int
    NAMED_ARGS_FIELD_NUMBER: builtins.int
    POS_ARGS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def fn(self) -> global___Expr: ...
    @property
    def named_args(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_Expr]: ...
    @property
    def pos_args(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Expr]: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        fn: global___Expr | None = ...,
        named_args: collections.abc.Iterable[global___Tuple_String_Expr] | None = ...,
        pos_args: collections.abc.Iterable[global___Expr] | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["fn", b"fn", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["fn", b"fn", "named_args", b"named_args", "pos_args", b"pos_args", "src", b"src"]) -> None: ...

global___ApplyExpr = ApplyExpr

@typing.final
class BigDecimalVal(google.protobuf.message.Message):
    """const.ir:31"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SCALE_FIELD_NUMBER: builtins.int
    SPECIAL_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    UNSCALED_VALUE_FIELD_NUMBER: builtins.int
    scale: builtins.int
    unscaled_value: builtins.bytes
    @property
    def special(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        scale: builtins.int = ...,
        special: google.protobuf.wrappers_pb2.StringValue | None = ...,
        src: global___SrcPosition | None = ...,
        unscaled_value: builtins.bytes = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["special", b"special", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["scale", b"scale", "special", b"special", "src", b"src", "unscaled_value", b"unscaled_value"]) -> None: ...

global___BigDecimalVal = BigDecimalVal

@typing.final
class BigIntVal(google.protobuf.message.Message):
    """const.ir:24"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IS_NEGATIVE_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    V_FIELD_NUMBER: builtins.int
    is_negative: builtins.bool
    v: builtins.bytes
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        is_negative: builtins.bool = ...,
        src: global___SrcPosition | None = ...,
        v: builtins.bytes = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["is_negative", b"is_negative", "src", b"src", "v", b"v"]) -> None: ...

global___BigIntVal = BigIntVal

@typing.final
class BinOp(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ADD_FIELD_NUMBER: builtins.int
    AND_FIELD_NUMBER: builtins.int
    BIT_AND_FIELD_NUMBER: builtins.int
    BIT_OR_FIELD_NUMBER: builtins.int
    BIT_XOR_FIELD_NUMBER: builtins.int
    DIV_FIELD_NUMBER: builtins.int
    EQ_FIELD_NUMBER: builtins.int
    GEQ_FIELD_NUMBER: builtins.int
    GT_FIELD_NUMBER: builtins.int
    LEQ_FIELD_NUMBER: builtins.int
    LT_FIELD_NUMBER: builtins.int
    MOD_FIELD_NUMBER: builtins.int
    MUL_FIELD_NUMBER: builtins.int
    NEQ_FIELD_NUMBER: builtins.int
    OR_FIELD_NUMBER: builtins.int
    POW_FIELD_NUMBER: builtins.int
    SUB_FIELD_NUMBER: builtins.int
    @property
    def add(self) -> global___Add: ...
    @property
    def bit_and(self) -> global___BitAnd: ...
    @property
    def bit_or(self) -> global___BitOr: ...
    @property
    def bit_xor(self) -> global___BitXor: ...
    @property
    def div(self) -> global___Div: ...
    @property
    def eq(self) -> global___Eq: ...
    @property
    def geq(self) -> global___Geq: ...
    @property
    def gt(self) -> global___Gt: ...
    @property
    def leq(self) -> global___Leq: ...
    @property
    def lt(self) -> global___Lt: ...
    @property
    def mod(self) -> global___Mod: ...
    @property
    def mul(self) -> global___Mul: ...
    @property
    def neq(self) -> global___Neq: ...
    @property
    def pow(self) -> global___Pow: ...
    @property
    def sub(self) -> global___Sub: ...
    def __init__(
        self,
        *,
        add: global___Add | None = ...,
        bit_and: global___BitAnd | None = ...,
        bit_or: global___BitOr | None = ...,
        bit_xor: global___BitXor | None = ...,
        div: global___Div | None = ...,
        eq: global___Eq | None = ...,
        geq: global___Geq | None = ...,
        gt: global___Gt | None = ...,
        leq: global___Leq | None = ...,
        lt: global___Lt | None = ...,
        mod: global___Mod | None = ...,
        mul: global___Mul | None = ...,
        neq: global___Neq | None = ...,
        pow: global___Pow | None = ...,
        sub: global___Sub | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["add", b"add", "and", b"and", "bit_and", b"bit_and", "bit_or", b"bit_or", "bit_xor", b"bit_xor", "div", b"div", "eq", b"eq", "geq", b"geq", "gt", b"gt", "leq", b"leq", "lt", b"lt", "mod", b"mod", "mul", b"mul", "neq", b"neq", "or", b"or", "pow", b"pow", "sub", b"sub", "variant", b"variant"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["add", b"add", "and", b"and", "bit_and", b"bit_and", "bit_or", b"bit_or", "bit_xor", b"bit_xor", "div", b"div", "eq", b"eq", "geq", b"geq", "gt", b"gt", "leq", b"leq", "lt", b"lt", "mod", b"mod", "mul", b"mul", "neq", b"neq", "or", b"or", "pow", b"pow", "sub", b"sub", "variant", b"variant"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["variant", b"variant"]) -> typing.Literal["add", "and", "bit_and", "bit_or", "bit_xor", "div", "eq", "geq", "gt", "leq", "lt", "mod", "mul", "neq", "or", "pow", "sub"] | None: ...

global___BinOp = BinOp

@typing.final
class BinaryVal(google.protobuf.message.Message):
    """const.ir:45"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SRC_FIELD_NUMBER: builtins.int
    V_FIELD_NUMBER: builtins.int
    v: builtins.bytes
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        src: global___SrcPosition | None = ...,
        v: builtins.bytes = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["src", b"src", "v", b"v"]) -> None: ...

global___BinaryVal = BinaryVal

@typing.final
class Bind(google.protobuf.message.Message):
    """ast.ir:45"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    EXPR_FIELD_NUMBER: builtins.int
    FIRST_REQUEST_ID_FIELD_NUMBER: builtins.int
    SYMBOL_FIELD_NUMBER: builtins.int
    UID_FIELD_NUMBER: builtins.int
    first_request_id: builtins.bytes
    uid: builtins.int
    @property
    def expr(self) -> global___Expr: ...
    @property
    def symbol(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    def __init__(
        self,
        *,
        expr: global___Expr | None = ...,
        first_request_id: builtins.bytes = ...,
        symbol: google.protobuf.wrappers_pb2.StringValue | None = ...,
        uid: builtins.int = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["expr", b"expr", "symbol", b"symbol"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["expr", b"expr", "first_request_id", b"first_request_id", "symbol", b"symbol", "uid", b"uid"]) -> None: ...

global___Bind = Bind

@typing.final
class BitAnd(google.protobuf.message.Message):
    """op.ir:58"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LHS_FIELD_NUMBER: builtins.int
    RHS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def lhs(self) -> global___Expr: ...
    @property
    def rhs(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        lhs: global___Expr | None = ...,
        rhs: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> None: ...

global___BitAnd = BitAnd

@typing.final
class BitOr(google.protobuf.message.Message):
    """op.ir:60"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LHS_FIELD_NUMBER: builtins.int
    RHS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def lhs(self) -> global___Expr: ...
    @property
    def rhs(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        lhs: global___Expr | None = ...,
        rhs: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> None: ...

global___BitOr = BitOr

@typing.final
class BitXor(google.protobuf.message.Message):
    """op.ir:62"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LHS_FIELD_NUMBER: builtins.int
    RHS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def lhs(self) -> global___Expr: ...
    @property
    def rhs(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        lhs: global___Expr | None = ...,
        rhs: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> None: ...

global___BitXor = BitXor

@typing.final
class BoolVal(google.protobuf.message.Message):
    """const.ir:16"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SRC_FIELD_NUMBER: builtins.int
    V_FIELD_NUMBER: builtins.int
    v: builtins.bool
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        src: global___SrcPosition | None = ...,
        v: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["src", b"src", "v", b"v"]) -> None: ...

global___BoolVal = BoolVal

@typing.final
class BuiltinFn(google.protobuf.message.Message):
    """fn.ir:23"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def name(self) -> global___NameRef: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        name: global___NameRef | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["name", b"name", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["name", b"name", "src", b"src"]) -> None: ...

global___BuiltinFn = BuiltinFn

@typing.final
class CallTableFunctionExpr(google.protobuf.message.Message):
    """fn.ir:149"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def name(self) -> global___NameRef: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        name: global___NameRef | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["name", b"name", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["name", b"name", "src", b"src"]) -> None: ...

global___CallTableFunctionExpr = CallTableFunctionExpr

@typing.final
class ColumnAlias(google.protobuf.message.Message):
    """column.ir:26"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_FIELD_NUMBER: builtins.int
    FN_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    name: builtins.str
    @property
    def col(self) -> global___Expr: ...
    @property
    def fn(self) -> global___ColumnAliasFn: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        col: global___Expr | None = ...,
        fn: global___ColumnAliasFn | None = ...,
        name: builtins.str = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col", b"col", "fn", b"fn", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col", b"col", "fn", b"fn", "name", b"name", "src", b"src"]) -> None: ...

global___ColumnAlias = ColumnAlias

@typing.final
class ColumnApply_Int(google.protobuf.message.Message):
    """column.ir:33"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_FIELD_NUMBER: builtins.int
    IDX_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    idx: builtins.int
    @property
    def col(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        col: global___Expr | None = ...,
        idx: builtins.int = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col", b"col", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col", b"col", "idx", b"idx", "src", b"src"]) -> None: ...

global___ColumnApply_Int = ColumnApply_Int

@typing.final
class ColumnApply_String(google.protobuf.message.Message):
    """column.ir:37"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_FIELD_NUMBER: builtins.int
    FIELD_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    field: builtins.str
    @property
    def col(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        col: global___Expr | None = ...,
        field: builtins.str = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col", b"col", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col", b"col", "field", b"field", "src", b"src"]) -> None: ...

global___ColumnApply_String = ColumnApply_String

@typing.final
class ColumnAsc(google.protobuf.message.Message):
    """column.ir:41"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_FIELD_NUMBER: builtins.int
    NULL_ORDER_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def col(self) -> global___Expr: ...
    @property
    def null_order(self) -> global___NullOrder: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        col: global___Expr | None = ...,
        null_order: global___NullOrder | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col", b"col", "null_order", b"null_order", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col", b"col", "null_order", b"null_order", "src", b"src"]) -> None: ...

global___ColumnAsc = ColumnAsc

@typing.final
class ColumnBetween(google.protobuf.message.Message):
    """column.ir:45"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_FIELD_NUMBER: builtins.int
    LOWER_BOUND_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    UPPER_BOUND_FIELD_NUMBER: builtins.int
    @property
    def col(self) -> global___Expr: ...
    @property
    def lower_bound(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def upper_bound(self) -> global___Expr: ...
    def __init__(
        self,
        *,
        col: global___Expr | None = ...,
        lower_bound: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        upper_bound: global___Expr | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col", b"col", "lower_bound", b"lower_bound", "src", b"src", "upper_bound", b"upper_bound"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col", b"col", "lower_bound", b"lower_bound", "src", b"src", "upper_bound", b"upper_bound"]) -> None: ...

global___ColumnBetween = ColumnBetween

@typing.final
class ColumnCaseExpr(google.protobuf.message.Message):
    """column.ir:12"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CASES_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def cases(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ColumnCaseExprClause]: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        cases: collections.abc.Iterable[global___ColumnCaseExprClause] | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["cases", b"cases", "src", b"src"]) -> None: ...

global___ColumnCaseExpr = ColumnCaseExpr

@typing.final
class ColumnCaseExprClause(google.protobuf.message.Message):
    """column.ir:16"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CONDITION_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    VALUE_FIELD_NUMBER: builtins.int
    @property
    def condition(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def value(self) -> global___Expr: ...
    def __init__(
        self,
        *,
        condition: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        value: global___Expr | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["condition", b"condition", "src", b"src", "value", b"value"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["condition", b"condition", "src", b"src", "value", b"value"]) -> None: ...

global___ColumnCaseExprClause = ColumnCaseExprClause

@typing.final
class ColumnCast(google.protobuf.message.Message):
    """column.ir:50"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    TO_FIELD_NUMBER: builtins.int
    @property
    def col(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def to(self) -> global___DataType: ...
    def __init__(
        self,
        *,
        col: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        to: global___DataType | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col", b"col", "src", b"src", "to", b"to"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col", b"col", "src", b"src", "to", b"to"]) -> None: ...

global___ColumnCast = ColumnCast

@typing.final
class ColumnDesc(google.protobuf.message.Message):
    """column.ir:58"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_FIELD_NUMBER: builtins.int
    NULL_ORDER_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def col(self) -> global___Expr: ...
    @property
    def null_order(self) -> global___NullOrder: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        col: global___Expr | None = ...,
        null_order: global___NullOrder | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col", b"col", "null_order", b"null_order", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col", b"col", "null_order", b"null_order", "src", b"src"]) -> None: ...

global___ColumnDesc = ColumnDesc

@typing.final
class ColumnEqualNan(google.protobuf.message.Message):
    """column.ir:64"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def col(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        col: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col", b"col", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col", b"col", "src", b"src"]) -> None: ...

global___ColumnEqualNan = ColumnEqualNan

@typing.final
class ColumnEqualNull(google.protobuf.message.Message):
    """column.ir:66"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LHS_FIELD_NUMBER: builtins.int
    RHS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def lhs(self) -> global___Expr: ...
    @property
    def rhs(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        lhs: global___Expr | None = ...,
        rhs: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> None: ...

global___ColumnEqualNull = ColumnEqualNull

@typing.final
class ColumnFn(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLUMN_ALIAS_FIELD_NUMBER: builtins.int
    COLUMN_APPLY__INT_FIELD_NUMBER: builtins.int
    COLUMN_APPLY__STRING_FIELD_NUMBER: builtins.int
    COLUMN_ASC_FIELD_NUMBER: builtins.int
    COLUMN_BETWEEN_FIELD_NUMBER: builtins.int
    COLUMN_CAST_FIELD_NUMBER: builtins.int
    COLUMN_DESC_FIELD_NUMBER: builtins.int
    COLUMN_EQUAL_NAN_FIELD_NUMBER: builtins.int
    COLUMN_IN_FIELD_NUMBER: builtins.int
    COLUMN_IS_NOT_NULL_FIELD_NUMBER: builtins.int
    COLUMN_IS_NULL_FIELD_NUMBER: builtins.int
    COLUMN_OVER_FIELD_NUMBER: builtins.int
    COLUMN_REGEXP_FIELD_NUMBER: builtins.int
    COLUMN_STRING_COLLATE_FIELD_NUMBER: builtins.int
    COLUMN_STRING_CONTAINS_FIELD_NUMBER: builtins.int
    COLUMN_STRING_ENDS_WITH_FIELD_NUMBER: builtins.int
    COLUMN_STRING_LIKE_FIELD_NUMBER: builtins.int
    COLUMN_STRING_STARTS_WITH_FIELD_NUMBER: builtins.int
    COLUMN_STRING_SUBSTR_FIELD_NUMBER: builtins.int
    COLUMN_TRY_CAST_FIELD_NUMBER: builtins.int
    COLUMN_WITHIN_GROUP_FIELD_NUMBER: builtins.int
    @property
    def column_alias(self) -> global___ColumnAlias: ...
    @property
    def column_apply__int(self) -> global___ColumnApply_Int: ...
    @property
    def column_apply__string(self) -> global___ColumnApply_String: ...
    @property
    def column_asc(self) -> global___ColumnAsc: ...
    @property
    def column_between(self) -> global___ColumnBetween: ...
    @property
    def column_cast(self) -> global___ColumnCast: ...
    @property
    def column_desc(self) -> global___ColumnDesc: ...
    @property
    def column_equal_nan(self) -> global___ColumnEqualNan: ...
    @property
    def column_in(self) -> global___ColumnIn: ...
    @property
    def column_is_not_null(self) -> global___ColumnIsNotNull: ...
    @property
    def column_is_null(self) -> global___ColumnIsNull: ...
    @property
    def column_over(self) -> global___ColumnOver: ...
    @property
    def column_regexp(self) -> global___ColumnRegexp: ...
    @property
    def column_string_collate(self) -> global___ColumnStringCollate: ...
    @property
    def column_string_contains(self) -> global___ColumnStringContains: ...
    @property
    def column_string_ends_with(self) -> global___ColumnStringEndsWith: ...
    @property
    def column_string_like(self) -> global___ColumnStringLike: ...
    @property
    def column_string_starts_with(self) -> global___ColumnStringStartsWith: ...
    @property
    def column_string_substr(self) -> global___ColumnStringSubstr: ...
    @property
    def column_try_cast(self) -> global___ColumnTryCast: ...
    @property
    def column_within_group(self) -> global___ColumnWithinGroup: ...
    def __init__(
        self,
        *,
        column_alias: global___ColumnAlias | None = ...,
        column_apply__int: global___ColumnApply_Int | None = ...,
        column_apply__string: global___ColumnApply_String | None = ...,
        column_asc: global___ColumnAsc | None = ...,
        column_between: global___ColumnBetween | None = ...,
        column_cast: global___ColumnCast | None = ...,
        column_desc: global___ColumnDesc | None = ...,
        column_equal_nan: global___ColumnEqualNan | None = ...,
        column_in: global___ColumnIn | None = ...,
        column_is_not_null: global___ColumnIsNotNull | None = ...,
        column_is_null: global___ColumnIsNull | None = ...,
        column_over: global___ColumnOver | None = ...,
        column_regexp: global___ColumnRegexp | None = ...,
        column_string_collate: global___ColumnStringCollate | None = ...,
        column_string_contains: global___ColumnStringContains | None = ...,
        column_string_ends_with: global___ColumnStringEndsWith | None = ...,
        column_string_like: global___ColumnStringLike | None = ...,
        column_string_starts_with: global___ColumnStringStartsWith | None = ...,
        column_string_substr: global___ColumnStringSubstr | None = ...,
        column_try_cast: global___ColumnTryCast | None = ...,
        column_within_group: global___ColumnWithinGroup | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["column_alias", b"column_alias", "column_apply__int", b"column_apply__int", "column_apply__string", b"column_apply__string", "column_asc", b"column_asc", "column_between", b"column_between", "column_cast", b"column_cast", "column_desc", b"column_desc", "column_equal_nan", b"column_equal_nan", "column_in", b"column_in", "column_is_not_null", b"column_is_not_null", "column_is_null", b"column_is_null", "column_over", b"column_over", "column_regexp", b"column_regexp", "column_string_collate", b"column_string_collate", "column_string_contains", b"column_string_contains", "column_string_ends_with", b"column_string_ends_with", "column_string_like", b"column_string_like", "column_string_starts_with", b"column_string_starts_with", "column_string_substr", b"column_string_substr", "column_try_cast", b"column_try_cast", "column_within_group", b"column_within_group", "variant", b"variant"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["column_alias", b"column_alias", "column_apply__int", b"column_apply__int", "column_apply__string", b"column_apply__string", "column_asc", b"column_asc", "column_between", b"column_between", "column_cast", b"column_cast", "column_desc", b"column_desc", "column_equal_nan", b"column_equal_nan", "column_in", b"column_in", "column_is_not_null", b"column_is_not_null", "column_is_null", b"column_is_null", "column_over", b"column_over", "column_regexp", b"column_regexp", "column_string_collate", b"column_string_collate", "column_string_contains", b"column_string_contains", "column_string_ends_with", b"column_string_ends_with", "column_string_like", b"column_string_like", "column_string_starts_with", b"column_string_starts_with", "column_string_substr", b"column_string_substr", "column_try_cast", b"column_try_cast", "column_within_group", b"column_within_group", "variant", b"variant"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["variant", b"variant"]) -> typing.Literal["column_alias", "column_apply__int", "column_apply__string", "column_asc", "column_between", "column_cast", "column_desc", "column_equal_nan", "column_in", "column_is_not_null", "column_is_null", "column_over", "column_regexp", "column_string_collate", "column_string_contains", "column_string_ends_with", "column_string_like", "column_string_starts_with", "column_string_substr", "column_try_cast", "column_within_group"] | None: ...

global___ColumnFn = ColumnFn

@typing.final
class ColumnIn(google.protobuf.message.Message):
    """column.ir:71"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    VALUES_FIELD_NUMBER: builtins.int
    @property
    def col(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def values(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Expr]: ...
    def __init__(
        self,
        *,
        col: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        values: collections.abc.Iterable[global___Expr] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col", b"col", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col", b"col", "src", b"src", "values", b"values"]) -> None: ...

global___ColumnIn = ColumnIn

@typing.final
class ColumnIsNotNull(google.protobuf.message.Message):
    """column.ir:75"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def col(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        col: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col", b"col", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col", b"col", "src", b"src"]) -> None: ...

global___ColumnIsNotNull = ColumnIsNotNull

@typing.final
class ColumnIsNull(google.protobuf.message.Message):
    """column.ir:77"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def col(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        col: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col", b"col", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col", b"col", "src", b"src"]) -> None: ...

global___ColumnIsNull = ColumnIsNull

@typing.final
class ColumnOver(google.protobuf.message.Message):
    """column.ir:79"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    WINDOW_SPEC_FIELD_NUMBER: builtins.int
    @property
    def col(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def window_spec(self) -> global___WindowSpecExpr: ...
    def __init__(
        self,
        *,
        col: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        window_spec: global___WindowSpecExpr | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col", b"col", "src", b"src", "window_spec", b"window_spec"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col", b"col", "src", b"src", "window_spec", b"window_spec"]) -> None: ...

global___ColumnOver = ColumnOver

@typing.final
class ColumnRegexp(google.protobuf.message.Message):
    """column.ir:83"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_FIELD_NUMBER: builtins.int
    PARAMETERS_FIELD_NUMBER: builtins.int
    PATTERN_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def col(self) -> global___Expr: ...
    @property
    def parameters(self) -> global___Expr: ...
    @property
    def pattern(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        col: global___Expr | None = ...,
        parameters: global___Expr | None = ...,
        pattern: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col", b"col", "parameters", b"parameters", "pattern", b"pattern", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col", b"col", "parameters", b"parameters", "pattern", b"pattern", "src", b"src"]) -> None: ...

global___ColumnRegexp = ColumnRegexp

@typing.final
class ColumnStringCollate(google.protobuf.message.Message):
    """column.ir:105"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_FIELD_NUMBER: builtins.int
    COLLATION_SPEC_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def col(self) -> global___Expr: ...
    @property
    def collation_spec(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        col: global___Expr | None = ...,
        collation_spec: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col", b"col", "collation_spec", b"collation_spec", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col", b"col", "collation_spec", b"collation_spec", "src", b"src"]) -> None: ...

global___ColumnStringCollate = ColumnStringCollate

@typing.final
class ColumnStringContains(google.protobuf.message.Message):
    """column.ir:109"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_FIELD_NUMBER: builtins.int
    PATTERN_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def col(self) -> global___Expr: ...
    @property
    def pattern(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        col: global___Expr | None = ...,
        pattern: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col", b"col", "pattern", b"pattern", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col", b"col", "pattern", b"pattern", "src", b"src"]) -> None: ...

global___ColumnStringContains = ColumnStringContains

@typing.final
class ColumnStringEndsWith(google.protobuf.message.Message):
    """column.ir:96"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    SUFFIX_FIELD_NUMBER: builtins.int
    @property
    def col(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def suffix(self) -> global___Expr: ...
    def __init__(
        self,
        *,
        col: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        suffix: global___Expr | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col", b"col", "src", b"src", "suffix", b"suffix"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col", b"col", "src", b"src", "suffix", b"suffix"]) -> None: ...

global___ColumnStringEndsWith = ColumnStringEndsWith

@typing.final
class ColumnStringLike(google.protobuf.message.Message):
    """column.ir:88"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_FIELD_NUMBER: builtins.int
    PATTERN_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def col(self) -> global___Expr: ...
    @property
    def pattern(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        col: global___Expr | None = ...,
        pattern: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col", b"col", "pattern", b"pattern", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col", b"col", "pattern", b"pattern", "src", b"src"]) -> None: ...

global___ColumnStringLike = ColumnStringLike

@typing.final
class ColumnStringStartsWith(google.protobuf.message.Message):
    """column.ir:92"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_FIELD_NUMBER: builtins.int
    PREFIX_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def col(self) -> global___Expr: ...
    @property
    def prefix(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        col: global___Expr | None = ...,
        prefix: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col", b"col", "prefix", b"prefix", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col", b"col", "prefix", b"prefix", "src", b"src"]) -> None: ...

global___ColumnStringStartsWith = ColumnStringStartsWith

@typing.final
class ColumnStringSubstr(google.protobuf.message.Message):
    """column.ir:100"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_FIELD_NUMBER: builtins.int
    LEN_FIELD_NUMBER: builtins.int
    POS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def col(self) -> global___Expr: ...
    @property
    def len(self) -> global___Expr: ...
    @property
    def pos(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        col: global___Expr | None = ...,
        len: global___Expr | None = ...,
        pos: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col", b"col", "len", b"len", "pos", b"pos", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col", b"col", "len", b"len", "pos", b"pos", "src", b"src"]) -> None: ...

global___ColumnStringSubstr = ColumnStringSubstr

@typing.final
class ColumnTryCast(google.protobuf.message.Message):
    """column.ir:54"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    TO_FIELD_NUMBER: builtins.int
    @property
    def col(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def to(self) -> global___DataType: ...
    def __init__(
        self,
        *,
        col: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        to: global___DataType | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col", b"col", "src", b"src", "to", b"to"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col", b"col", "src", b"src", "to", b"to"]) -> None: ...

global___ColumnTryCast = ColumnTryCast

@typing.final
class ColumnWithinGroup(google.protobuf.message.Message):
    """column.ir:113"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_FIELD_NUMBER: builtins.int
    COLS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def col(self) -> global___Expr: ...
    @property
    def cols(self) -> global___ExprArgList: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        col: global___Expr | None = ...,
        cols: global___ExprArgList | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col", b"col", "cols", b"cols", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col", b"col", "cols", b"cols", "src", b"src"]) -> None: ...

global___ColumnWithinGroup = ColumnWithinGroup

@typing.final
class Const(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BIG_DECIMAL_VAL_FIELD_NUMBER: builtins.int
    BIG_INT_VAL_FIELD_NUMBER: builtins.int
    BINARY_VAL_FIELD_NUMBER: builtins.int
    BOOL_VAL_FIELD_NUMBER: builtins.int
    DATATYPE_VAL_FIELD_NUMBER: builtins.int
    FLOAT64_VAL_FIELD_NUMBER: builtins.int
    INT64_VAL_FIELD_NUMBER: builtins.int
    NULL_VAL_FIELD_NUMBER: builtins.int
    PYTHON_DATE_VAL_FIELD_NUMBER: builtins.int
    PYTHON_TIME_VAL_FIELD_NUMBER: builtins.int
    PYTHON_TIMESTAMP_VAL_FIELD_NUMBER: builtins.int
    REDACTED_CONST_FIELD_NUMBER: builtins.int
    STRING_VAL_FIELD_NUMBER: builtins.int
    @property
    def big_decimal_val(self) -> global___BigDecimalVal: ...
    @property
    def big_int_val(self) -> global___BigIntVal: ...
    @property
    def binary_val(self) -> global___BinaryVal: ...
    @property
    def bool_val(self) -> global___BoolVal: ...
    @property
    def datatype_val(self) -> global___DatatypeVal: ...
    @property
    def float64_val(self) -> global___Float64Val: ...
    @property
    def int64_val(self) -> global___Int64Val: ...
    @property
    def null_val(self) -> global___NullVal: ...
    @property
    def python_date_val(self) -> global___PythonDateVal: ...
    @property
    def python_time_val(self) -> global___PythonTimeVal: ...
    @property
    def python_timestamp_val(self) -> global___PythonTimestampVal: ...
    @property
    def redacted_const(self) -> global___RedactedConst: ...
    @property
    def string_val(self) -> global___StringVal: ...
    def __init__(
        self,
        *,
        big_decimal_val: global___BigDecimalVal | None = ...,
        big_int_val: global___BigIntVal | None = ...,
        binary_val: global___BinaryVal | None = ...,
        bool_val: global___BoolVal | None = ...,
        datatype_val: global___DatatypeVal | None = ...,
        float64_val: global___Float64Val | None = ...,
        int64_val: global___Int64Val | None = ...,
        null_val: global___NullVal | None = ...,
        python_date_val: global___PythonDateVal | None = ...,
        python_time_val: global___PythonTimeVal | None = ...,
        python_timestamp_val: global___PythonTimestampVal | None = ...,
        redacted_const: global___RedactedConst | None = ...,
        string_val: global___StringVal | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["big_decimal_val", b"big_decimal_val", "big_int_val", b"big_int_val", "binary_val", b"binary_val", "bool_val", b"bool_val", "datatype_val", b"datatype_val", "float64_val", b"float64_val", "int64_val", b"int64_val", "null_val", b"null_val", "python_date_val", b"python_date_val", "python_time_val", b"python_time_val", "python_timestamp_val", b"python_timestamp_val", "redacted_const", b"redacted_const", "string_val", b"string_val", "variant", b"variant"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["big_decimal_val", b"big_decimal_val", "big_int_val", b"big_int_val", "binary_val", b"binary_val", "bool_val", b"bool_val", "datatype_val", b"datatype_val", "float64_val", b"float64_val", "int64_val", b"int64_val", "null_val", b"null_val", "python_date_val", b"python_date_val", "python_time_val", b"python_time_val", "python_timestamp_val", b"python_timestamp_val", "redacted_const", b"redacted_const", "string_val", b"string_val", "variant", b"variant"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["variant", b"variant"]) -> typing.Literal["big_decimal_val", "big_int_val", "binary_val", "bool_val", "datatype_val", "float64_val", "int64_val", "null_val", "python_date_val", "python_time_val", "python_timestamp_val", "redacted_const", "string_val"] | None: ...

global___Const = Const

@typing.final
class CreateDataframe(google.protobuf.message.Message):
    """dataframe.ir:39"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATA_FIELD_NUMBER: builtins.int
    SCHEMA_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def data(self) -> global___DataframeData: ...
    @property
    def schema(self) -> global___DataframeSchema: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        data: global___DataframeData | None = ...,
        schema: global___DataframeSchema | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["data", b"data", "schema", b"schema", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["data", b"data", "schema", b"schema", "src", b"src"]) -> None: ...

global___CreateDataframe = CreateDataframe

@typing.final
class DataframeAgg(google.protobuf.message.Message):
    """dataframe.ir:165"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DF_FIELD_NUMBER: builtins.int
    EXPRS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def df(self) -> global___Expr: ...
    @property
    def exprs(self) -> global___ExprArgList: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        df: global___Expr | None = ...,
        exprs: global___ExprArgList | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "exprs", b"exprs", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["df", b"df", "exprs", b"exprs", "src", b"src"]) -> None: ...

global___DataframeAgg = DataframeAgg

@typing.final
class DataframeAlias(google.protobuf.message.Message):
    """dataframe.ir:170"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DF_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    name: builtins.str
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        df: global___Expr | None = ...,
        name: builtins.str = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["df", b"df", "name", b"name", "src", b"src"]) -> None: ...

global___DataframeAlias = DataframeAlias

@typing.final
class DataframeAnalyticsComputeLag(google.protobuf.message.Message):
    """dataframe-analytics.ir:27"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLS_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    FORMATTED_COL_NAMES_FIELD_NUMBER: builtins.int
    GROUP_BY_FIELD_NUMBER: builtins.int
    LAGS_FIELD_NUMBER: builtins.int
    ORDER_BY_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def cols(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Expr]: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def formatted_col_names(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def group_by(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def lags(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def order_by(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        cols: collections.abc.Iterable[global___Expr] | None = ...,
        df: global___Expr | None = ...,
        formatted_col_names: collections.abc.Iterable[builtins.str] | None = ...,
        group_by: collections.abc.Iterable[builtins.str] | None = ...,
        lags: collections.abc.Iterable[builtins.int] | None = ...,
        order_by: collections.abc.Iterable[builtins.str] | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["cols", b"cols", "df", b"df", "formatted_col_names", b"formatted_col_names", "group_by", b"group_by", "lags", b"lags", "order_by", b"order_by", "src", b"src"]) -> None: ...

global___DataframeAnalyticsComputeLag = DataframeAnalyticsComputeLag

@typing.final
class DataframeAnalyticsComputeLead(google.protobuf.message.Message):
    """dataframe-analytics.ir:36"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLS_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    FORMATTED_COL_NAMES_FIELD_NUMBER: builtins.int
    GROUP_BY_FIELD_NUMBER: builtins.int
    LEADS_FIELD_NUMBER: builtins.int
    ORDER_BY_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def cols(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Expr]: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def formatted_col_names(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def group_by(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def leads(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def order_by(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        cols: collections.abc.Iterable[global___Expr] | None = ...,
        df: global___Expr | None = ...,
        formatted_col_names: collections.abc.Iterable[builtins.str] | None = ...,
        group_by: collections.abc.Iterable[builtins.str] | None = ...,
        leads: collections.abc.Iterable[builtins.int] | None = ...,
        order_by: collections.abc.Iterable[builtins.str] | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["cols", b"cols", "df", b"df", "formatted_col_names", b"formatted_col_names", "group_by", b"group_by", "leads", b"leads", "order_by", b"order_by", "src", b"src"]) -> None: ...

global___DataframeAnalyticsComputeLead = DataframeAnalyticsComputeLead

@typing.final
class DataframeAnalyticsCumulativeAgg(google.protobuf.message.Message):
    """dataframe-analytics.ir:18"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    AGGS_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    FORMATTED_COL_NAMES_FIELD_NUMBER: builtins.int
    GROUP_BY_FIELD_NUMBER: builtins.int
    IS_FORWARD_FIELD_NUMBER: builtins.int
    ORDER_BY_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    is_forward: builtins.bool
    @property
    def aggs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_List_String]: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def formatted_col_names(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def group_by(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def order_by(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        aggs: collections.abc.Iterable[global___Tuple_String_List_String] | None = ...,
        df: global___Expr | None = ...,
        formatted_col_names: collections.abc.Iterable[builtins.str] | None = ...,
        group_by: collections.abc.Iterable[builtins.str] | None = ...,
        is_forward: builtins.bool = ...,
        order_by: collections.abc.Iterable[builtins.str] | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["aggs", b"aggs", "df", b"df", "formatted_col_names", b"formatted_col_names", "group_by", b"group_by", "is_forward", b"is_forward", "order_by", b"order_by", "src", b"src"]) -> None: ...

global___DataframeAnalyticsCumulativeAgg = DataframeAnalyticsCumulativeAgg

@typing.final
class DataframeAnalyticsMovingAgg(google.protobuf.message.Message):
    """dataframe-analytics.ir:9"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    AGGS_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    FORMATTED_COL_NAMES_FIELD_NUMBER: builtins.int
    GROUP_BY_FIELD_NUMBER: builtins.int
    ORDER_BY_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    WINDOW_SIZES_FIELD_NUMBER: builtins.int
    @property
    def aggs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_List_String]: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def formatted_col_names(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def group_by(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def order_by(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def window_sizes(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    def __init__(
        self,
        *,
        aggs: collections.abc.Iterable[global___Tuple_String_List_String] | None = ...,
        df: global___Expr | None = ...,
        formatted_col_names: collections.abc.Iterable[builtins.str] | None = ...,
        group_by: collections.abc.Iterable[builtins.str] | None = ...,
        order_by: collections.abc.Iterable[builtins.str] | None = ...,
        src: global___SrcPosition | None = ...,
        window_sizes: collections.abc.Iterable[builtins.int] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["aggs", b"aggs", "df", b"df", "formatted_col_names", b"formatted_col_names", "group_by", b"group_by", "order_by", b"order_by", "src", b"src", "window_sizes", b"window_sizes"]) -> None: ...

global___DataframeAnalyticsMovingAgg = DataframeAnalyticsMovingAgg

@typing.final
class DataframeAnalyticsTimeSeriesAgg(google.protobuf.message.Message):
    """dataframe-analytics.ir:45"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    AGGS_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    FORMATTED_COL_NAMES_FIELD_NUMBER: builtins.int
    GROUP_BY_FIELD_NUMBER: builtins.int
    SLIDING_INTERVAL_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    TIME_COL_FIELD_NUMBER: builtins.int
    WINDOWS_FIELD_NUMBER: builtins.int
    sliding_interval: builtins.str
    time_col: builtins.str
    @property
    def aggs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_List_String]: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def formatted_col_names(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def group_by(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def windows(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    def __init__(
        self,
        *,
        aggs: collections.abc.Iterable[global___Tuple_String_List_String] | None = ...,
        df: global___Expr | None = ...,
        formatted_col_names: collections.abc.Iterable[builtins.str] | None = ...,
        group_by: collections.abc.Iterable[builtins.str] | None = ...,
        sliding_interval: builtins.str = ...,
        src: global___SrcPosition | None = ...,
        time_col: builtins.str = ...,
        windows: collections.abc.Iterable[builtins.str] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["aggs", b"aggs", "df", b"df", "formatted_col_names", b"formatted_col_names", "group_by", b"group_by", "sliding_interval", b"sliding_interval", "src", b"src", "time_col", b"time_col", "windows", b"windows"]) -> None: ...

global___DataframeAnalyticsTimeSeriesAgg = DataframeAnalyticsTimeSeriesAgg

@typing.final
class DataframeCacheResult(google.protobuf.message.Message):
    """dataframe.ir:349"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DF_FIELD_NUMBER: builtins.int
    OBJECT_NAME_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    @property
    def df(self) -> global___Expr: ...
    @property
    def object_name(self) -> global___NameRef: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    def __init__(
        self,
        *,
        df: global___Expr | None = ...,
        object_name: global___NameRef | None = ...,
        src: global___SrcPosition | None = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "object_name", b"object_name", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["df", b"df", "object_name", b"object_name", "src", b"src", "statement_params", b"statement_params"]) -> None: ...

global___DataframeCacheResult = DataframeCacheResult

@typing.final
class DataframeCol(google.protobuf.message.Message):
    """column.ir:1"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_NAME_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    col_name: builtins.str
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        col_name: builtins.str = ...,
        df: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col_name", b"col_name", "df", b"df", "src", b"src"]) -> None: ...

global___DataframeCol = DataframeCol

@typing.final
class DataframeCollect(google.protobuf.message.Message):
    """dataframe.ir:19"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BLOCK_FIELD_NUMBER: builtins.int
    CASE_SENSITIVE_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    LOG_ON_EXCEPTION_FIELD_NUMBER: builtins.int
    NO_WAIT_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    block: builtins.bool
    case_sensitive: builtins.bool
    log_on_exception: builtins.bool
    no_wait: builtins.bool
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    def __init__(
        self,
        *,
        block: builtins.bool = ...,
        case_sensitive: builtins.bool = ...,
        df: global___Expr | None = ...,
        log_on_exception: builtins.bool = ...,
        no_wait: builtins.bool = ...,
        src: global___SrcPosition | None = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["block", b"block", "case_sensitive", b"case_sensitive", "df", b"df", "log_on_exception", b"log_on_exception", "no_wait", b"no_wait", "src", b"src", "statement_params", b"statement_params"]) -> None: ...

global___DataframeCollect = DataframeCollect

@typing.final
class DataframeCopyIntoTable(google.protobuf.message.Message):
    """dataframe-io.ir:148"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COPY_OPTIONS_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    FILES_FIELD_NUMBER: builtins.int
    FORMAT_TYPE_OPTIONS_FIELD_NUMBER: builtins.int
    ICEBERG_CONFIG_FIELD_NUMBER: builtins.int
    PATTERN_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    TABLE_NAME_FIELD_NUMBER: builtins.int
    TARGET_COLUMNS_FIELD_NUMBER: builtins.int
    TRANSFORMATIONS_FIELD_NUMBER: builtins.int
    VALIDATION_MODE_FIELD_NUMBER: builtins.int
    @property
    def copy_options(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_Expr]: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def files(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def format_type_options(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_Expr]: ...
    @property
    def iceberg_config(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_Expr]: ...
    @property
    def pattern(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    @property
    def table_name(self) -> global___NameRef: ...
    @property
    def target_columns(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def transformations(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Expr]: ...
    @property
    def validation_mode(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    def __init__(
        self,
        *,
        copy_options: collections.abc.Iterable[global___Tuple_String_Expr] | None = ...,
        df: global___Expr | None = ...,
        files: collections.abc.Iterable[builtins.str] | None = ...,
        format_type_options: collections.abc.Iterable[global___Tuple_String_Expr] | None = ...,
        iceberg_config: collections.abc.Iterable[global___Tuple_String_Expr] | None = ...,
        pattern: google.protobuf.wrappers_pb2.StringValue | None = ...,
        src: global___SrcPosition | None = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
        table_name: global___NameRef | None = ...,
        target_columns: collections.abc.Iterable[builtins.str] | None = ...,
        transformations: collections.abc.Iterable[global___Expr] | None = ...,
        validation_mode: google.protobuf.wrappers_pb2.StringValue | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "pattern", b"pattern", "src", b"src", "table_name", b"table_name", "validation_mode", b"validation_mode"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["copy_options", b"copy_options", "df", b"df", "files", b"files", "format_type_options", b"format_type_options", "iceberg_config", b"iceberg_config", "pattern", b"pattern", "src", b"src", "statement_params", b"statement_params", "table_name", b"table_name", "target_columns", b"target_columns", "transformations", b"transformations", "validation_mode", b"validation_mode"]) -> None: ...

global___DataframeCopyIntoTable = DataframeCopyIntoTable

@typing.final
class DataframeCount(google.protobuf.message.Message):
    """dataframe.ir:13"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BLOCK_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    block: builtins.bool
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    def __init__(
        self,
        *,
        block: builtins.bool = ...,
        df: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["block", b"block", "df", b"df", "src", b"src", "statement_params", b"statement_params"]) -> None: ...

global___DataframeCount = DataframeCount

@typing.final
class DataframeCreateOrReplaceDynamicTable(google.protobuf.message.Message):
    """dataframe-io.ir:132"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CLUSTERING_KEYS_FIELD_NUMBER: builtins.int
    COMMENT_FIELD_NUMBER: builtins.int
    DATA_RETENTION_TIME_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    INITIALIZE_FIELD_NUMBER: builtins.int
    IS_TRANSIENT_FIELD_NUMBER: builtins.int
    LAG_FIELD_NUMBER: builtins.int
    MAX_DATA_EXTENSION_TIME_FIELD_NUMBER: builtins.int
    MODE_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    REFRESH_MODE_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    WAREHOUSE_FIELD_NUMBER: builtins.int
    is_transient: builtins.bool
    lag: builtins.str
    warehouse: builtins.str
    @property
    def clustering_keys(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Expr]: ...
    @property
    def comment(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def data_retention_time(self) -> google.protobuf.wrappers_pb2.Int64Value: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def initialize(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def max_data_extension_time(self) -> google.protobuf.wrappers_pb2.Int64Value: ...
    @property
    def mode(self) -> global___SaveMode: ...
    @property
    def name(self) -> global___NameRef: ...
    @property
    def refresh_mode(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    def __init__(
        self,
        *,
        clustering_keys: collections.abc.Iterable[global___Expr] | None = ...,
        comment: google.protobuf.wrappers_pb2.StringValue | None = ...,
        data_retention_time: google.protobuf.wrappers_pb2.Int64Value | None = ...,
        df: global___Expr | None = ...,
        initialize: google.protobuf.wrappers_pb2.StringValue | None = ...,
        is_transient: builtins.bool = ...,
        lag: builtins.str = ...,
        max_data_extension_time: google.protobuf.wrappers_pb2.Int64Value | None = ...,
        mode: global___SaveMode | None = ...,
        name: global___NameRef | None = ...,
        refresh_mode: google.protobuf.wrappers_pb2.StringValue | None = ...,
        src: global___SrcPosition | None = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
        warehouse: builtins.str = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["comment", b"comment", "data_retention_time", b"data_retention_time", "df", b"df", "initialize", b"initialize", "max_data_extension_time", b"max_data_extension_time", "mode", b"mode", "name", b"name", "refresh_mode", b"refresh_mode", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["clustering_keys", b"clustering_keys", "comment", b"comment", "data_retention_time", b"data_retention_time", "df", b"df", "initialize", b"initialize", "is_transient", b"is_transient", "lag", b"lag", "max_data_extension_time", b"max_data_extension_time", "mode", b"mode", "name", b"name", "refresh_mode", b"refresh_mode", "src", b"src", "statement_params", b"statement_params", "warehouse", b"warehouse"]) -> None: ...

global___DataframeCreateOrReplaceDynamicTable = DataframeCreateOrReplaceDynamicTable

@typing.final
class DataframeCreateOrReplaceView(google.protobuf.message.Message):
    """dataframe-io.ir:124"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COMMENT_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    IS_TEMP_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    is_temp: builtins.bool
    @property
    def comment(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def name(self) -> global___NameRef: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    def __init__(
        self,
        *,
        comment: google.protobuf.wrappers_pb2.StringValue | None = ...,
        df: global___Expr | None = ...,
        is_temp: builtins.bool = ...,
        name: global___NameRef | None = ...,
        src: global___SrcPosition | None = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["comment", b"comment", "df", b"df", "name", b"name", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["comment", b"comment", "df", b"df", "is_temp", b"is_temp", "name", b"name", "src", b"src", "statement_params", b"statement_params"]) -> None: ...

global___DataframeCreateOrReplaceView = DataframeCreateOrReplaceView

@typing.final
class DataframeCrossJoin(google.protobuf.message.Message):
    """dataframe.ir:175"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LHS_FIELD_NUMBER: builtins.int
    LSUFFIX_FIELD_NUMBER: builtins.int
    RHS_FIELD_NUMBER: builtins.int
    RSUFFIX_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def lhs(self) -> global___Expr: ...
    @property
    def lsuffix(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def rhs(self) -> global___Expr: ...
    @property
    def rsuffix(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        lhs: global___Expr | None = ...,
        lsuffix: google.protobuf.wrappers_pb2.StringValue | None = ...,
        rhs: global___Expr | None = ...,
        rsuffix: google.protobuf.wrappers_pb2.StringValue | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["lhs", b"lhs", "lsuffix", b"lsuffix", "rhs", b"rhs", "rsuffix", b"rsuffix", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["lhs", b"lhs", "lsuffix", b"lsuffix", "rhs", b"rhs", "rsuffix", b"rsuffix", "src", b"src"]) -> None: ...

global___DataframeCrossJoin = DataframeCrossJoin

@typing.final
class DataframeCube(google.protobuf.message.Message):
    """dataframe-grouped.ir:6"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLS_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def cols(self) -> global___ExprArgList: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        cols: global___ExprArgList | None = ...,
        df: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["cols", b"cols", "df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["cols", b"cols", "df", b"df", "src", b"src"]) -> None: ...

global___DataframeCube = DataframeCube

@typing.final
class DataframeDescribe(google.protobuf.message.Message):
    """dataframe.ir:182"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLS_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STRINGS_INCLUDE_MATH_STATS_FIELD_NUMBER: builtins.int
    strings_include_math_stats: builtins.bool
    @property
    def cols(self) -> global___ExprArgList: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        cols: global___ExprArgList | None = ...,
        df: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        strings_include_math_stats: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["cols", b"cols", "df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["cols", b"cols", "df", b"df", "src", b"src", "strings_include_math_stats", b"strings_include_math_stats"]) -> None: ...

global___DataframeDescribe = DataframeDescribe

@typing.final
class DataframeDistinct(google.protobuf.message.Message):
    """dataframe.ir:188"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DF_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        df: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["df", b"df", "src", b"src"]) -> None: ...

global___DataframeDistinct = DataframeDistinct

@typing.final
class DataframeDrop(google.protobuf.message.Message):
    """dataframe.ir:192"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLS_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def cols(self) -> global___ExprArgList: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        cols: global___ExprArgList | None = ...,
        df: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["cols", b"cols", "df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["cols", b"cols", "df", b"df", "src", b"src"]) -> None: ...

global___DataframeDrop = DataframeDrop

@typing.final
class DataframeDropDuplicates(google.protobuf.message.Message):
    """dataframe.ir:197"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLS_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def cols(self) -> global___ExprArgList: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        cols: global___ExprArgList | None = ...,
        df: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["cols", b"cols", "df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["cols", b"cols", "df", b"df", "src", b"src"]) -> None: ...

global___DataframeDropDuplicates = DataframeDropDuplicates

@typing.final
class DataframeExcept(google.protobuf.message.Message):
    """dataframe.ir:202"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DF_FIELD_NUMBER: builtins.int
    OTHER_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def df(self) -> global___Expr: ...
    @property
    def other(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        df: global___Expr | None = ...,
        other: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "other", b"other", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["df", b"df", "other", b"other", "src", b"src"]) -> None: ...

global___DataframeExcept = DataframeExcept

@typing.final
class DataframeFilter(google.protobuf.message.Message):
    """dataframe.ir:207"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CONDITION_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def condition(self) -> global___Expr: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        condition: global___Expr | None = ...,
        df: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["condition", b"condition", "df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["condition", b"condition", "df", b"df", "src", b"src"]) -> None: ...

global___DataframeFilter = DataframeFilter

@typing.final
class DataframeFirst(google.protobuf.message.Message):
    """dataframe.ir:221"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BLOCK_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    NUM_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    block: builtins.bool
    num: builtins.int
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    def __init__(
        self,
        *,
        block: builtins.bool = ...,
        df: global___Expr | None = ...,
        num: builtins.int = ...,
        src: global___SrcPosition | None = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["block", b"block", "df", b"df", "num", b"num", "src", b"src", "statement_params", b"statement_params"]) -> None: ...

global___DataframeFirst = DataframeFirst

@typing.final
class DataframeFlatten(google.protobuf.message.Message):
    """dataframe.ir:212"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DF_FIELD_NUMBER: builtins.int
    INPUT_FIELD_NUMBER: builtins.int
    MODE_FIELD_NUMBER: builtins.int
    OUTER_FIELD_NUMBER: builtins.int
    PATH_FIELD_NUMBER: builtins.int
    RECURSIVE_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    outer: builtins.bool
    recursive: builtins.bool
    @property
    def df(self) -> global___Expr: ...
    @property
    def input(self) -> global___Expr: ...
    @property
    def mode(self) -> global___FlattenMode: ...
    @property
    def path(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        df: global___Expr | None = ...,
        input: global___Expr | None = ...,
        mode: global___FlattenMode | None = ...,
        outer: builtins.bool = ...,
        path: google.protobuf.wrappers_pb2.StringValue | None = ...,
        recursive: builtins.bool = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "input", b"input", "mode", b"mode", "path", b"path", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["df", b"df", "input", b"input", "mode", b"mode", "outer", b"outer", "path", b"path", "recursive", b"recursive", "src", b"src"]) -> None: ...

global___DataframeFlatten = DataframeFlatten

@typing.final
class DataframeGroupBy(google.protobuf.message.Message):
    """dataframe-grouped.ir:11"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLS_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def cols(self) -> global___ExprArgList: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        cols: global___ExprArgList | None = ...,
        df: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["cols", b"cols", "df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["cols", b"cols", "df", b"df", "src", b"src"]) -> None: ...

global___DataframeGroupBy = DataframeGroupBy

@typing.final
class DataframeGroupByGroupingSets(google.protobuf.message.Message):
    """dataframe-grouped.ir:28"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DF_FIELD_NUMBER: builtins.int
    GROUPING_SETS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def df(self) -> global___Expr: ...
    @property
    def grouping_sets(self) -> global___ExprArgList: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        df: global___Expr | None = ...,
        grouping_sets: global___ExprArgList | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "grouping_sets", b"grouping_sets", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["df", b"df", "grouping_sets", b"grouping_sets", "src", b"src"]) -> None: ...

global___DataframeGroupByGroupingSets = DataframeGroupByGroupingSets

@typing.final
class DataframeIntersect(google.protobuf.message.Message):
    """dataframe.ir:228"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DF_FIELD_NUMBER: builtins.int
    OTHER_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def df(self) -> global___Expr: ...
    @property
    def other(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        df: global___Expr | None = ...,
        other: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "other", b"other", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["df", b"df", "other", b"other", "src", b"src"]) -> None: ...

global___DataframeIntersect = DataframeIntersect

@typing.final
class DataframeJoin(google.protobuf.message.Message):
    """dataframe.ir:244"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    JOIN_EXPR_FIELD_NUMBER: builtins.int
    JOIN_TYPE_FIELD_NUMBER: builtins.int
    LHS_FIELD_NUMBER: builtins.int
    LSUFFIX_FIELD_NUMBER: builtins.int
    MATCH_CONDITION_FIELD_NUMBER: builtins.int
    RHS_FIELD_NUMBER: builtins.int
    RSUFFIX_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def join_expr(self) -> global___Expr: ...
    @property
    def join_type(self) -> global___JoinType: ...
    @property
    def lhs(self) -> global___Expr: ...
    @property
    def lsuffix(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def match_condition(self) -> global___Expr: ...
    @property
    def rhs(self) -> global___Expr: ...
    @property
    def rsuffix(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        join_expr: global___Expr | None = ...,
        join_type: global___JoinType | None = ...,
        lhs: global___Expr | None = ...,
        lsuffix: google.protobuf.wrappers_pb2.StringValue | None = ...,
        match_condition: global___Expr | None = ...,
        rhs: global___Expr | None = ...,
        rsuffix: google.protobuf.wrappers_pb2.StringValue | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["join_expr", b"join_expr", "join_type", b"join_type", "lhs", b"lhs", "lsuffix", b"lsuffix", "match_condition", b"match_condition", "rhs", b"rhs", "rsuffix", b"rsuffix", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["join_expr", b"join_expr", "join_type", b"join_type", "lhs", b"lhs", "lsuffix", b"lsuffix", "match_condition", b"match_condition", "rhs", b"rhs", "rsuffix", b"rsuffix", "src", b"src"]) -> None: ...

global___DataframeJoin = DataframeJoin

@typing.final
class DataframeJoinTableFunction(google.protobuf.message.Message):
    """dataframe.ir:254"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FN_FIELD_NUMBER: builtins.int
    LHS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def fn(self) -> global___Expr: ...
    @property
    def lhs(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        fn: global___Expr | None = ...,
        lhs: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["fn", b"fn", "lhs", b"lhs", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["fn", b"fn", "lhs", b"lhs", "src", b"src"]) -> None: ...

global___DataframeJoinTableFunction = DataframeJoinTableFunction

@typing.final
class DataframeLimit(google.protobuf.message.Message):
    """dataframe.ir:259"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DF_FIELD_NUMBER: builtins.int
    N_FIELD_NUMBER: builtins.int
    OFFSET_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    n: builtins.int
    offset: builtins.int
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        df: global___Expr | None = ...,
        n: builtins.int = ...,
        offset: builtins.int = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["df", b"df", "n", b"n", "offset", b"offset", "src", b"src"]) -> None: ...

global___DataframeLimit = DataframeLimit

@typing.final
class DataframeNaDrop_Python(google.protobuf.message.Message):
    """dataframe.ir:135"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DF_FIELD_NUMBER: builtins.int
    HOW_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    SUBSET_FIELD_NUMBER: builtins.int
    THRESH_FIELD_NUMBER: builtins.int
    how: builtins.str
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def subset(self) -> global___ExprArgList: ...
    @property
    def thresh(self) -> google.protobuf.wrappers_pb2.Int64Value: ...
    def __init__(
        self,
        *,
        df: global___Expr | None = ...,
        how: builtins.str = ...,
        src: global___SrcPosition | None = ...,
        subset: global___ExprArgList | None = ...,
        thresh: google.protobuf.wrappers_pb2.Int64Value | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "src", b"src", "subset", b"subset", "thresh", b"thresh"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["df", b"df", "how", b"how", "src", b"src", "subset", b"subset", "thresh", b"thresh"]) -> None: ...

global___DataframeNaDrop_Python = DataframeNaDrop_Python

@typing.final
class DataframeNaDrop_Scala(google.protobuf.message.Message):
    """dataframe.ir:129"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLS_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    MIN_NON_NULLS_PER_ROW_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    min_non_nulls_per_row: builtins.int
    @property
    def cols(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        cols: collections.abc.Iterable[builtins.str] | None = ...,
        df: global___Expr | None = ...,
        min_non_nulls_per_row: builtins.int = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["cols", b"cols", "df", b"df", "min_non_nulls_per_row", b"min_non_nulls_per_row", "src", b"src"]) -> None: ...

global___DataframeNaDrop_Scala = DataframeNaDrop_Scala

@typing.final
class DataframeNaFill(google.protobuf.message.Message):
    """dataframe.ir:142"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DF_FIELD_NUMBER: builtins.int
    INCLUDE_DECIMAL_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    SUBSET_FIELD_NUMBER: builtins.int
    VALUE_FIELD_NUMBER: builtins.int
    VALUE_MAP_FIELD_NUMBER: builtins.int
    include_decimal: builtins.bool
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def subset(self) -> global___ExprArgList: ...
    @property
    def value(self) -> global___Expr: ...
    @property
    def value_map(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_Expr]: ...
    def __init__(
        self,
        *,
        df: global___Expr | None = ...,
        include_decimal: builtins.bool = ...,
        src: global___SrcPosition | None = ...,
        subset: global___ExprArgList | None = ...,
        value: global___Expr | None = ...,
        value_map: collections.abc.Iterable[global___Tuple_String_Expr] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "src", b"src", "subset", b"subset", "value", b"value"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["df", b"df", "include_decimal", b"include_decimal", "src", b"src", "subset", b"subset", "value", b"value", "value_map", b"value_map"]) -> None: ...

global___DataframeNaFill = DataframeNaFill

@typing.final
class DataframeNaReplace(google.protobuf.message.Message):
    """dataframe.ir:150"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DF_FIELD_NUMBER: builtins.int
    INCLUDE_DECIMAL_FIELD_NUMBER: builtins.int
    REPLACEMENT_MAP_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    SUBSET_FIELD_NUMBER: builtins.int
    TO_REPLACE_LIST_FIELD_NUMBER: builtins.int
    TO_REPLACE_VALUE_FIELD_NUMBER: builtins.int
    VALUE_FIELD_NUMBER: builtins.int
    VALUES_FIELD_NUMBER: builtins.int
    include_decimal: builtins.bool
    @property
    def df(self) -> global___Expr: ...
    @property
    def replacement_map(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_Expr_Expr]: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def subset(self) -> global___ExprArgList: ...
    @property
    def to_replace_list(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Expr]: ...
    @property
    def to_replace_value(self) -> global___Expr: ...
    @property
    def value(self) -> global___Expr: ...
    @property
    def values(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Expr]: ...
    def __init__(
        self,
        *,
        df: global___Expr | None = ...,
        include_decimal: builtins.bool = ...,
        replacement_map: collections.abc.Iterable[global___Tuple_Expr_Expr] | None = ...,
        src: global___SrcPosition | None = ...,
        subset: global___ExprArgList | None = ...,
        to_replace_list: collections.abc.Iterable[global___Expr] | None = ...,
        to_replace_value: global___Expr | None = ...,
        value: global___Expr | None = ...,
        values: collections.abc.Iterable[global___Expr] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "src", b"src", "subset", b"subset", "to_replace_value", b"to_replace_value", "value", b"value"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["df", b"df", "include_decimal", b"include_decimal", "replacement_map", b"replacement_map", "src", b"src", "subset", b"subset", "to_replace_list", b"to_replace_list", "to_replace_value", b"to_replace_value", "value", b"value", "values", b"values"]) -> None: ...

global___DataframeNaReplace = DataframeNaReplace

@typing.final
class DataframeNaturalJoin(google.protobuf.message.Message):
    """dataframe.ir:265"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    JOIN_TYPE_FIELD_NUMBER: builtins.int
    LHS_FIELD_NUMBER: builtins.int
    RHS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def join_type(self) -> global___JoinType: ...
    @property
    def lhs(self) -> global___Expr: ...
    @property
    def rhs(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        join_type: global___JoinType | None = ...,
        lhs: global___Expr | None = ...,
        rhs: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["join_type", b"join_type", "lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["join_type", b"join_type", "lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> None: ...

global___DataframeNaturalJoin = DataframeNaturalJoin

@typing.final
class DataframePivot(google.protobuf.message.Message):
    """dataframe-grouped.ir:16"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DEFAULT_ON_NULL_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    PIVOT_COL_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    VALUES_FIELD_NUMBER: builtins.int
    @property
    def default_on_null(self) -> global___Expr: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def pivot_col(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def values(self) -> global___Expr: ...
    def __init__(
        self,
        *,
        default_on_null: global___Expr | None = ...,
        df: global___Expr | None = ...,
        pivot_col: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        values: global___Expr | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["default_on_null", b"default_on_null", "df", b"df", "pivot_col", b"pivot_col", "src", b"src", "values", b"values"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["default_on_null", b"default_on_null", "df", b"df", "pivot_col", b"pivot_col", "src", b"src", "values", b"values"]) -> None: ...

global___DataframePivot = DataframePivot

@typing.final
class DataframeRandomSplit(google.protobuf.message.Message):
    """dataframe.ir:279"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DF_FIELD_NUMBER: builtins.int
    SEED_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    WEIGHTS_FIELD_NUMBER: builtins.int
    @property
    def df(self) -> global___Expr: ...
    @property
    def seed(self) -> google.protobuf.wrappers_pb2.Int64Value: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    @property
    def weights(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.float]: ...
    def __init__(
        self,
        *,
        df: global___Expr | None = ...,
        seed: google.protobuf.wrappers_pb2.Int64Value | None = ...,
        src: global___SrcPosition | None = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
        weights: collections.abc.Iterable[builtins.float] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "seed", b"seed", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["df", b"df", "seed", b"seed", "src", b"src", "statement_params", b"statement_params", "weights", b"weights"]) -> None: ...

global___DataframeRandomSplit = DataframeRandomSplit

@typing.final
class DataframeReader(google.protobuf.message.Message):
    """dataframe-io.ir:13"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FORMAT_FIELD_NUMBER: builtins.int
    METADATA_COLUMNS_FIELD_NUMBER: builtins.int
    OPTIONS_FIELD_NUMBER: builtins.int
    SCHEMA_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def format(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def metadata_columns(self) -> global___ExprArgList: ...
    @property
    def options(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_Expr]: ...
    @property
    def schema(self) -> global___StructType: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        format: google.protobuf.wrappers_pb2.StringValue | None = ...,
        metadata_columns: global___ExprArgList | None = ...,
        options: collections.abc.Iterable[global___Tuple_String_Expr] | None = ...,
        schema: global___StructType | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["format", b"format", "metadata_columns", b"metadata_columns", "schema", b"schema", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["format", b"format", "metadata_columns", b"metadata_columns", "options", b"options", "schema", b"schema", "src", b"src"]) -> None: ...

global___DataframeReader = DataframeReader

@typing.final
class DataframeRef(google.protobuf.message.Message):
    """dataframe.ir:4"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    id: builtins.int
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        id: builtins.int = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["id", b"id", "src", b"src"]) -> None: ...

global___DataframeRef = DataframeRef

@typing.final
class DataframeRename(google.protobuf.message.Message):
    """dataframe.ir:286"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_OR_MAPPER_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    NEW_COLUMN_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def col_or_mapper(self) -> global___Expr: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def new_column(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        col_or_mapper: global___Expr | None = ...,
        df: global___Expr | None = ...,
        new_column: google.protobuf.wrappers_pb2.StringValue | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col_or_mapper", b"col_or_mapper", "df", b"df", "new_column", b"new_column", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col_or_mapper", b"col_or_mapper", "df", b"df", "new_column", b"new_column", "src", b"src"]) -> None: ...

global___DataframeRename = DataframeRename

@typing.final
class DataframeRollup(google.protobuf.message.Message):
    """dataframe-grouped.ir:23"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLS_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def cols(self) -> global___ExprArgList: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        cols: global___ExprArgList | None = ...,
        df: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["cols", b"cols", "df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["cols", b"cols", "df", b"df", "src", b"src"]) -> None: ...

global___DataframeRollup = DataframeRollup

@typing.final
class DataframeSample(google.protobuf.message.Message):
    """dataframe.ir:292"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DF_FIELD_NUMBER: builtins.int
    NUM_FIELD_NUMBER: builtins.int
    PROBABILITY_FRACTION_FIELD_NUMBER: builtins.int
    SEED_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def df(self) -> global___Expr: ...
    @property
    def num(self) -> google.protobuf.wrappers_pb2.Int64Value: ...
    @property
    def probability_fraction(self) -> google.protobuf.wrappers_pb2.DoubleValue: ...
    @property
    def seed(self) -> google.protobuf.wrappers_pb2.Int64Value: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        df: global___Expr | None = ...,
        num: google.protobuf.wrappers_pb2.Int64Value | None = ...,
        probability_fraction: google.protobuf.wrappers_pb2.DoubleValue | None = ...,
        seed: google.protobuf.wrappers_pb2.Int64Value | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "num", b"num", "probability_fraction", b"probability_fraction", "seed", b"seed", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["df", b"df", "num", b"num", "probability_fraction", b"probability_fraction", "seed", b"seed", "src", b"src"]) -> None: ...

global___DataframeSample = DataframeSample

@typing.final
class DataframeSelect(google.protobuf.message.Message):
    """dataframe.ir:299"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLS_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    EXPR_VARIANT_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    expr_variant: builtins.bool
    @property
    def cols(self) -> global___ExprArgList: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        cols: global___ExprArgList | None = ...,
        df: global___Expr | None = ...,
        expr_variant: builtins.bool = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["cols", b"cols", "df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["cols", b"cols", "df", b"df", "expr_variant", b"expr_variant", "src", b"src"]) -> None: ...

global___DataframeSelect = DataframeSelect

@typing.final
class DataframeShow(google.protobuf.message.Message):
    """dataframe.ir:8"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DF_FIELD_NUMBER: builtins.int
    N_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    n: builtins.int
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        df: global___Expr | None = ...,
        n: builtins.int = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["df", b"df", "n", b"n", "src", b"src"]) -> None: ...

global___DataframeShow = DataframeShow

@typing.final
class DataframeSort(google.protobuf.message.Message):
    """dataframe.ir:306"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASCENDING_FIELD_NUMBER: builtins.int
    COLS_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def ascending(self) -> global___Expr: ...
    @property
    def cols(self) -> global___ExprArgList: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        ascending: global___Expr | None = ...,
        cols: global___ExprArgList | None = ...,
        df: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["ascending", b"ascending", "cols", b"cols", "df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["ascending", b"ascending", "cols", b"cols", "df", b"df", "src", b"src"]) -> None: ...

global___DataframeSort = DataframeSort

@typing.final
class DataframeStatApproxQuantile(google.protobuf.message.Message):
    """dataframe-stat.ir:1"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLS_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    PERCENTILE_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    @property
    def cols(self) -> global___ExprArgList: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def percentile(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.float]: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    def __init__(
        self,
        *,
        cols: global___ExprArgList | None = ...,
        df: global___Expr | None = ...,
        percentile: collections.abc.Iterable[builtins.float] | None = ...,
        src: global___SrcPosition | None = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["cols", b"cols", "df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["cols", b"cols", "df", b"df", "percentile", b"percentile", "src", b"src", "statement_params", b"statement_params"]) -> None: ...

global___DataframeStatApproxQuantile = DataframeStatApproxQuantile

@typing.final
class DataframeStatCorr(google.protobuf.message.Message):
    """dataframe-stat.ir:8"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL1_FIELD_NUMBER: builtins.int
    COL2_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    @property
    def col1(self) -> global___Expr: ...
    @property
    def col2(self) -> global___Expr: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    def __init__(
        self,
        *,
        col1: global___Expr | None = ...,
        col2: global___Expr | None = ...,
        df: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col1", b"col1", "col2", b"col2", "df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col1", b"col1", "col2", b"col2", "df", b"df", "src", b"src", "statement_params", b"statement_params"]) -> None: ...

global___DataframeStatCorr = DataframeStatCorr

@typing.final
class DataframeStatCov(google.protobuf.message.Message):
    """dataframe-stat.ir:15"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL1_FIELD_NUMBER: builtins.int
    COL2_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    @property
    def col1(self) -> global___Expr: ...
    @property
    def col2(self) -> global___Expr: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    def __init__(
        self,
        *,
        col1: global___Expr | None = ...,
        col2: global___Expr | None = ...,
        df: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col1", b"col1", "col2", b"col2", "df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col1", b"col1", "col2", b"col2", "df", b"df", "src", b"src", "statement_params", b"statement_params"]) -> None: ...

global___DataframeStatCov = DataframeStatCov

@typing.final
class DataframeStatCrossTab(google.protobuf.message.Message):
    """dataframe-stat.ir:22"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL1_FIELD_NUMBER: builtins.int
    COL2_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    @property
    def col1(self) -> global___Expr: ...
    @property
    def col2(self) -> global___Expr: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    def __init__(
        self,
        *,
        col1: global___Expr | None = ...,
        col2: global___Expr | None = ...,
        df: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col1", b"col1", "col2", b"col2", "df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col1", b"col1", "col2", b"col2", "df", b"df", "src", b"src", "statement_params", b"statement_params"]) -> None: ...

global___DataframeStatCrossTab = DataframeStatCrossTab

@typing.final
class DataframeStatSampleBy(google.protobuf.message.Message):
    """dataframe-stat.ir:29"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    FRACTIONS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def col(self) -> global___Expr: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def fractions(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_Expr_Float]: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        col: global___Expr | None = ...,
        df: global___Expr | None = ...,
        fractions: collections.abc.Iterable[global___Tuple_Expr_Float] | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col", b"col", "df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col", b"col", "df", b"df", "fractions", b"fractions", "src", b"src"]) -> None: ...

global___DataframeStatSampleBy = DataframeStatSampleBy

@typing.final
class DataframeToDf(google.protobuf.message.Message):
    """dataframe.ir:120"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_NAMES_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def col_names(self) -> global___ExprArgList: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        col_names: global___ExprArgList | None = ...,
        df: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col_names", b"col_names", "df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col_names", b"col_names", "df", b"df", "src", b"src"]) -> None: ...

global___DataframeToDf = DataframeToDf

@typing.final
class DataframeToLocalIterator(google.protobuf.message.Message):
    """dataframe.ir:28"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BLOCK_FIELD_NUMBER: builtins.int
    CASE_SENSITIVE_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    block: builtins.bool
    case_sensitive: builtins.bool
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    def __init__(
        self,
        *,
        block: builtins.bool = ...,
        case_sensitive: builtins.bool = ...,
        df: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["block", b"block", "case_sensitive", b"case_sensitive", "df", b"df", "src", b"src", "statement_params", b"statement_params"]) -> None: ...

global___DataframeToLocalIterator = DataframeToLocalIterator

@typing.final
class DataframeToPandas(google.protobuf.message.Message):
    """dataframe.ir:108"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BLOCK_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    block: builtins.bool
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    def __init__(
        self,
        *,
        block: builtins.bool = ...,
        df: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["block", b"block", "df", b"df", "src", b"src", "statement_params", b"statement_params"]) -> None: ...

global___DataframeToPandas = DataframeToPandas

@typing.final
class DataframeToPandasBatches(google.protobuf.message.Message):
    """dataframe.ir:114"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BLOCK_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    block: builtins.bool
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    def __init__(
        self,
        *,
        block: builtins.bool = ...,
        df: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["block", b"block", "df", b"df", "src", b"src", "statement_params", b"statement_params"]) -> None: ...

global___DataframeToPandasBatches = DataframeToPandasBatches

@typing.final
class DataframeUnion(google.protobuf.message.Message):
    """dataframe.ir:312"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ALL_FIELD_NUMBER: builtins.int
    ALLOW_MISSING_COLUMNS_FIELD_NUMBER: builtins.int
    BY_NAME_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    OTHER_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    all: builtins.bool
    allow_missing_columns: builtins.bool
    by_name: builtins.bool
    @property
    def df(self) -> global___Expr: ...
    @property
    def other(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        all: builtins.bool = ...,
        allow_missing_columns: builtins.bool = ...,
        by_name: builtins.bool = ...,
        df: global___Expr | None = ...,
        other: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "other", b"other", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["all", b"all", "allow_missing_columns", b"allow_missing_columns", "by_name", b"by_name", "df", b"df", "other", b"other", "src", b"src"]) -> None: ...

global___DataframeUnion = DataframeUnion

@typing.final
class DataframeUnpivot(google.protobuf.message.Message):
    """dataframe.ir:271"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLUMN_LIST_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    INCLUDE_NULLS_FIELD_NUMBER: builtins.int
    NAME_COLUMN_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    VALUE_COLUMN_FIELD_NUMBER: builtins.int
    include_nulls: builtins.bool
    name_column: builtins.str
    value_column: builtins.str
    @property
    def column_list(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Expr]: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        column_list: collections.abc.Iterable[global___Expr] | None = ...,
        df: global___Expr | None = ...,
        include_nulls: builtins.bool = ...,
        name_column: builtins.str = ...,
        src: global___SrcPosition | None = ...,
        value_column: builtins.str = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["column_list", b"column_list", "df", b"df", "include_nulls", b"include_nulls", "name_column", b"name_column", "src", b"src", "value_column", b"value_column"]) -> None: ...

global___DataframeUnpivot = DataframeUnpivot

@typing.final
class DataframeWithColumn(google.protobuf.message.Message):
    """dataframe.ir:320"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_FIELD_NUMBER: builtins.int
    COL_NAME_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    col_name: builtins.str
    @property
    def col(self) -> global___Expr: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        col: global___Expr | None = ...,
        col_name: builtins.str = ...,
        df: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col", b"col", "df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col", b"col", "col_name", b"col_name", "df", b"df", "src", b"src"]) -> None: ...

global___DataframeWithColumn = DataframeWithColumn

@typing.final
class DataframeWithColumnRenamed(google.protobuf.message.Message):
    """dataframe.ir:326"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    NEW_NAME_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    new_name: builtins.str
    @property
    def col(self) -> global___Expr: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        col: global___Expr | None = ...,
        df: global___Expr | None = ...,
        new_name: builtins.str = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["col", b"col", "df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col", b"col", "df", b"df", "new_name", b"new_name", "src", b"src"]) -> None: ...

global___DataframeWithColumnRenamed = DataframeWithColumnRenamed

@typing.final
class DataframeWithColumns(google.protobuf.message.Message):
    """dataframe.ir:332"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COL_NAMES_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    VALUES_FIELD_NUMBER: builtins.int
    @property
    def col_names(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def values(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Expr]: ...
    def __init__(
        self,
        *,
        col_names: collections.abc.Iterable[builtins.str] | None = ...,
        df: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        values: collections.abc.Iterable[global___Expr] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["col_names", b"col_names", "df", b"df", "src", b"src", "values", b"values"]) -> None: ...

global___DataframeWithColumns = DataframeWithColumns

@typing.final
class DataframeWriter(google.protobuf.message.Message):
    """dataframe-io.ir:62"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DF_FIELD_NUMBER: builtins.int
    FORMAT_FIELD_NUMBER: builtins.int
    OPTIONS_FIELD_NUMBER: builtins.int
    PARTITION_BY_FIELD_NUMBER: builtins.int
    SAVE_MODE_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def df(self) -> global___Expr: ...
    @property
    def format(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def options(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_Expr]: ...
    @property
    def partition_by(self) -> global___Expr: ...
    @property
    def save_mode(self) -> global___SaveMode: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        df: global___Expr | None = ...,
        format: google.protobuf.wrappers_pb2.StringValue | None = ...,
        options: collections.abc.Iterable[global___Tuple_String_Expr] | None = ...,
        partition_by: global___Expr | None = ...,
        save_mode: global___SaveMode | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "format", b"format", "partition_by", b"partition_by", "save_mode", b"save_mode", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["df", b"df", "format", b"format", "options", b"options", "partition_by", b"partition_by", "save_mode", b"save_mode", "src", b"src"]) -> None: ...

global___DataframeWriter = DataframeWriter

@typing.final
class DatatypeVal(google.protobuf.message.Message):
    """const.ir:49"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATATYPE_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def datatype(self) -> global___DataType: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        datatype: global___DataType | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["datatype", b"datatype", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["datatype", b"datatype", "src", b"src"]) -> None: ...

global___DatatypeVal = DatatypeVal

@typing.final
class Div(google.protobuf.message.Message):
    """op.ir:48"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LHS_FIELD_NUMBER: builtins.int
    RHS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def lhs(self) -> global___Expr: ...
    @property
    def rhs(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        lhs: global___Expr | None = ...,
        rhs: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> None: ...

global___Div = Div

@typing.final
class Eq(google.protobuf.message.Message):
    """op.ir:24"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LHS_FIELD_NUMBER: builtins.int
    RHS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def lhs(self) -> global___Expr: ...
    @property
    def rhs(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        lhs: global___Expr | None = ...,
        rhs: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> None: ...

global___Eq = Eq

@typing.final
class Error(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    EXTENSION_ERROR_FIELD_NUMBER: builtins.int
    SESSION_RESET_REQUIRED_ERROR_FIELD_NUMBER: builtins.int
    @property
    def extension_error(self) -> global___ExtensionError: ...
    @property
    def session_reset_required_error(self) -> global___SessionResetRequiredError: ...
    def __init__(
        self,
        *,
        extension_error: global___ExtensionError | None = ...,
        session_reset_required_error: global___SessionResetRequiredError | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["extension_error", b"extension_error", "session_reset_required_error", b"session_reset_required_error", "variant", b"variant"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["extension_error", b"extension_error", "session_reset_required_error", b"session_reset_required_error", "variant", b"variant"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["variant", b"variant"]) -> typing.Literal["extension_error", "session_reset_required_error"] | None: ...

global___Error = Error

@typing.final
class Eval(google.protobuf.message.Message):
    """ast.ir:53"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BIND_ID_FIELD_NUMBER: builtins.int
    bind_id: builtins.int
    def __init__(
        self,
        *,
        bind_id: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["bind_id", b"bind_id"]) -> None: ...

global___Eval = Eval

@typing.final
class EvalOk(google.protobuf.message.Message):
    """ast.ir:73"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BIND_ID_FIELD_NUMBER: builtins.int
    DATA_FIELD_NUMBER: builtins.int
    UID_FIELD_NUMBER: builtins.int
    bind_id: builtins.int
    uid: builtins.int
    @property
    def data(self) -> global___EvalResult: ...
    def __init__(
        self,
        *,
        bind_id: builtins.int = ...,
        data: global___EvalResult | None = ...,
        uid: builtins.int = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["data", b"data"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["bind_id", b"bind_id", "data", b"data", "uid", b"uid"]) -> None: ...

global___EvalOk = EvalOk

@typing.final
class EvalResult(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TRAIT_CONST_FIELD_NUMBER: builtins.int
    BIG_DECIMAL_VAL_FIELD_NUMBER: builtins.int
    BIG_INT_VAL_FIELD_NUMBER: builtins.int
    BINARY_VAL_FIELD_NUMBER: builtins.int
    BOOL_VAL_FIELD_NUMBER: builtins.int
    DATATYPE_VAL_FIELD_NUMBER: builtins.int
    EXTENSION_EVAL_RESULT_FIELD_NUMBER: builtins.int
    FLOAT64_VAL_FIELD_NUMBER: builtins.int
    INT64_VAL_FIELD_NUMBER: builtins.int
    NULL_VAL_FIELD_NUMBER: builtins.int
    PYTHON_DATE_VAL_FIELD_NUMBER: builtins.int
    PYTHON_TIME_VAL_FIELD_NUMBER: builtins.int
    PYTHON_TIMESTAMP_VAL_FIELD_NUMBER: builtins.int
    REDACTED_CONST_FIELD_NUMBER: builtins.int
    SF_QUERY_RESULT_FIELD_NUMBER: builtins.int
    SHOW_RESULT_FIELD_NUMBER: builtins.int
    STRING_VAL_FIELD_NUMBER: builtins.int
    @property
    def trait_const(self) -> global___Const: ...
    @property
    def big_decimal_val(self) -> global___BigDecimalVal: ...
    @property
    def big_int_val(self) -> global___BigIntVal: ...
    @property
    def binary_val(self) -> global___BinaryVal: ...
    @property
    def bool_val(self) -> global___BoolVal: ...
    @property
    def datatype_val(self) -> global___DatatypeVal: ...
    @property
    def extension_eval_result(self) -> global___ExtensionEvalResult: ...
    @property
    def float64_val(self) -> global___Float64Val: ...
    @property
    def int64_val(self) -> global___Int64Val: ...
    @property
    def null_val(self) -> global___NullVal: ...
    @property
    def python_date_val(self) -> global___PythonDateVal: ...
    @property
    def python_time_val(self) -> global___PythonTimeVal: ...
    @property
    def python_timestamp_val(self) -> global___PythonTimestampVal: ...
    @property
    def redacted_const(self) -> global___RedactedConst: ...
    @property
    def sf_query_result(self) -> global___SfQueryResult: ...
    @property
    def show_result(self) -> global___ShowResult: ...
    @property
    def string_val(self) -> global___StringVal: ...
    def __init__(
        self,
        *,
        trait_const: global___Const | None = ...,
        big_decimal_val: global___BigDecimalVal | None = ...,
        big_int_val: global___BigIntVal | None = ...,
        binary_val: global___BinaryVal | None = ...,
        bool_val: global___BoolVal | None = ...,
        datatype_val: global___DatatypeVal | None = ...,
        extension_eval_result: global___ExtensionEvalResult | None = ...,
        float64_val: global___Float64Val | None = ...,
        int64_val: global___Int64Val | None = ...,
        null_val: global___NullVal | None = ...,
        python_date_val: global___PythonDateVal | None = ...,
        python_time_val: global___PythonTimeVal | None = ...,
        python_timestamp_val: global___PythonTimestampVal | None = ...,
        redacted_const: global___RedactedConst | None = ...,
        sf_query_result: global___SfQueryResult | None = ...,
        show_result: global___ShowResult | None = ...,
        string_val: global___StringVal | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["big_decimal_val", b"big_decimal_val", "big_int_val", b"big_int_val", "binary_val", b"binary_val", "bool_val", b"bool_val", "datatype_val", b"datatype_val", "extension_eval_result", b"extension_eval_result", "float64_val", b"float64_val", "int64_val", b"int64_val", "null_val", b"null_val", "python_date_val", b"python_date_val", "python_time_val", b"python_time_val", "python_timestamp_val", b"python_timestamp_val", "redacted_const", b"redacted_const", "sf_query_result", b"sf_query_result", "show_result", b"show_result", "string_val", b"string_val", "trait_const", b"trait_const", "variant", b"variant"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["big_decimal_val", b"big_decimal_val", "big_int_val", b"big_int_val", "binary_val", b"binary_val", "bool_val", b"bool_val", "datatype_val", b"datatype_val", "extension_eval_result", b"extension_eval_result", "float64_val", b"float64_val", "int64_val", b"int64_val", "null_val", b"null_val", "python_date_val", b"python_date_val", "python_time_val", b"python_time_val", "python_timestamp_val", b"python_timestamp_val", "redacted_const", b"redacted_const", "sf_query_result", b"sf_query_result", "show_result", b"show_result", "string_val", b"string_val", "trait_const", b"trait_const", "variant", b"variant"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["variant", b"variant"]) -> typing.Literal["trait_const", "big_decimal_val", "big_int_val", "binary_val", "bool_val", "datatype_val", "extension_eval_result", "float64_val", "int64_val", "null_val", "python_date_val", "python_time_val", "python_timestamp_val", "redacted_const", "sf_query_result", "show_result", "string_val"] | None: ...

global___EvalResult = EvalResult

@typing.final
class Expr(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TRAIT_BIN_OP_FIELD_NUMBER: builtins.int
    TRAIT_COLUMN_FN_FIELD_NUMBER: builtins.int
    TRAIT_CONST_FIELD_NUMBER: builtins.int
    TRAIT_EXTENSION_EXPR_FIELD_NUMBER: builtins.int
    TRAIT_FN_ID_REF_EXPR_FIELD_NUMBER: builtins.int
    TRAIT_FN_NAME_REF_EXPR_FIELD_NUMBER: builtins.int
    TRAIT_READ_FILE_FIELD_NUMBER: builtins.int
    TRAIT_UNARY_OP_FIELD_NUMBER: builtins.int
    TRAIT_WRITE_FILE_FIELD_NUMBER: builtins.int
    ADD_FIELD_NUMBER: builtins.int
    AND_FIELD_NUMBER: builtins.int
    APPLY_EXPR_FIELD_NUMBER: builtins.int
    BIG_DECIMAL_VAL_FIELD_NUMBER: builtins.int
    BIG_INT_VAL_FIELD_NUMBER: builtins.int
    BINARY_VAL_FIELD_NUMBER: builtins.int
    BIT_AND_FIELD_NUMBER: builtins.int
    BIT_OR_FIELD_NUMBER: builtins.int
    BIT_XOR_FIELD_NUMBER: builtins.int
    BOOL_VAL_FIELD_NUMBER: builtins.int
    BUILTIN_FN_FIELD_NUMBER: builtins.int
    CALL_TABLE_FUNCTION_EXPR_FIELD_NUMBER: builtins.int
    COLUMN_ALIAS_FIELD_NUMBER: builtins.int
    COLUMN_APPLY__INT_FIELD_NUMBER: builtins.int
    COLUMN_APPLY__STRING_FIELD_NUMBER: builtins.int
    COLUMN_ASC_FIELD_NUMBER: builtins.int
    COLUMN_BETWEEN_FIELD_NUMBER: builtins.int
    COLUMN_CASE_EXPR_FIELD_NUMBER: builtins.int
    COLUMN_CAST_FIELD_NUMBER: builtins.int
    COLUMN_DESC_FIELD_NUMBER: builtins.int
    COLUMN_EQUAL_NAN_FIELD_NUMBER: builtins.int
    COLUMN_EQUAL_NULL_FIELD_NUMBER: builtins.int
    COLUMN_IN_FIELD_NUMBER: builtins.int
    COLUMN_IS_NOT_NULL_FIELD_NUMBER: builtins.int
    COLUMN_IS_NULL_FIELD_NUMBER: builtins.int
    COLUMN_OVER_FIELD_NUMBER: builtins.int
    COLUMN_REGEXP_FIELD_NUMBER: builtins.int
    COLUMN_STRING_COLLATE_FIELD_NUMBER: builtins.int
    COLUMN_STRING_CONTAINS_FIELD_NUMBER: builtins.int
    COLUMN_STRING_ENDS_WITH_FIELD_NUMBER: builtins.int
    COLUMN_STRING_LIKE_FIELD_NUMBER: builtins.int
    COLUMN_STRING_STARTS_WITH_FIELD_NUMBER: builtins.int
    COLUMN_STRING_SUBSTR_FIELD_NUMBER: builtins.int
    COLUMN_TRY_CAST_FIELD_NUMBER: builtins.int
    COLUMN_WITHIN_GROUP_FIELD_NUMBER: builtins.int
    CREATE_DATAFRAME_FIELD_NUMBER: builtins.int
    DATAFRAME_AGG_FIELD_NUMBER: builtins.int
    DATAFRAME_ALIAS_FIELD_NUMBER: builtins.int
    DATAFRAME_ANALYTICS_COMPUTE_LAG_FIELD_NUMBER: builtins.int
    DATAFRAME_ANALYTICS_COMPUTE_LEAD_FIELD_NUMBER: builtins.int
    DATAFRAME_ANALYTICS_CUMULATIVE_AGG_FIELD_NUMBER: builtins.int
    DATAFRAME_ANALYTICS_MOVING_AGG_FIELD_NUMBER: builtins.int
    DATAFRAME_ANALYTICS_TIME_SERIES_AGG_FIELD_NUMBER: builtins.int
    DATAFRAME_CACHE_RESULT_FIELD_NUMBER: builtins.int
    DATAFRAME_COL_FIELD_NUMBER: builtins.int
    DATAFRAME_COLLECT_FIELD_NUMBER: builtins.int
    DATAFRAME_COPY_INTO_TABLE_FIELD_NUMBER: builtins.int
    DATAFRAME_COUNT_FIELD_NUMBER: builtins.int
    DATAFRAME_CREATE_OR_REPLACE_DYNAMIC_TABLE_FIELD_NUMBER: builtins.int
    DATAFRAME_CREATE_OR_REPLACE_VIEW_FIELD_NUMBER: builtins.int
    DATAFRAME_CROSS_JOIN_FIELD_NUMBER: builtins.int
    DATAFRAME_CUBE_FIELD_NUMBER: builtins.int
    DATAFRAME_DESCRIBE_FIELD_NUMBER: builtins.int
    DATAFRAME_DISTINCT_FIELD_NUMBER: builtins.int
    DATAFRAME_DROP_FIELD_NUMBER: builtins.int
    DATAFRAME_DROP_DUPLICATES_FIELD_NUMBER: builtins.int
    DATAFRAME_EXCEPT_FIELD_NUMBER: builtins.int
    DATAFRAME_FILTER_FIELD_NUMBER: builtins.int
    DATAFRAME_FIRST_FIELD_NUMBER: builtins.int
    DATAFRAME_FLATTEN_FIELD_NUMBER: builtins.int
    DATAFRAME_GROUP_BY_FIELD_NUMBER: builtins.int
    DATAFRAME_GROUP_BY_GROUPING_SETS_FIELD_NUMBER: builtins.int
    DATAFRAME_INTERSECT_FIELD_NUMBER: builtins.int
    DATAFRAME_JOIN_FIELD_NUMBER: builtins.int
    DATAFRAME_JOIN_TABLE_FUNCTION_FIELD_NUMBER: builtins.int
    DATAFRAME_LIMIT_FIELD_NUMBER: builtins.int
    DATAFRAME_NA_DROP__PYTHON_FIELD_NUMBER: builtins.int
    DATAFRAME_NA_DROP__SCALA_FIELD_NUMBER: builtins.int
    DATAFRAME_NA_FILL_FIELD_NUMBER: builtins.int
    DATAFRAME_NA_REPLACE_FIELD_NUMBER: builtins.int
    DATAFRAME_NATURAL_JOIN_FIELD_NUMBER: builtins.int
    DATAFRAME_PIVOT_FIELD_NUMBER: builtins.int
    DATAFRAME_RANDOM_SPLIT_FIELD_NUMBER: builtins.int
    DATAFRAME_READER_FIELD_NUMBER: builtins.int
    DATAFRAME_REF_FIELD_NUMBER: builtins.int
    DATAFRAME_RENAME_FIELD_NUMBER: builtins.int
    DATAFRAME_ROLLUP_FIELD_NUMBER: builtins.int
    DATAFRAME_SAMPLE_FIELD_NUMBER: builtins.int
    DATAFRAME_SELECT_FIELD_NUMBER: builtins.int
    DATAFRAME_SHOW_FIELD_NUMBER: builtins.int
    DATAFRAME_SORT_FIELD_NUMBER: builtins.int
    DATAFRAME_STAT_APPROX_QUANTILE_FIELD_NUMBER: builtins.int
    DATAFRAME_STAT_CORR_FIELD_NUMBER: builtins.int
    DATAFRAME_STAT_COV_FIELD_NUMBER: builtins.int
    DATAFRAME_STAT_CROSS_TAB_FIELD_NUMBER: builtins.int
    DATAFRAME_STAT_SAMPLE_BY_FIELD_NUMBER: builtins.int
    DATAFRAME_TO_DF_FIELD_NUMBER: builtins.int
    DATAFRAME_TO_LOCAL_ITERATOR_FIELD_NUMBER: builtins.int
    DATAFRAME_TO_PANDAS_FIELD_NUMBER: builtins.int
    DATAFRAME_TO_PANDAS_BATCHES_FIELD_NUMBER: builtins.int
    DATAFRAME_UNION_FIELD_NUMBER: builtins.int
    DATAFRAME_UNPIVOT_FIELD_NUMBER: builtins.int
    DATAFRAME_WITH_COLUMN_FIELD_NUMBER: builtins.int
    DATAFRAME_WITH_COLUMN_RENAMED_FIELD_NUMBER: builtins.int
    DATAFRAME_WITH_COLUMNS_FIELD_NUMBER: builtins.int
    DATAFRAME_WRITER_FIELD_NUMBER: builtins.int
    DATATYPE_VAL_FIELD_NUMBER: builtins.int
    DIV_FIELD_NUMBER: builtins.int
    EQ_FIELD_NUMBER: builtins.int
    FLATTEN_FIELD_NUMBER: builtins.int
    FLOAT64_VAL_FIELD_NUMBER: builtins.int
    FN_REF_FIELD_NUMBER: builtins.int
    GENERATOR_FIELD_NUMBER: builtins.int
    GEQ_FIELD_NUMBER: builtins.int
    GROUPING_SETS_FIELD_NUMBER: builtins.int
    GT_FIELD_NUMBER: builtins.int
    INDIRECT_TABLE_FN_ID_REF_FIELD_NUMBER: builtins.int
    INDIRECT_TABLE_FN_NAME_REF_FIELD_NUMBER: builtins.int
    INT64_VAL_FIELD_NUMBER: builtins.int
    LEQ_FIELD_NUMBER: builtins.int
    LIST_VAL_FIELD_NUMBER: builtins.int
    LT_FIELD_NUMBER: builtins.int
    MERGE_DELETE_WHEN_MATCHED_CLAUSE_FIELD_NUMBER: builtins.int
    MERGE_INSERT_WHEN_NOT_MATCHED_CLAUSE_FIELD_NUMBER: builtins.int
    MERGE_UPDATE_WHEN_MATCHED_CLAUSE_FIELD_NUMBER: builtins.int
    MOD_FIELD_NUMBER: builtins.int
    MUL_FIELD_NUMBER: builtins.int
    NEG_FIELD_NUMBER: builtins.int
    NEQ_FIELD_NUMBER: builtins.int
    NOT_FIELD_NUMBER: builtins.int
    NULL_VAL_FIELD_NUMBER: builtins.int
    OBJECT_GET_ITEM_FIELD_NUMBER: builtins.int
    OR_FIELD_NUMBER: builtins.int
    POW_FIELD_NUMBER: builtins.int
    PYTHON_DATE_VAL_FIELD_NUMBER: builtins.int
    PYTHON_TIME_VAL_FIELD_NUMBER: builtins.int
    PYTHON_TIMESTAMP_VAL_FIELD_NUMBER: builtins.int
    RANGE_FIELD_NUMBER: builtins.int
    READ_AVRO_FIELD_NUMBER: builtins.int
    READ_CSV_FIELD_NUMBER: builtins.int
    READ_JSON_FIELD_NUMBER: builtins.int
    READ_LOAD_FIELD_NUMBER: builtins.int
    READ_ORC_FIELD_NUMBER: builtins.int
    READ_PARQUET_FIELD_NUMBER: builtins.int
    READ_TABLE_FIELD_NUMBER: builtins.int
    READ_XML_FIELD_NUMBER: builtins.int
    REDACTED_CONST_FIELD_NUMBER: builtins.int
    RELATIONAL_GROUPED_DATAFRAME_AGG_FIELD_NUMBER: builtins.int
    RELATIONAL_GROUPED_DATAFRAME_APPLY_IN_PANDAS_FIELD_NUMBER: builtins.int
    RELATIONAL_GROUPED_DATAFRAME_BUILTIN_FIELD_NUMBER: builtins.int
    RELATIONAL_GROUPED_DATAFRAME_PIVOT_FIELD_NUMBER: builtins.int
    RELATIONAL_GROUPED_DATAFRAME_REF_FIELD_NUMBER: builtins.int
    ROW_FIELD_NUMBER: builtins.int
    SEQ_MAP_VAL_FIELD_NUMBER: builtins.int
    SESSION_TABLE_FUNCTION_FIELD_NUMBER: builtins.int
    SQL_FIELD_NUMBER: builtins.int
    SQL_EXPR_FIELD_NUMBER: builtins.int
    STORED_PROCEDURE_FIELD_NUMBER: builtins.int
    STRING_VAL_FIELD_NUMBER: builtins.int
    SUB_FIELD_NUMBER: builtins.int
    TABLE_FIELD_NUMBER: builtins.int
    TABLE_DELETE_FIELD_NUMBER: builtins.int
    TABLE_DROP_TABLE_FIELD_NUMBER: builtins.int
    TABLE_FN_CALL_ALIAS_FIELD_NUMBER: builtins.int
    TABLE_FN_CALL_OVER_FIELD_NUMBER: builtins.int
    TABLE_MERGE_FIELD_NUMBER: builtins.int
    TABLE_SAMPLE_FIELD_NUMBER: builtins.int
    TABLE_UPDATE_FIELD_NUMBER: builtins.int
    TO_SNOWPARK_PANDAS_FIELD_NUMBER: builtins.int
    TRUNCATED_EXPR_FIELD_NUMBER: builtins.int
    TUPLE_VAL_FIELD_NUMBER: builtins.int
    UDAF_FIELD_NUMBER: builtins.int
    UDF_FIELD_NUMBER: builtins.int
    UDTF_FIELD_NUMBER: builtins.int
    WRITE_COPY_INTO_LOCATION_FIELD_NUMBER: builtins.int
    WRITE_CSV_FIELD_NUMBER: builtins.int
    WRITE_INSERT_INTO_FIELD_NUMBER: builtins.int
    WRITE_JSON_FIELD_NUMBER: builtins.int
    WRITE_PANDAS_FIELD_NUMBER: builtins.int
    WRITE_PARQUET_FIELD_NUMBER: builtins.int
    WRITE_SAVE_FIELD_NUMBER: builtins.int
    WRITE_TABLE_FIELD_NUMBER: builtins.int
    @property
    def trait_bin_op(self) -> global___BinOp: ...
    @property
    def trait_column_fn(self) -> global___ColumnFn: ...
    @property
    def trait_const(self) -> global___Const: ...
    @property
    def trait_extension_expr(self) -> global___ExtensionExpr: ...
    @property
    def trait_fn_id_ref_expr(self) -> global___FnIdRefExpr: ...
    @property
    def trait_fn_name_ref_expr(self) -> global___FnNameRefExpr: ...
    @property
    def trait_read_file(self) -> global___ReadFile: ...
    @property
    def trait_unary_op(self) -> global___UnaryOp: ...
    @property
    def trait_write_file(self) -> global___WriteFile: ...
    @property
    def add(self) -> global___Add: ...
    @property
    def apply_expr(self) -> global___ApplyExpr: ...
    @property
    def big_decimal_val(self) -> global___BigDecimalVal: ...
    @property
    def big_int_val(self) -> global___BigIntVal: ...
    @property
    def binary_val(self) -> global___BinaryVal: ...
    @property
    def bit_and(self) -> global___BitAnd: ...
    @property
    def bit_or(self) -> global___BitOr: ...
    @property
    def bit_xor(self) -> global___BitXor: ...
    @property
    def bool_val(self) -> global___BoolVal: ...
    @property
    def builtin_fn(self) -> global___BuiltinFn: ...
    @property
    def call_table_function_expr(self) -> global___CallTableFunctionExpr: ...
    @property
    def column_alias(self) -> global___ColumnAlias: ...
    @property
    def column_apply__int(self) -> global___ColumnApply_Int: ...
    @property
    def column_apply__string(self) -> global___ColumnApply_String: ...
    @property
    def column_asc(self) -> global___ColumnAsc: ...
    @property
    def column_between(self) -> global___ColumnBetween: ...
    @property
    def column_case_expr(self) -> global___ColumnCaseExpr: ...
    @property
    def column_cast(self) -> global___ColumnCast: ...
    @property
    def column_desc(self) -> global___ColumnDesc: ...
    @property
    def column_equal_nan(self) -> global___ColumnEqualNan: ...
    @property
    def column_equal_null(self) -> global___ColumnEqualNull: ...
    @property
    def column_in(self) -> global___ColumnIn: ...
    @property
    def column_is_not_null(self) -> global___ColumnIsNotNull: ...
    @property
    def column_is_null(self) -> global___ColumnIsNull: ...
    @property
    def column_over(self) -> global___ColumnOver: ...
    @property
    def column_regexp(self) -> global___ColumnRegexp: ...
    @property
    def column_string_collate(self) -> global___ColumnStringCollate: ...
    @property
    def column_string_contains(self) -> global___ColumnStringContains: ...
    @property
    def column_string_ends_with(self) -> global___ColumnStringEndsWith: ...
    @property
    def column_string_like(self) -> global___ColumnStringLike: ...
    @property
    def column_string_starts_with(self) -> global___ColumnStringStartsWith: ...
    @property
    def column_string_substr(self) -> global___ColumnStringSubstr: ...
    @property
    def column_try_cast(self) -> global___ColumnTryCast: ...
    @property
    def column_within_group(self) -> global___ColumnWithinGroup: ...
    @property
    def create_dataframe(self) -> global___CreateDataframe: ...
    @property
    def dataframe_agg(self) -> global___DataframeAgg: ...
    @property
    def dataframe_alias(self) -> global___DataframeAlias: ...
    @property
    def dataframe_analytics_compute_lag(self) -> global___DataframeAnalyticsComputeLag: ...
    @property
    def dataframe_analytics_compute_lead(self) -> global___DataframeAnalyticsComputeLead: ...
    @property
    def dataframe_analytics_cumulative_agg(self) -> global___DataframeAnalyticsCumulativeAgg: ...
    @property
    def dataframe_analytics_moving_agg(self) -> global___DataframeAnalyticsMovingAgg: ...
    @property
    def dataframe_analytics_time_series_agg(self) -> global___DataframeAnalyticsTimeSeriesAgg: ...
    @property
    def dataframe_cache_result(self) -> global___DataframeCacheResult: ...
    @property
    def dataframe_col(self) -> global___DataframeCol: ...
    @property
    def dataframe_collect(self) -> global___DataframeCollect: ...
    @property
    def dataframe_copy_into_table(self) -> global___DataframeCopyIntoTable: ...
    @property
    def dataframe_count(self) -> global___DataframeCount: ...
    @property
    def dataframe_create_or_replace_dynamic_table(self) -> global___DataframeCreateOrReplaceDynamicTable: ...
    @property
    def dataframe_create_or_replace_view(self) -> global___DataframeCreateOrReplaceView: ...
    @property
    def dataframe_cross_join(self) -> global___DataframeCrossJoin: ...
    @property
    def dataframe_cube(self) -> global___DataframeCube: ...
    @property
    def dataframe_describe(self) -> global___DataframeDescribe: ...
    @property
    def dataframe_distinct(self) -> global___DataframeDistinct: ...
    @property
    def dataframe_drop(self) -> global___DataframeDrop: ...
    @property
    def dataframe_drop_duplicates(self) -> global___DataframeDropDuplicates: ...
    @property
    def dataframe_except(self) -> global___DataframeExcept: ...
    @property
    def dataframe_filter(self) -> global___DataframeFilter: ...
    @property
    def dataframe_first(self) -> global___DataframeFirst: ...
    @property
    def dataframe_flatten(self) -> global___DataframeFlatten: ...
    @property
    def dataframe_group_by(self) -> global___DataframeGroupBy: ...
    @property
    def dataframe_group_by_grouping_sets(self) -> global___DataframeGroupByGroupingSets: ...
    @property
    def dataframe_intersect(self) -> global___DataframeIntersect: ...
    @property
    def dataframe_join(self) -> global___DataframeJoin: ...
    @property
    def dataframe_join_table_function(self) -> global___DataframeJoinTableFunction: ...
    @property
    def dataframe_limit(self) -> global___DataframeLimit: ...
    @property
    def dataframe_na_drop__python(self) -> global___DataframeNaDrop_Python: ...
    @property
    def dataframe_na_drop__scala(self) -> global___DataframeNaDrop_Scala: ...
    @property
    def dataframe_na_fill(self) -> global___DataframeNaFill: ...
    @property
    def dataframe_na_replace(self) -> global___DataframeNaReplace: ...
    @property
    def dataframe_natural_join(self) -> global___DataframeNaturalJoin: ...
    @property
    def dataframe_pivot(self) -> global___DataframePivot: ...
    @property
    def dataframe_random_split(self) -> global___DataframeRandomSplit: ...
    @property
    def dataframe_reader(self) -> global___DataframeReader: ...
    @property
    def dataframe_ref(self) -> global___DataframeRef: ...
    @property
    def dataframe_rename(self) -> global___DataframeRename: ...
    @property
    def dataframe_rollup(self) -> global___DataframeRollup: ...
    @property
    def dataframe_sample(self) -> global___DataframeSample: ...
    @property
    def dataframe_select(self) -> global___DataframeSelect: ...
    @property
    def dataframe_show(self) -> global___DataframeShow: ...
    @property
    def dataframe_sort(self) -> global___DataframeSort: ...
    @property
    def dataframe_stat_approx_quantile(self) -> global___DataframeStatApproxQuantile: ...
    @property
    def dataframe_stat_corr(self) -> global___DataframeStatCorr: ...
    @property
    def dataframe_stat_cov(self) -> global___DataframeStatCov: ...
    @property
    def dataframe_stat_cross_tab(self) -> global___DataframeStatCrossTab: ...
    @property
    def dataframe_stat_sample_by(self) -> global___DataframeStatSampleBy: ...
    @property
    def dataframe_to_df(self) -> global___DataframeToDf: ...
    @property
    def dataframe_to_local_iterator(self) -> global___DataframeToLocalIterator: ...
    @property
    def dataframe_to_pandas(self) -> global___DataframeToPandas: ...
    @property
    def dataframe_to_pandas_batches(self) -> global___DataframeToPandasBatches: ...
    @property
    def dataframe_union(self) -> global___DataframeUnion: ...
    @property
    def dataframe_unpivot(self) -> global___DataframeUnpivot: ...
    @property
    def dataframe_with_column(self) -> global___DataframeWithColumn: ...
    @property
    def dataframe_with_column_renamed(self) -> global___DataframeWithColumnRenamed: ...
    @property
    def dataframe_with_columns(self) -> global___DataframeWithColumns: ...
    @property
    def dataframe_writer(self) -> global___DataframeWriter: ...
    @property
    def datatype_val(self) -> global___DatatypeVal: ...
    @property
    def div(self) -> global___Div: ...
    @property
    def eq(self) -> global___Eq: ...
    @property
    def flatten(self) -> global___Flatten: ...
    @property
    def float64_val(self) -> global___Float64Val: ...
    @property
    def fn_ref(self) -> global___FnRef: ...
    @property
    def generator(self) -> global___Generator: ...
    @property
    def geq(self) -> global___Geq: ...
    @property
    def grouping_sets(self) -> global___GroupingSets: ...
    @property
    def gt(self) -> global___Gt: ...
    @property
    def indirect_table_fn_id_ref(self) -> global___IndirectTableFnIdRef: ...
    @property
    def indirect_table_fn_name_ref(self) -> global___IndirectTableFnNameRef: ...
    @property
    def int64_val(self) -> global___Int64Val: ...
    @property
    def leq(self) -> global___Leq: ...
    @property
    def list_val(self) -> global___ListVal: ...
    @property
    def lt(self) -> global___Lt: ...
    @property
    def merge_delete_when_matched_clause(self) -> global___MergeDeleteWhenMatchedClause: ...
    @property
    def merge_insert_when_not_matched_clause(self) -> global___MergeInsertWhenNotMatchedClause: ...
    @property
    def merge_update_when_matched_clause(self) -> global___MergeUpdateWhenMatchedClause: ...
    @property
    def mod(self) -> global___Mod: ...
    @property
    def mul(self) -> global___Mul: ...
    @property
    def neg(self) -> global___Neg: ...
    @property
    def neq(self) -> global___Neq: ...
    @property
    def null_val(self) -> global___NullVal: ...
    @property
    def object_get_item(self) -> global___ObjectGetItem: ...
    @property
    def pow(self) -> global___Pow: ...
    @property
    def python_date_val(self) -> global___PythonDateVal: ...
    @property
    def python_time_val(self) -> global___PythonTimeVal: ...
    @property
    def python_timestamp_val(self) -> global___PythonTimestampVal: ...
    @property
    def range(self) -> global___Range: ...
    @property
    def read_avro(self) -> global___ReadAvro: ...
    @property
    def read_csv(self) -> global___ReadCsv: ...
    @property
    def read_json(self) -> global___ReadJson: ...
    @property
    def read_load(self) -> global___ReadLoad: ...
    @property
    def read_orc(self) -> global___ReadOrc: ...
    @property
    def read_parquet(self) -> global___ReadParquet: ...
    @property
    def read_table(self) -> global___ReadTable: ...
    @property
    def read_xml(self) -> global___ReadXml: ...
    @property
    def redacted_const(self) -> global___RedactedConst: ...
    @property
    def relational_grouped_dataframe_agg(self) -> global___RelationalGroupedDataframeAgg: ...
    @property
    def relational_grouped_dataframe_apply_in_pandas(self) -> global___RelationalGroupedDataframeApplyInPandas: ...
    @property
    def relational_grouped_dataframe_builtin(self) -> global___RelationalGroupedDataframeBuiltin: ...
    @property
    def relational_grouped_dataframe_pivot(self) -> global___RelationalGroupedDataframePivot: ...
    @property
    def relational_grouped_dataframe_ref(self) -> global___RelationalGroupedDataframeRef: ...
    @property
    def row(self) -> global___Row: ...
    @property
    def seq_map_val(self) -> global___SeqMapVal: ...
    @property
    def session_table_function(self) -> global___SessionTableFunction: ...
    @property
    def sql(self) -> global___Sql: ...
    @property
    def sql_expr(self) -> global___SqlExpr: ...
    @property
    def stored_procedure(self) -> global___StoredProcedure: ...
    @property
    def string_val(self) -> global___StringVal: ...
    @property
    def sub(self) -> global___Sub: ...
    @property
    def table(self) -> global___Table: ...
    @property
    def table_delete(self) -> global___TableDelete: ...
    @property
    def table_drop_table(self) -> global___TableDropTable: ...
    @property
    def table_fn_call_alias(self) -> global___TableFnCallAlias: ...
    @property
    def table_fn_call_over(self) -> global___TableFnCallOver: ...
    @property
    def table_merge(self) -> global___TableMerge: ...
    @property
    def table_sample(self) -> global___TableSample: ...
    @property
    def table_update(self) -> global___TableUpdate: ...
    @property
    def to_snowpark_pandas(self) -> global___ToSnowparkPandas: ...
    @property
    def truncated_expr(self) -> global___TruncatedExpr: ...
    @property
    def tuple_val(self) -> global___TupleVal: ...
    @property
    def udaf(self) -> global___Udaf: ...
    @property
    def udf(self) -> global___Udf: ...
    @property
    def udtf(self) -> global___Udtf: ...
    @property
    def write_copy_into_location(self) -> global___WriteCopyIntoLocation: ...
    @property
    def write_csv(self) -> global___WriteCsv: ...
    @property
    def write_insert_into(self) -> global___WriteInsertInto: ...
    @property
    def write_json(self) -> global___WriteJson: ...
    @property
    def write_pandas(self) -> global___WritePandas: ...
    @property
    def write_parquet(self) -> global___WriteParquet: ...
    @property
    def write_save(self) -> global___WriteSave: ...
    @property
    def write_table(self) -> global___WriteTable: ...
    def __init__(
        self,
        *,
        trait_bin_op: global___BinOp | None = ...,
        trait_column_fn: global___ColumnFn | None = ...,
        trait_const: global___Const | None = ...,
        trait_extension_expr: global___ExtensionExpr | None = ...,
        trait_fn_id_ref_expr: global___FnIdRefExpr | None = ...,
        trait_fn_name_ref_expr: global___FnNameRefExpr | None = ...,
        trait_read_file: global___ReadFile | None = ...,
        trait_unary_op: global___UnaryOp | None = ...,
        trait_write_file: global___WriteFile | None = ...,
        add: global___Add | None = ...,
        apply_expr: global___ApplyExpr | None = ...,
        big_decimal_val: global___BigDecimalVal | None = ...,
        big_int_val: global___BigIntVal | None = ...,
        binary_val: global___BinaryVal | None = ...,
        bit_and: global___BitAnd | None = ...,
        bit_or: global___BitOr | None = ...,
        bit_xor: global___BitXor | None = ...,
        bool_val: global___BoolVal | None = ...,
        builtin_fn: global___BuiltinFn | None = ...,
        call_table_function_expr: global___CallTableFunctionExpr | None = ...,
        column_alias: global___ColumnAlias | None = ...,
        column_apply__int: global___ColumnApply_Int | None = ...,
        column_apply__string: global___ColumnApply_String | None = ...,
        column_asc: global___ColumnAsc | None = ...,
        column_between: global___ColumnBetween | None = ...,
        column_case_expr: global___ColumnCaseExpr | None = ...,
        column_cast: global___ColumnCast | None = ...,
        column_desc: global___ColumnDesc | None = ...,
        column_equal_nan: global___ColumnEqualNan | None = ...,
        column_equal_null: global___ColumnEqualNull | None = ...,
        column_in: global___ColumnIn | None = ...,
        column_is_not_null: global___ColumnIsNotNull | None = ...,
        column_is_null: global___ColumnIsNull | None = ...,
        column_over: global___ColumnOver | None = ...,
        column_regexp: global___ColumnRegexp | None = ...,
        column_string_collate: global___ColumnStringCollate | None = ...,
        column_string_contains: global___ColumnStringContains | None = ...,
        column_string_ends_with: global___ColumnStringEndsWith | None = ...,
        column_string_like: global___ColumnStringLike | None = ...,
        column_string_starts_with: global___ColumnStringStartsWith | None = ...,
        column_string_substr: global___ColumnStringSubstr | None = ...,
        column_try_cast: global___ColumnTryCast | None = ...,
        column_within_group: global___ColumnWithinGroup | None = ...,
        create_dataframe: global___CreateDataframe | None = ...,
        dataframe_agg: global___DataframeAgg | None = ...,
        dataframe_alias: global___DataframeAlias | None = ...,
        dataframe_analytics_compute_lag: global___DataframeAnalyticsComputeLag | None = ...,
        dataframe_analytics_compute_lead: global___DataframeAnalyticsComputeLead | None = ...,
        dataframe_analytics_cumulative_agg: global___DataframeAnalyticsCumulativeAgg | None = ...,
        dataframe_analytics_moving_agg: global___DataframeAnalyticsMovingAgg | None = ...,
        dataframe_analytics_time_series_agg: global___DataframeAnalyticsTimeSeriesAgg | None = ...,
        dataframe_cache_result: global___DataframeCacheResult | None = ...,
        dataframe_col: global___DataframeCol | None = ...,
        dataframe_collect: global___DataframeCollect | None = ...,
        dataframe_copy_into_table: global___DataframeCopyIntoTable | None = ...,
        dataframe_count: global___DataframeCount | None = ...,
        dataframe_create_or_replace_dynamic_table: global___DataframeCreateOrReplaceDynamicTable | None = ...,
        dataframe_create_or_replace_view: global___DataframeCreateOrReplaceView | None = ...,
        dataframe_cross_join: global___DataframeCrossJoin | None = ...,
        dataframe_cube: global___DataframeCube | None = ...,
        dataframe_describe: global___DataframeDescribe | None = ...,
        dataframe_distinct: global___DataframeDistinct | None = ...,
        dataframe_drop: global___DataframeDrop | None = ...,
        dataframe_drop_duplicates: global___DataframeDropDuplicates | None = ...,
        dataframe_except: global___DataframeExcept | None = ...,
        dataframe_filter: global___DataframeFilter | None = ...,
        dataframe_first: global___DataframeFirst | None = ...,
        dataframe_flatten: global___DataframeFlatten | None = ...,
        dataframe_group_by: global___DataframeGroupBy | None = ...,
        dataframe_group_by_grouping_sets: global___DataframeGroupByGroupingSets | None = ...,
        dataframe_intersect: global___DataframeIntersect | None = ...,
        dataframe_join: global___DataframeJoin | None = ...,
        dataframe_join_table_function: global___DataframeJoinTableFunction | None = ...,
        dataframe_limit: global___DataframeLimit | None = ...,
        dataframe_na_drop__python: global___DataframeNaDrop_Python | None = ...,
        dataframe_na_drop__scala: global___DataframeNaDrop_Scala | None = ...,
        dataframe_na_fill: global___DataframeNaFill | None = ...,
        dataframe_na_replace: global___DataframeNaReplace | None = ...,
        dataframe_natural_join: global___DataframeNaturalJoin | None = ...,
        dataframe_pivot: global___DataframePivot | None = ...,
        dataframe_random_split: global___DataframeRandomSplit | None = ...,
        dataframe_reader: global___DataframeReader | None = ...,
        dataframe_ref: global___DataframeRef | None = ...,
        dataframe_rename: global___DataframeRename | None = ...,
        dataframe_rollup: global___DataframeRollup | None = ...,
        dataframe_sample: global___DataframeSample | None = ...,
        dataframe_select: global___DataframeSelect | None = ...,
        dataframe_show: global___DataframeShow | None = ...,
        dataframe_sort: global___DataframeSort | None = ...,
        dataframe_stat_approx_quantile: global___DataframeStatApproxQuantile | None = ...,
        dataframe_stat_corr: global___DataframeStatCorr | None = ...,
        dataframe_stat_cov: global___DataframeStatCov | None = ...,
        dataframe_stat_cross_tab: global___DataframeStatCrossTab | None = ...,
        dataframe_stat_sample_by: global___DataframeStatSampleBy | None = ...,
        dataframe_to_df: global___DataframeToDf | None = ...,
        dataframe_to_local_iterator: global___DataframeToLocalIterator | None = ...,
        dataframe_to_pandas: global___DataframeToPandas | None = ...,
        dataframe_to_pandas_batches: global___DataframeToPandasBatches | None = ...,
        dataframe_union: global___DataframeUnion | None = ...,
        dataframe_unpivot: global___DataframeUnpivot | None = ...,
        dataframe_with_column: global___DataframeWithColumn | None = ...,
        dataframe_with_column_renamed: global___DataframeWithColumnRenamed | None = ...,
        dataframe_with_columns: global___DataframeWithColumns | None = ...,
        dataframe_writer: global___DataframeWriter | None = ...,
        datatype_val: global___DatatypeVal | None = ...,
        div: global___Div | None = ...,
        eq: global___Eq | None = ...,
        flatten: global___Flatten | None = ...,
        float64_val: global___Float64Val | None = ...,
        fn_ref: global___FnRef | None = ...,
        generator: global___Generator | None = ...,
        geq: global___Geq | None = ...,
        grouping_sets: global___GroupingSets | None = ...,
        gt: global___Gt | None = ...,
        indirect_table_fn_id_ref: global___IndirectTableFnIdRef | None = ...,
        indirect_table_fn_name_ref: global___IndirectTableFnNameRef | None = ...,
        int64_val: global___Int64Val | None = ...,
        leq: global___Leq | None = ...,
        list_val: global___ListVal | None = ...,
        lt: global___Lt | None = ...,
        merge_delete_when_matched_clause: global___MergeDeleteWhenMatchedClause | None = ...,
        merge_insert_when_not_matched_clause: global___MergeInsertWhenNotMatchedClause | None = ...,
        merge_update_when_matched_clause: global___MergeUpdateWhenMatchedClause | None = ...,
        mod: global___Mod | None = ...,
        mul: global___Mul | None = ...,
        neg: global___Neg | None = ...,
        neq: global___Neq | None = ...,
        null_val: global___NullVal | None = ...,
        object_get_item: global___ObjectGetItem | None = ...,
        pow: global___Pow | None = ...,
        python_date_val: global___PythonDateVal | None = ...,
        python_time_val: global___PythonTimeVal | None = ...,
        python_timestamp_val: global___PythonTimestampVal | None = ...,
        range: global___Range | None = ...,
        read_avro: global___ReadAvro | None = ...,
        read_csv: global___ReadCsv | None = ...,
        read_json: global___ReadJson | None = ...,
        read_load: global___ReadLoad | None = ...,
        read_orc: global___ReadOrc | None = ...,
        read_parquet: global___ReadParquet | None = ...,
        read_table: global___ReadTable | None = ...,
        read_xml: global___ReadXml | None = ...,
        redacted_const: global___RedactedConst | None = ...,
        relational_grouped_dataframe_agg: global___RelationalGroupedDataframeAgg | None = ...,
        relational_grouped_dataframe_apply_in_pandas: global___RelationalGroupedDataframeApplyInPandas | None = ...,
        relational_grouped_dataframe_builtin: global___RelationalGroupedDataframeBuiltin | None = ...,
        relational_grouped_dataframe_pivot: global___RelationalGroupedDataframePivot | None = ...,
        relational_grouped_dataframe_ref: global___RelationalGroupedDataframeRef | None = ...,
        row: global___Row | None = ...,
        seq_map_val: global___SeqMapVal | None = ...,
        session_table_function: global___SessionTableFunction | None = ...,
        sql: global___Sql | None = ...,
        sql_expr: global___SqlExpr | None = ...,
        stored_procedure: global___StoredProcedure | None = ...,
        string_val: global___StringVal | None = ...,
        sub: global___Sub | None = ...,
        table: global___Table | None = ...,
        table_delete: global___TableDelete | None = ...,
        table_drop_table: global___TableDropTable | None = ...,
        table_fn_call_alias: global___TableFnCallAlias | None = ...,
        table_fn_call_over: global___TableFnCallOver | None = ...,
        table_merge: global___TableMerge | None = ...,
        table_sample: global___TableSample | None = ...,
        table_update: global___TableUpdate | None = ...,
        to_snowpark_pandas: global___ToSnowparkPandas | None = ...,
        truncated_expr: global___TruncatedExpr | None = ...,
        tuple_val: global___TupleVal | None = ...,
        udaf: global___Udaf | None = ...,
        udf: global___Udf | None = ...,
        udtf: global___Udtf | None = ...,
        write_copy_into_location: global___WriteCopyIntoLocation | None = ...,
        write_csv: global___WriteCsv | None = ...,
        write_insert_into: global___WriteInsertInto | None = ...,
        write_json: global___WriteJson | None = ...,
        write_pandas: global___WritePandas | None = ...,
        write_parquet: global___WriteParquet | None = ...,
        write_save: global___WriteSave | None = ...,
        write_table: global___WriteTable | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["add", b"add", "and", b"and", "apply_expr", b"apply_expr", "big_decimal_val", b"big_decimal_val", "big_int_val", b"big_int_val", "binary_val", b"binary_val", "bit_and", b"bit_and", "bit_or", b"bit_or", "bit_xor", b"bit_xor", "bool_val", b"bool_val", "builtin_fn", b"builtin_fn", "call_table_function_expr", b"call_table_function_expr", "column_alias", b"column_alias", "column_apply__int", b"column_apply__int", "column_apply__string", b"column_apply__string", "column_asc", b"column_asc", "column_between", b"column_between", "column_case_expr", b"column_case_expr", "column_cast", b"column_cast", "column_desc", b"column_desc", "column_equal_nan", b"column_equal_nan", "column_equal_null", b"column_equal_null", "column_in", b"column_in", "column_is_not_null", b"column_is_not_null", "column_is_null", b"column_is_null", "column_over", b"column_over", "column_regexp", b"column_regexp", "column_string_collate", b"column_string_collate", "column_string_contains", b"column_string_contains", "column_string_ends_with", b"column_string_ends_with", "column_string_like", b"column_string_like", "column_string_starts_with", b"column_string_starts_with", "column_string_substr", b"column_string_substr", "column_try_cast", b"column_try_cast", "column_within_group", b"column_within_group", "create_dataframe", b"create_dataframe", "dataframe_agg", b"dataframe_agg", "dataframe_alias", b"dataframe_alias", "dataframe_analytics_compute_lag", b"dataframe_analytics_compute_lag", "dataframe_analytics_compute_lead", b"dataframe_analytics_compute_lead", "dataframe_analytics_cumulative_agg", b"dataframe_analytics_cumulative_agg", "dataframe_analytics_moving_agg", b"dataframe_analytics_moving_agg", "dataframe_analytics_time_series_agg", b"dataframe_analytics_time_series_agg", "dataframe_cache_result", b"dataframe_cache_result", "dataframe_col", b"dataframe_col", "dataframe_collect", b"dataframe_collect", "dataframe_copy_into_table", b"dataframe_copy_into_table", "dataframe_count", b"dataframe_count", "dataframe_create_or_replace_dynamic_table", b"dataframe_create_or_replace_dynamic_table", "dataframe_create_or_replace_view", b"dataframe_create_or_replace_view", "dataframe_cross_join", b"dataframe_cross_join", "dataframe_cube", b"dataframe_cube", "dataframe_describe", b"dataframe_describe", "dataframe_distinct", b"dataframe_distinct", "dataframe_drop", b"dataframe_drop", "dataframe_drop_duplicates", b"dataframe_drop_duplicates", "dataframe_except", b"dataframe_except", "dataframe_filter", b"dataframe_filter", "dataframe_first", b"dataframe_first", "dataframe_flatten", b"dataframe_flatten", "dataframe_group_by", b"dataframe_group_by", "dataframe_group_by_grouping_sets", b"dataframe_group_by_grouping_sets", "dataframe_intersect", b"dataframe_intersect", "dataframe_join", b"dataframe_join", "dataframe_join_table_function", b"dataframe_join_table_function", "dataframe_limit", b"dataframe_limit", "dataframe_na_drop__python", b"dataframe_na_drop__python", "dataframe_na_drop__scala", b"dataframe_na_drop__scala", "dataframe_na_fill", b"dataframe_na_fill", "dataframe_na_replace", b"dataframe_na_replace", "dataframe_natural_join", b"dataframe_natural_join", "dataframe_pivot", b"dataframe_pivot", "dataframe_random_split", b"dataframe_random_split", "dataframe_reader", b"dataframe_reader", "dataframe_ref", b"dataframe_ref", "dataframe_rename", b"dataframe_rename", "dataframe_rollup", b"dataframe_rollup", "dataframe_sample", b"dataframe_sample", "dataframe_select", b"dataframe_select", "dataframe_show", b"dataframe_show", "dataframe_sort", b"dataframe_sort", "dataframe_stat_approx_quantile", b"dataframe_stat_approx_quantile", "dataframe_stat_corr", b"dataframe_stat_corr", "dataframe_stat_cov", b"dataframe_stat_cov", "dataframe_stat_cross_tab", b"dataframe_stat_cross_tab", "dataframe_stat_sample_by", b"dataframe_stat_sample_by", "dataframe_to_df", b"dataframe_to_df", "dataframe_to_local_iterator", b"dataframe_to_local_iterator", "dataframe_to_pandas", b"dataframe_to_pandas", "dataframe_to_pandas_batches", b"dataframe_to_pandas_batches", "dataframe_union", b"dataframe_union", "dataframe_unpivot", b"dataframe_unpivot", "dataframe_with_column", b"dataframe_with_column", "dataframe_with_column_renamed", b"dataframe_with_column_renamed", "dataframe_with_columns", b"dataframe_with_columns", "dataframe_writer", b"dataframe_writer", "datatype_val", b"datatype_val", "div", b"div", "eq", b"eq", "flatten", b"flatten", "float64_val", b"float64_val", "fn_ref", b"fn_ref", "generator", b"generator", "geq", b"geq", "grouping_sets", b"grouping_sets", "gt", b"gt", "indirect_table_fn_id_ref", b"indirect_table_fn_id_ref", "indirect_table_fn_name_ref", b"indirect_table_fn_name_ref", "int64_val", b"int64_val", "leq", b"leq", "list_val", b"list_val", "lt", b"lt", "merge_delete_when_matched_clause", b"merge_delete_when_matched_clause", "merge_insert_when_not_matched_clause", b"merge_insert_when_not_matched_clause", "merge_update_when_matched_clause", b"merge_update_when_matched_clause", "mod", b"mod", "mul", b"mul", "neg", b"neg", "neq", b"neq", "not", b"not", "null_val", b"null_val", "object_get_item", b"object_get_item", "or", b"or", "pow", b"pow", "python_date_val", b"python_date_val", "python_time_val", b"python_time_val", "python_timestamp_val", b"python_timestamp_val", "range", b"range", "read_avro", b"read_avro", "read_csv", b"read_csv", "read_json", b"read_json", "read_load", b"read_load", "read_orc", b"read_orc", "read_parquet", b"read_parquet", "read_table", b"read_table", "read_xml", b"read_xml", "redacted_const", b"redacted_const", "relational_grouped_dataframe_agg", b"relational_grouped_dataframe_agg", "relational_grouped_dataframe_apply_in_pandas", b"relational_grouped_dataframe_apply_in_pandas", "relational_grouped_dataframe_builtin", b"relational_grouped_dataframe_builtin", "relational_grouped_dataframe_pivot", b"relational_grouped_dataframe_pivot", "relational_grouped_dataframe_ref", b"relational_grouped_dataframe_ref", "row", b"row", "seq_map_val", b"seq_map_val", "session_table_function", b"session_table_function", "sql", b"sql", "sql_expr", b"sql_expr", "stored_procedure", b"stored_procedure", "string_val", b"string_val", "sub", b"sub", "table", b"table", "table_delete", b"table_delete", "table_drop_table", b"table_drop_table", "table_fn_call_alias", b"table_fn_call_alias", "table_fn_call_over", b"table_fn_call_over", "table_merge", b"table_merge", "table_sample", b"table_sample", "table_update", b"table_update", "to_snowpark_pandas", b"to_snowpark_pandas", "trait_bin_op", b"trait_bin_op", "trait_column_fn", b"trait_column_fn", "trait_const", b"trait_const", "trait_extension_expr", b"trait_extension_expr", "trait_fn_id_ref_expr", b"trait_fn_id_ref_expr", "trait_fn_name_ref_expr", b"trait_fn_name_ref_expr", "trait_read_file", b"trait_read_file", "trait_unary_op", b"trait_unary_op", "trait_write_file", b"trait_write_file", "truncated_expr", b"truncated_expr", "tuple_val", b"tuple_val", "udaf", b"udaf", "udf", b"udf", "udtf", b"udtf", "variant", b"variant", "write_copy_into_location", b"write_copy_into_location", "write_csv", b"write_csv", "write_insert_into", b"write_insert_into", "write_json", b"write_json", "write_pandas", b"write_pandas", "write_parquet", b"write_parquet", "write_save", b"write_save", "write_table", b"write_table"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["add", b"add", "and", b"and", "apply_expr", b"apply_expr", "big_decimal_val", b"big_decimal_val", "big_int_val", b"big_int_val", "binary_val", b"binary_val", "bit_and", b"bit_and", "bit_or", b"bit_or", "bit_xor", b"bit_xor", "bool_val", b"bool_val", "builtin_fn", b"builtin_fn", "call_table_function_expr", b"call_table_function_expr", "column_alias", b"column_alias", "column_apply__int", b"column_apply__int", "column_apply__string", b"column_apply__string", "column_asc", b"column_asc", "column_between", b"column_between", "column_case_expr", b"column_case_expr", "column_cast", b"column_cast", "column_desc", b"column_desc", "column_equal_nan", b"column_equal_nan", "column_equal_null", b"column_equal_null", "column_in", b"column_in", "column_is_not_null", b"column_is_not_null", "column_is_null", b"column_is_null", "column_over", b"column_over", "column_regexp", b"column_regexp", "column_string_collate", b"column_string_collate", "column_string_contains", b"column_string_contains", "column_string_ends_with", b"column_string_ends_with", "column_string_like", b"column_string_like", "column_string_starts_with", b"column_string_starts_with", "column_string_substr", b"column_string_substr", "column_try_cast", b"column_try_cast", "column_within_group", b"column_within_group", "create_dataframe", b"create_dataframe", "dataframe_agg", b"dataframe_agg", "dataframe_alias", b"dataframe_alias", "dataframe_analytics_compute_lag", b"dataframe_analytics_compute_lag", "dataframe_analytics_compute_lead", b"dataframe_analytics_compute_lead", "dataframe_analytics_cumulative_agg", b"dataframe_analytics_cumulative_agg", "dataframe_analytics_moving_agg", b"dataframe_analytics_moving_agg", "dataframe_analytics_time_series_agg", b"dataframe_analytics_time_series_agg", "dataframe_cache_result", b"dataframe_cache_result", "dataframe_col", b"dataframe_col", "dataframe_collect", b"dataframe_collect", "dataframe_copy_into_table", b"dataframe_copy_into_table", "dataframe_count", b"dataframe_count", "dataframe_create_or_replace_dynamic_table", b"dataframe_create_or_replace_dynamic_table", "dataframe_create_or_replace_view", b"dataframe_create_or_replace_view", "dataframe_cross_join", b"dataframe_cross_join", "dataframe_cube", b"dataframe_cube", "dataframe_describe", b"dataframe_describe", "dataframe_distinct", b"dataframe_distinct", "dataframe_drop", b"dataframe_drop", "dataframe_drop_duplicates", b"dataframe_drop_duplicates", "dataframe_except", b"dataframe_except", "dataframe_filter", b"dataframe_filter", "dataframe_first", b"dataframe_first", "dataframe_flatten", b"dataframe_flatten", "dataframe_group_by", b"dataframe_group_by", "dataframe_group_by_grouping_sets", b"dataframe_group_by_grouping_sets", "dataframe_intersect", b"dataframe_intersect", "dataframe_join", b"dataframe_join", "dataframe_join_table_function", b"dataframe_join_table_function", "dataframe_limit", b"dataframe_limit", "dataframe_na_drop__python", b"dataframe_na_drop__python", "dataframe_na_drop__scala", b"dataframe_na_drop__scala", "dataframe_na_fill", b"dataframe_na_fill", "dataframe_na_replace", b"dataframe_na_replace", "dataframe_natural_join", b"dataframe_natural_join", "dataframe_pivot", b"dataframe_pivot", "dataframe_random_split", b"dataframe_random_split", "dataframe_reader", b"dataframe_reader", "dataframe_ref", b"dataframe_ref", "dataframe_rename", b"dataframe_rename", "dataframe_rollup", b"dataframe_rollup", "dataframe_sample", b"dataframe_sample", "dataframe_select", b"dataframe_select", "dataframe_show", b"dataframe_show", "dataframe_sort", b"dataframe_sort", "dataframe_stat_approx_quantile", b"dataframe_stat_approx_quantile", "dataframe_stat_corr", b"dataframe_stat_corr", "dataframe_stat_cov", b"dataframe_stat_cov", "dataframe_stat_cross_tab", b"dataframe_stat_cross_tab", "dataframe_stat_sample_by", b"dataframe_stat_sample_by", "dataframe_to_df", b"dataframe_to_df", "dataframe_to_local_iterator", b"dataframe_to_local_iterator", "dataframe_to_pandas", b"dataframe_to_pandas", "dataframe_to_pandas_batches", b"dataframe_to_pandas_batches", "dataframe_union", b"dataframe_union", "dataframe_unpivot", b"dataframe_unpivot", "dataframe_with_column", b"dataframe_with_column", "dataframe_with_column_renamed", b"dataframe_with_column_renamed", "dataframe_with_columns", b"dataframe_with_columns", "dataframe_writer", b"dataframe_writer", "datatype_val", b"datatype_val", "div", b"div", "eq", b"eq", "flatten", b"flatten", "float64_val", b"float64_val", "fn_ref", b"fn_ref", "generator", b"generator", "geq", b"geq", "grouping_sets", b"grouping_sets", "gt", b"gt", "indirect_table_fn_id_ref", b"indirect_table_fn_id_ref", "indirect_table_fn_name_ref", b"indirect_table_fn_name_ref", "int64_val", b"int64_val", "leq", b"leq", "list_val", b"list_val", "lt", b"lt", "merge_delete_when_matched_clause", b"merge_delete_when_matched_clause", "merge_insert_when_not_matched_clause", b"merge_insert_when_not_matched_clause", "merge_update_when_matched_clause", b"merge_update_when_matched_clause", "mod", b"mod", "mul", b"mul", "neg", b"neg", "neq", b"neq", "not", b"not", "null_val", b"null_val", "object_get_item", b"object_get_item", "or", b"or", "pow", b"pow", "python_date_val", b"python_date_val", "python_time_val", b"python_time_val", "python_timestamp_val", b"python_timestamp_val", "range", b"range", "read_avro", b"read_avro", "read_csv", b"read_csv", "read_json", b"read_json", "read_load", b"read_load", "read_orc", b"read_orc", "read_parquet", b"read_parquet", "read_table", b"read_table", "read_xml", b"read_xml", "redacted_const", b"redacted_const", "relational_grouped_dataframe_agg", b"relational_grouped_dataframe_agg", "relational_grouped_dataframe_apply_in_pandas", b"relational_grouped_dataframe_apply_in_pandas", "relational_grouped_dataframe_builtin", b"relational_grouped_dataframe_builtin", "relational_grouped_dataframe_pivot", b"relational_grouped_dataframe_pivot", "relational_grouped_dataframe_ref", b"relational_grouped_dataframe_ref", "row", b"row", "seq_map_val", b"seq_map_val", "session_table_function", b"session_table_function", "sql", b"sql", "sql_expr", b"sql_expr", "stored_procedure", b"stored_procedure", "string_val", b"string_val", "sub", b"sub", "table", b"table", "table_delete", b"table_delete", "table_drop_table", b"table_drop_table", "table_fn_call_alias", b"table_fn_call_alias", "table_fn_call_over", b"table_fn_call_over", "table_merge", b"table_merge", "table_sample", b"table_sample", "table_update", b"table_update", "to_snowpark_pandas", b"to_snowpark_pandas", "trait_bin_op", b"trait_bin_op", "trait_column_fn", b"trait_column_fn", "trait_const", b"trait_const", "trait_extension_expr", b"trait_extension_expr", "trait_fn_id_ref_expr", b"trait_fn_id_ref_expr", "trait_fn_name_ref_expr", b"trait_fn_name_ref_expr", "trait_read_file", b"trait_read_file", "trait_unary_op", b"trait_unary_op", "trait_write_file", b"trait_write_file", "truncated_expr", b"truncated_expr", "tuple_val", b"tuple_val", "udaf", b"udaf", "udf", b"udf", "udtf", b"udtf", "variant", b"variant", "write_copy_into_location", b"write_copy_into_location", "write_csv", b"write_csv", "write_insert_into", b"write_insert_into", "write_json", b"write_json", "write_pandas", b"write_pandas", "write_parquet", b"write_parquet", "write_save", b"write_save", "write_table", b"write_table"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["variant", b"variant"]) -> typing.Literal["trait_bin_op", "trait_column_fn", "trait_const", "trait_extension_expr", "trait_fn_id_ref_expr", "trait_fn_name_ref_expr", "trait_read_file", "trait_unary_op", "trait_write_file", "add", "and", "apply_expr", "big_decimal_val", "big_int_val", "binary_val", "bit_and", "bit_or", "bit_xor", "bool_val", "builtin_fn", "call_table_function_expr", "column_alias", "column_apply__int", "column_apply__string", "column_asc", "column_between", "column_case_expr", "column_cast", "column_desc", "column_equal_nan", "column_equal_null", "column_in", "column_is_not_null", "column_is_null", "column_over", "column_regexp", "column_string_collate", "column_string_contains", "column_string_ends_with", "column_string_like", "column_string_starts_with", "column_string_substr", "column_try_cast", "column_within_group", "create_dataframe", "dataframe_agg", "dataframe_alias", "dataframe_analytics_compute_lag", "dataframe_analytics_compute_lead", "dataframe_analytics_cumulative_agg", "dataframe_analytics_moving_agg", "dataframe_analytics_time_series_agg", "dataframe_cache_result", "dataframe_col", "dataframe_collect", "dataframe_copy_into_table", "dataframe_count", "dataframe_create_or_replace_dynamic_table", "dataframe_create_or_replace_view", "dataframe_cross_join", "dataframe_cube", "dataframe_describe", "dataframe_distinct", "dataframe_drop", "dataframe_drop_duplicates", "dataframe_except", "dataframe_filter", "dataframe_first", "dataframe_flatten", "dataframe_group_by", "dataframe_group_by_grouping_sets", "dataframe_intersect", "dataframe_join", "dataframe_join_table_function", "dataframe_limit", "dataframe_na_drop__python", "dataframe_na_drop__scala", "dataframe_na_fill", "dataframe_na_replace", "dataframe_natural_join", "dataframe_pivot", "dataframe_random_split", "dataframe_reader", "dataframe_ref", "dataframe_rename", "dataframe_rollup", "dataframe_sample", "dataframe_select", "dataframe_show", "dataframe_sort", "dataframe_stat_approx_quantile", "dataframe_stat_corr", "dataframe_stat_cov", "dataframe_stat_cross_tab", "dataframe_stat_sample_by", "dataframe_to_df", "dataframe_to_local_iterator", "dataframe_to_pandas", "dataframe_to_pandas_batches", "dataframe_union", "dataframe_unpivot", "dataframe_with_column", "dataframe_with_column_renamed", "dataframe_with_columns", "dataframe_writer", "datatype_val", "div", "eq", "flatten", "float64_val", "fn_ref", "generator", "geq", "grouping_sets", "gt", "indirect_table_fn_id_ref", "indirect_table_fn_name_ref", "int64_val", "leq", "list_val", "lt", "merge_delete_when_matched_clause", "merge_insert_when_not_matched_clause", "merge_update_when_matched_clause", "mod", "mul", "neg", "neq", "not", "null_val", "object_get_item", "or", "pow", "python_date_val", "python_time_val", "python_timestamp_val", "range", "read_avro", "read_csv", "read_json", "read_load", "read_orc", "read_parquet", "read_table", "read_xml", "redacted_const", "relational_grouped_dataframe_agg", "relational_grouped_dataframe_apply_in_pandas", "relational_grouped_dataframe_builtin", "relational_grouped_dataframe_pivot", "relational_grouped_dataframe_ref", "row", "seq_map_val", "session_table_function", "sql", "sql_expr", "stored_procedure", "string_val", "sub", "table", "table_delete", "table_drop_table", "table_fn_call_alias", "table_fn_call_over", "table_merge", "table_sample", "table_update", "to_snowpark_pandas", "truncated_expr", "tuple_val", "udaf", "udf", "udtf", "write_copy_into_location", "write_csv", "write_insert_into", "write_json", "write_pandas", "write_parquet", "write_save", "write_table"] | None: ...

global___Expr = Expr

@typing.final
class ExprArgList(google.protobuf.message.Message):
    """ast.ir:131"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ARGS_FIELD_NUMBER: builtins.int
    VARIADIC_FIELD_NUMBER: builtins.int
    variadic: builtins.bool
    @property
    def args(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Expr]: ...
    def __init__(
        self,
        *,
        args: collections.abc.Iterable[global___Expr] | None = ...,
        variadic: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["args", b"args", "variadic", b"variadic"]) -> None: ...

global___ExprArgList = ExprArgList

@typing.final
class ExtensionError(google.protobuf.message.Message):
    """ast.ir:84"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ATTRS_FIELD_NUMBER: builtins.int
    BIND_ID_FIELD_NUMBER: builtins.int
    KIND_FIELD_NUMBER: builtins.int
    UID_FIELD_NUMBER: builtins.int
    bind_id: builtins.int
    kind: builtins.str
    uid: builtins.int
    @property
    def attrs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_Expr]: ...
    def __init__(
        self,
        *,
        attrs: collections.abc.Iterable[global___Tuple_String_Expr] | None = ...,
        bind_id: builtins.int = ...,
        kind: builtins.str = ...,
        uid: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["attrs", b"attrs", "bind_id", b"bind_id", "kind", b"kind", "uid", b"uid"]) -> None: ...

global___ExtensionError = ExtensionError

@typing.final
class ExtensionEvalResult(google.protobuf.message.Message):
    """ast.ir:103"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ATTRS_FIELD_NUMBER: builtins.int
    KIND_FIELD_NUMBER: builtins.int
    kind: builtins.str
    @property
    def attrs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_Expr]: ...
    def __init__(
        self,
        *,
        attrs: collections.abc.Iterable[global___Tuple_String_Expr] | None = ...,
        kind: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["attrs", b"attrs", "kind", b"kind"]) -> None: ...

global___ExtensionEvalResult = ExtensionEvalResult

@typing.final
class ExtensionExpr(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DUMMY_FIELD_NUMBER: builtins.int
    dummy: builtins.bool
    def __init__(
        self,
        *,
        dummy: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["dummy", b"dummy", "variant", b"variant"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["dummy", b"dummy", "variant", b"variant"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["variant", b"variant"]) -> typing.Literal["dummy"] | None: ...

global___ExtensionExpr = ExtensionExpr

@typing.final
class ExtensionStmt(google.protobuf.message.Message):
    """ast.ir:62"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ATTRS_FIELD_NUMBER: builtins.int
    KIND_FIELD_NUMBER: builtins.int
    kind: builtins.str
    @property
    def attrs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_Expr]: ...
    def __init__(
        self,
        *,
        attrs: collections.abc.Iterable[global___Tuple_String_Expr] | None = ...,
        kind: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["attrs", b"attrs", "kind", b"kind"]) -> None: ...

global___ExtensionStmt = ExtensionStmt

@typing.final
class Flatten(google.protobuf.message.Message):
    """dataframe.ir:69"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    INPUT_FIELD_NUMBER: builtins.int
    MODE_FIELD_NUMBER: builtins.int
    OUTER_FIELD_NUMBER: builtins.int
    PATH_FIELD_NUMBER: builtins.int
    RECURSIVE_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    outer: builtins.bool
    recursive: builtins.bool
    @property
    def input(self) -> global___Expr: ...
    @property
    def mode(self) -> global___FlattenMode: ...
    @property
    def path(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        input: global___Expr | None = ...,
        mode: global___FlattenMode | None = ...,
        outer: builtins.bool = ...,
        path: google.protobuf.wrappers_pb2.StringValue | None = ...,
        recursive: builtins.bool = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["input", b"input", "mode", b"mode", "path", b"path", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["input", b"input", "mode", b"mode", "outer", b"outer", "path", b"path", "recursive", b"recursive", "src", b"src"]) -> None: ...

global___Flatten = Flatten

@typing.final
class Float64Val(google.protobuf.message.Message):
    """const.ir:37"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SRC_FIELD_NUMBER: builtins.int
    V_FIELD_NUMBER: builtins.int
    v: builtins.float
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        src: global___SrcPosition | None = ...,
        v: builtins.float = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["src", b"src", "v", b"v"]) -> None: ...

global___Float64Val = Float64Val

@typing.final
class FnIdRefExpr(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FN_REF_FIELD_NUMBER: builtins.int
    INDIRECT_TABLE_FN_ID_REF_FIELD_NUMBER: builtins.int
    @property
    def fn_ref(self) -> global___FnRef: ...
    @property
    def indirect_table_fn_id_ref(self) -> global___IndirectTableFnIdRef: ...
    def __init__(
        self,
        *,
        fn_ref: global___FnRef | None = ...,
        indirect_table_fn_id_ref: global___IndirectTableFnIdRef | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["fn_ref", b"fn_ref", "indirect_table_fn_id_ref", b"indirect_table_fn_id_ref", "variant", b"variant"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["fn_ref", b"fn_ref", "indirect_table_fn_id_ref", b"indirect_table_fn_id_ref", "variant", b"variant"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["variant", b"variant"]) -> typing.Literal["fn_ref", "indirect_table_fn_id_ref"] | None: ...

global___FnIdRefExpr = FnIdRefExpr

@typing.final
class FnNameRefExpr(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BUILTIN_FN_FIELD_NUMBER: builtins.int
    CALL_TABLE_FUNCTION_EXPR_FIELD_NUMBER: builtins.int
    INDIRECT_TABLE_FN_NAME_REF_FIELD_NUMBER: builtins.int
    STORED_PROCEDURE_FIELD_NUMBER: builtins.int
    UDAF_FIELD_NUMBER: builtins.int
    UDF_FIELD_NUMBER: builtins.int
    UDTF_FIELD_NUMBER: builtins.int
    @property
    def builtin_fn(self) -> global___BuiltinFn: ...
    @property
    def call_table_function_expr(self) -> global___CallTableFunctionExpr: ...
    @property
    def indirect_table_fn_name_ref(self) -> global___IndirectTableFnNameRef: ...
    @property
    def stored_procedure(self) -> global___StoredProcedure: ...
    @property
    def udaf(self) -> global___Udaf: ...
    @property
    def udf(self) -> global___Udf: ...
    @property
    def udtf(self) -> global___Udtf: ...
    def __init__(
        self,
        *,
        builtin_fn: global___BuiltinFn | None = ...,
        call_table_function_expr: global___CallTableFunctionExpr | None = ...,
        indirect_table_fn_name_ref: global___IndirectTableFnNameRef | None = ...,
        stored_procedure: global___StoredProcedure | None = ...,
        udaf: global___Udaf | None = ...,
        udf: global___Udf | None = ...,
        udtf: global___Udtf | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["builtin_fn", b"builtin_fn", "call_table_function_expr", b"call_table_function_expr", "indirect_table_fn_name_ref", b"indirect_table_fn_name_ref", "stored_procedure", b"stored_procedure", "udaf", b"udaf", "udf", b"udf", "udtf", b"udtf", "variant", b"variant"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["builtin_fn", b"builtin_fn", "call_table_function_expr", b"call_table_function_expr", "indirect_table_fn_name_ref", b"indirect_table_fn_name_ref", "stored_procedure", b"stored_procedure", "udaf", b"udaf", "udf", b"udf", "udtf", b"udtf", "variant", b"variant"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["variant", b"variant"]) -> typing.Literal["builtin_fn", "call_table_function_expr", "indirect_table_fn_name_ref", "stored_procedure", "udaf", "udf", "udtf"] | None: ...

global___FnNameRefExpr = FnNameRefExpr

@typing.final
class FnRef(google.protobuf.message.Message):
    """fn.ir:19"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    id: builtins.int
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        id: builtins.int = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["id", b"id", "src", b"src"]) -> None: ...

global___FnRef = FnRef

@typing.final
class Generator(google.protobuf.message.Message):
    """dataframe.ir:79"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLUMNS_FIELD_NUMBER: builtins.int
    ROW_COUNT_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    TIME_LIMIT_SECONDS_FIELD_NUMBER: builtins.int
    row_count: builtins.int
    time_limit_seconds: builtins.int
    @property
    def columns(self) -> global___ExprArgList: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        columns: global___ExprArgList | None = ...,
        row_count: builtins.int = ...,
        src: global___SrcPosition | None = ...,
        time_limit_seconds: builtins.int = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["columns", b"columns", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["columns", b"columns", "row_count", b"row_count", "src", b"src", "time_limit_seconds", b"time_limit_seconds"]) -> None: ...

global___Generator = Generator

@typing.final
class Geq(google.protobuf.message.Message):
    """op.ir:34"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LHS_FIELD_NUMBER: builtins.int
    RHS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def lhs(self) -> global___Expr: ...
    @property
    def rhs(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        lhs: global___Expr | None = ...,
        rhs: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> None: ...

global___Geq = Geq

@typing.final
class GroupingSets(google.protobuf.message.Message):
    """dataframe.ir:338"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SETS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def sets(self) -> global___ExprArgList: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        sets: global___ExprArgList | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["sets", b"sets", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["sets", b"sets", "src", b"src"]) -> None: ...

global___GroupingSets = GroupingSets

@typing.final
class Gt(google.protobuf.message.Message):
    """op.ir:32"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LHS_FIELD_NUMBER: builtins.int
    RHS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def lhs(self) -> global___Expr: ...
    @property
    def rhs(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        lhs: global___Expr | None = ...,
        rhs: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> None: ...

global___Gt = Gt

@typing.final
class HasSrcPosition(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TRAIT_BIN_OP_FIELD_NUMBER: builtins.int
    TRAIT_COLUMN_FN_FIELD_NUMBER: builtins.int
    TRAIT_CONST_FIELD_NUMBER: builtins.int
    TRAIT_EXPR_FIELD_NUMBER: builtins.int
    TRAIT_EXTENSION_EXPR_FIELD_NUMBER: builtins.int
    TRAIT_FN_ID_REF_EXPR_FIELD_NUMBER: builtins.int
    TRAIT_FN_NAME_REF_EXPR_FIELD_NUMBER: builtins.int
    TRAIT_READ_FILE_FIELD_NUMBER: builtins.int
    TRAIT_UNARY_OP_FIELD_NUMBER: builtins.int
    TRAIT_WINDOW_SPEC_EXPR_FIELD_NUMBER: builtins.int
    TRAIT_WRITE_FILE_FIELD_NUMBER: builtins.int
    ADD_FIELD_NUMBER: builtins.int
    AND_FIELD_NUMBER: builtins.int
    APPLY_EXPR_FIELD_NUMBER: builtins.int
    BIG_DECIMAL_VAL_FIELD_NUMBER: builtins.int
    BIG_INT_VAL_FIELD_NUMBER: builtins.int
    BINARY_VAL_FIELD_NUMBER: builtins.int
    BIT_AND_FIELD_NUMBER: builtins.int
    BIT_OR_FIELD_NUMBER: builtins.int
    BIT_XOR_FIELD_NUMBER: builtins.int
    BOOL_VAL_FIELD_NUMBER: builtins.int
    BUILTIN_FN_FIELD_NUMBER: builtins.int
    CALL_TABLE_FUNCTION_EXPR_FIELD_NUMBER: builtins.int
    COLUMN_ALIAS_FIELD_NUMBER: builtins.int
    COLUMN_APPLY__INT_FIELD_NUMBER: builtins.int
    COLUMN_APPLY__STRING_FIELD_NUMBER: builtins.int
    COLUMN_ASC_FIELD_NUMBER: builtins.int
    COLUMN_BETWEEN_FIELD_NUMBER: builtins.int
    COLUMN_CASE_EXPR_FIELD_NUMBER: builtins.int
    COLUMN_CASE_EXPR_CLAUSE_FIELD_NUMBER: builtins.int
    COLUMN_CAST_FIELD_NUMBER: builtins.int
    COLUMN_DESC_FIELD_NUMBER: builtins.int
    COLUMN_EQUAL_NAN_FIELD_NUMBER: builtins.int
    COLUMN_EQUAL_NULL_FIELD_NUMBER: builtins.int
    COLUMN_IN_FIELD_NUMBER: builtins.int
    COLUMN_IS_NOT_NULL_FIELD_NUMBER: builtins.int
    COLUMN_IS_NULL_FIELD_NUMBER: builtins.int
    COLUMN_OVER_FIELD_NUMBER: builtins.int
    COLUMN_REGEXP_FIELD_NUMBER: builtins.int
    COLUMN_STRING_COLLATE_FIELD_NUMBER: builtins.int
    COLUMN_STRING_CONTAINS_FIELD_NUMBER: builtins.int
    COLUMN_STRING_ENDS_WITH_FIELD_NUMBER: builtins.int
    COLUMN_STRING_LIKE_FIELD_NUMBER: builtins.int
    COLUMN_STRING_STARTS_WITH_FIELD_NUMBER: builtins.int
    COLUMN_STRING_SUBSTR_FIELD_NUMBER: builtins.int
    COLUMN_TRY_CAST_FIELD_NUMBER: builtins.int
    COLUMN_WITHIN_GROUP_FIELD_NUMBER: builtins.int
    CREATE_DATAFRAME_FIELD_NUMBER: builtins.int
    DATAFRAME_AGG_FIELD_NUMBER: builtins.int
    DATAFRAME_ALIAS_FIELD_NUMBER: builtins.int
    DATAFRAME_ANALYTICS_COMPUTE_LAG_FIELD_NUMBER: builtins.int
    DATAFRAME_ANALYTICS_COMPUTE_LEAD_FIELD_NUMBER: builtins.int
    DATAFRAME_ANALYTICS_CUMULATIVE_AGG_FIELD_NUMBER: builtins.int
    DATAFRAME_ANALYTICS_MOVING_AGG_FIELD_NUMBER: builtins.int
    DATAFRAME_ANALYTICS_TIME_SERIES_AGG_FIELD_NUMBER: builtins.int
    DATAFRAME_CACHE_RESULT_FIELD_NUMBER: builtins.int
    DATAFRAME_COL_FIELD_NUMBER: builtins.int
    DATAFRAME_COLLECT_FIELD_NUMBER: builtins.int
    DATAFRAME_COPY_INTO_TABLE_FIELD_NUMBER: builtins.int
    DATAFRAME_COUNT_FIELD_NUMBER: builtins.int
    DATAFRAME_CREATE_OR_REPLACE_DYNAMIC_TABLE_FIELD_NUMBER: builtins.int
    DATAFRAME_CREATE_OR_REPLACE_VIEW_FIELD_NUMBER: builtins.int
    DATAFRAME_CROSS_JOIN_FIELD_NUMBER: builtins.int
    DATAFRAME_CUBE_FIELD_NUMBER: builtins.int
    DATAFRAME_DESCRIBE_FIELD_NUMBER: builtins.int
    DATAFRAME_DISTINCT_FIELD_NUMBER: builtins.int
    DATAFRAME_DROP_FIELD_NUMBER: builtins.int
    DATAFRAME_DROP_DUPLICATES_FIELD_NUMBER: builtins.int
    DATAFRAME_EXCEPT_FIELD_NUMBER: builtins.int
    DATAFRAME_FILTER_FIELD_NUMBER: builtins.int
    DATAFRAME_FIRST_FIELD_NUMBER: builtins.int
    DATAFRAME_FLATTEN_FIELD_NUMBER: builtins.int
    DATAFRAME_GROUP_BY_FIELD_NUMBER: builtins.int
    DATAFRAME_GROUP_BY_GROUPING_SETS_FIELD_NUMBER: builtins.int
    DATAFRAME_INTERSECT_FIELD_NUMBER: builtins.int
    DATAFRAME_JOIN_FIELD_NUMBER: builtins.int
    DATAFRAME_JOIN_TABLE_FUNCTION_FIELD_NUMBER: builtins.int
    DATAFRAME_LIMIT_FIELD_NUMBER: builtins.int
    DATAFRAME_NA_DROP__PYTHON_FIELD_NUMBER: builtins.int
    DATAFRAME_NA_DROP__SCALA_FIELD_NUMBER: builtins.int
    DATAFRAME_NA_FILL_FIELD_NUMBER: builtins.int
    DATAFRAME_NA_REPLACE_FIELD_NUMBER: builtins.int
    DATAFRAME_NATURAL_JOIN_FIELD_NUMBER: builtins.int
    DATAFRAME_PIVOT_FIELD_NUMBER: builtins.int
    DATAFRAME_RANDOM_SPLIT_FIELD_NUMBER: builtins.int
    DATAFRAME_READER_FIELD_NUMBER: builtins.int
    DATAFRAME_REF_FIELD_NUMBER: builtins.int
    DATAFRAME_RENAME_FIELD_NUMBER: builtins.int
    DATAFRAME_ROLLUP_FIELD_NUMBER: builtins.int
    DATAFRAME_SAMPLE_FIELD_NUMBER: builtins.int
    DATAFRAME_SELECT_FIELD_NUMBER: builtins.int
    DATAFRAME_SHOW_FIELD_NUMBER: builtins.int
    DATAFRAME_SORT_FIELD_NUMBER: builtins.int
    DATAFRAME_STAT_APPROX_QUANTILE_FIELD_NUMBER: builtins.int
    DATAFRAME_STAT_CORR_FIELD_NUMBER: builtins.int
    DATAFRAME_STAT_COV_FIELD_NUMBER: builtins.int
    DATAFRAME_STAT_CROSS_TAB_FIELD_NUMBER: builtins.int
    DATAFRAME_STAT_SAMPLE_BY_FIELD_NUMBER: builtins.int
    DATAFRAME_TO_DF_FIELD_NUMBER: builtins.int
    DATAFRAME_TO_LOCAL_ITERATOR_FIELD_NUMBER: builtins.int
    DATAFRAME_TO_PANDAS_FIELD_NUMBER: builtins.int
    DATAFRAME_TO_PANDAS_BATCHES_FIELD_NUMBER: builtins.int
    DATAFRAME_UNION_FIELD_NUMBER: builtins.int
    DATAFRAME_UNPIVOT_FIELD_NUMBER: builtins.int
    DATAFRAME_WITH_COLUMN_FIELD_NUMBER: builtins.int
    DATAFRAME_WITH_COLUMN_RENAMED_FIELD_NUMBER: builtins.int
    DATAFRAME_WITH_COLUMNS_FIELD_NUMBER: builtins.int
    DATAFRAME_WRITER_FIELD_NUMBER: builtins.int
    DATATYPE_VAL_FIELD_NUMBER: builtins.int
    DIV_FIELD_NUMBER: builtins.int
    EQ_FIELD_NUMBER: builtins.int
    FLATTEN_FIELD_NUMBER: builtins.int
    FLOAT64_VAL_FIELD_NUMBER: builtins.int
    FN_REF_FIELD_NUMBER: builtins.int
    GENERATOR_FIELD_NUMBER: builtins.int
    GEQ_FIELD_NUMBER: builtins.int
    GROUPING_SETS_FIELD_NUMBER: builtins.int
    GT_FIELD_NUMBER: builtins.int
    INDIRECT_TABLE_FN_ID_REF_FIELD_NUMBER: builtins.int
    INDIRECT_TABLE_FN_NAME_REF_FIELD_NUMBER: builtins.int
    INT64_VAL_FIELD_NUMBER: builtins.int
    LEQ_FIELD_NUMBER: builtins.int
    LIST_VAL_FIELD_NUMBER: builtins.int
    LT_FIELD_NUMBER: builtins.int
    MERGE_DELETE_WHEN_MATCHED_CLAUSE_FIELD_NUMBER: builtins.int
    MERGE_INSERT_WHEN_NOT_MATCHED_CLAUSE_FIELD_NUMBER: builtins.int
    MERGE_UPDATE_WHEN_MATCHED_CLAUSE_FIELD_NUMBER: builtins.int
    MOD_FIELD_NUMBER: builtins.int
    MUL_FIELD_NUMBER: builtins.int
    NAME_REF_FIELD_NUMBER: builtins.int
    NEG_FIELD_NUMBER: builtins.int
    NEQ_FIELD_NUMBER: builtins.int
    NOT_FIELD_NUMBER: builtins.int
    NULL_VAL_FIELD_NUMBER: builtins.int
    OBJECT_GET_ITEM_FIELD_NUMBER: builtins.int
    OR_FIELD_NUMBER: builtins.int
    POW_FIELD_NUMBER: builtins.int
    PYTHON_DATE_VAL_FIELD_NUMBER: builtins.int
    PYTHON_TIME_VAL_FIELD_NUMBER: builtins.int
    PYTHON_TIMESTAMP_VAL_FIELD_NUMBER: builtins.int
    RANGE_FIELD_NUMBER: builtins.int
    READ_AVRO_FIELD_NUMBER: builtins.int
    READ_CSV_FIELD_NUMBER: builtins.int
    READ_JSON_FIELD_NUMBER: builtins.int
    READ_LOAD_FIELD_NUMBER: builtins.int
    READ_ORC_FIELD_NUMBER: builtins.int
    READ_PARQUET_FIELD_NUMBER: builtins.int
    READ_TABLE_FIELD_NUMBER: builtins.int
    READ_XML_FIELD_NUMBER: builtins.int
    REDACTED_CONST_FIELD_NUMBER: builtins.int
    RELATIONAL_GROUPED_DATAFRAME_AGG_FIELD_NUMBER: builtins.int
    RELATIONAL_GROUPED_DATAFRAME_APPLY_IN_PANDAS_FIELD_NUMBER: builtins.int
    RELATIONAL_GROUPED_DATAFRAME_BUILTIN_FIELD_NUMBER: builtins.int
    RELATIONAL_GROUPED_DATAFRAME_PIVOT_FIELD_NUMBER: builtins.int
    RELATIONAL_GROUPED_DATAFRAME_REF_FIELD_NUMBER: builtins.int
    ROW_FIELD_NUMBER: builtins.int
    SEQ_MAP_VAL_FIELD_NUMBER: builtins.int
    SESSION_TABLE_FUNCTION_FIELD_NUMBER: builtins.int
    SQL_FIELD_NUMBER: builtins.int
    SQL_EXPR_FIELD_NUMBER: builtins.int
    STORED_PROCEDURE_FIELD_NUMBER: builtins.int
    STRING_VAL_FIELD_NUMBER: builtins.int
    SUB_FIELD_NUMBER: builtins.int
    TABLE_FIELD_NUMBER: builtins.int
    TABLE_DELETE_FIELD_NUMBER: builtins.int
    TABLE_DROP_TABLE_FIELD_NUMBER: builtins.int
    TABLE_FN_CALL_ALIAS_FIELD_NUMBER: builtins.int
    TABLE_FN_CALL_OVER_FIELD_NUMBER: builtins.int
    TABLE_MERGE_FIELD_NUMBER: builtins.int
    TABLE_SAMPLE_FIELD_NUMBER: builtins.int
    TABLE_UPDATE_FIELD_NUMBER: builtins.int
    TO_SNOWPARK_PANDAS_FIELD_NUMBER: builtins.int
    TRUNCATED_EXPR_FIELD_NUMBER: builtins.int
    TUPLE_VAL_FIELD_NUMBER: builtins.int
    UDAF_FIELD_NUMBER: builtins.int
    UDF_FIELD_NUMBER: builtins.int
    UDTF_FIELD_NUMBER: builtins.int
    WINDOW_SPEC_EMPTY_FIELD_NUMBER: builtins.int
    WINDOW_SPEC_ORDER_BY_FIELD_NUMBER: builtins.int
    WINDOW_SPEC_PARTITION_BY_FIELD_NUMBER: builtins.int
    WINDOW_SPEC_RANGE_BETWEEN_FIELD_NUMBER: builtins.int
    WINDOW_SPEC_ROWS_BETWEEN_FIELD_NUMBER: builtins.int
    WRITE_COPY_INTO_LOCATION_FIELD_NUMBER: builtins.int
    WRITE_CSV_FIELD_NUMBER: builtins.int
    WRITE_INSERT_INTO_FIELD_NUMBER: builtins.int
    WRITE_JSON_FIELD_NUMBER: builtins.int
    WRITE_PANDAS_FIELD_NUMBER: builtins.int
    WRITE_PARQUET_FIELD_NUMBER: builtins.int
    WRITE_SAVE_FIELD_NUMBER: builtins.int
    WRITE_TABLE_FIELD_NUMBER: builtins.int
    @property
    def trait_bin_op(self) -> global___BinOp: ...
    @property
    def trait_column_fn(self) -> global___ColumnFn: ...
    @property
    def trait_const(self) -> global___Const: ...
    @property
    def trait_expr(self) -> global___Expr: ...
    @property
    def trait_extension_expr(self) -> global___ExtensionExpr: ...
    @property
    def trait_fn_id_ref_expr(self) -> global___FnIdRefExpr: ...
    @property
    def trait_fn_name_ref_expr(self) -> global___FnNameRefExpr: ...
    @property
    def trait_read_file(self) -> global___ReadFile: ...
    @property
    def trait_unary_op(self) -> global___UnaryOp: ...
    @property
    def trait_window_spec_expr(self) -> global___WindowSpecExpr: ...
    @property
    def trait_write_file(self) -> global___WriteFile: ...
    @property
    def add(self) -> global___Add: ...
    @property
    def apply_expr(self) -> global___ApplyExpr: ...
    @property
    def big_decimal_val(self) -> global___BigDecimalVal: ...
    @property
    def big_int_val(self) -> global___BigIntVal: ...
    @property
    def binary_val(self) -> global___BinaryVal: ...
    @property
    def bit_and(self) -> global___BitAnd: ...
    @property
    def bit_or(self) -> global___BitOr: ...
    @property
    def bit_xor(self) -> global___BitXor: ...
    @property
    def bool_val(self) -> global___BoolVal: ...
    @property
    def builtin_fn(self) -> global___BuiltinFn: ...
    @property
    def call_table_function_expr(self) -> global___CallTableFunctionExpr: ...
    @property
    def column_alias(self) -> global___ColumnAlias: ...
    @property
    def column_apply__int(self) -> global___ColumnApply_Int: ...
    @property
    def column_apply__string(self) -> global___ColumnApply_String: ...
    @property
    def column_asc(self) -> global___ColumnAsc: ...
    @property
    def column_between(self) -> global___ColumnBetween: ...
    @property
    def column_case_expr(self) -> global___ColumnCaseExpr: ...
    @property
    def column_case_expr_clause(self) -> global___ColumnCaseExprClause: ...
    @property
    def column_cast(self) -> global___ColumnCast: ...
    @property
    def column_desc(self) -> global___ColumnDesc: ...
    @property
    def column_equal_nan(self) -> global___ColumnEqualNan: ...
    @property
    def column_equal_null(self) -> global___ColumnEqualNull: ...
    @property
    def column_in(self) -> global___ColumnIn: ...
    @property
    def column_is_not_null(self) -> global___ColumnIsNotNull: ...
    @property
    def column_is_null(self) -> global___ColumnIsNull: ...
    @property
    def column_over(self) -> global___ColumnOver: ...
    @property
    def column_regexp(self) -> global___ColumnRegexp: ...
    @property
    def column_string_collate(self) -> global___ColumnStringCollate: ...
    @property
    def column_string_contains(self) -> global___ColumnStringContains: ...
    @property
    def column_string_ends_with(self) -> global___ColumnStringEndsWith: ...
    @property
    def column_string_like(self) -> global___ColumnStringLike: ...
    @property
    def column_string_starts_with(self) -> global___ColumnStringStartsWith: ...
    @property
    def column_string_substr(self) -> global___ColumnStringSubstr: ...
    @property
    def column_try_cast(self) -> global___ColumnTryCast: ...
    @property
    def column_within_group(self) -> global___ColumnWithinGroup: ...
    @property
    def create_dataframe(self) -> global___CreateDataframe: ...
    @property
    def dataframe_agg(self) -> global___DataframeAgg: ...
    @property
    def dataframe_alias(self) -> global___DataframeAlias: ...
    @property
    def dataframe_analytics_compute_lag(self) -> global___DataframeAnalyticsComputeLag: ...
    @property
    def dataframe_analytics_compute_lead(self) -> global___DataframeAnalyticsComputeLead: ...
    @property
    def dataframe_analytics_cumulative_agg(self) -> global___DataframeAnalyticsCumulativeAgg: ...
    @property
    def dataframe_analytics_moving_agg(self) -> global___DataframeAnalyticsMovingAgg: ...
    @property
    def dataframe_analytics_time_series_agg(self) -> global___DataframeAnalyticsTimeSeriesAgg: ...
    @property
    def dataframe_cache_result(self) -> global___DataframeCacheResult: ...
    @property
    def dataframe_col(self) -> global___DataframeCol: ...
    @property
    def dataframe_collect(self) -> global___DataframeCollect: ...
    @property
    def dataframe_copy_into_table(self) -> global___DataframeCopyIntoTable: ...
    @property
    def dataframe_count(self) -> global___DataframeCount: ...
    @property
    def dataframe_create_or_replace_dynamic_table(self) -> global___DataframeCreateOrReplaceDynamicTable: ...
    @property
    def dataframe_create_or_replace_view(self) -> global___DataframeCreateOrReplaceView: ...
    @property
    def dataframe_cross_join(self) -> global___DataframeCrossJoin: ...
    @property
    def dataframe_cube(self) -> global___DataframeCube: ...
    @property
    def dataframe_describe(self) -> global___DataframeDescribe: ...
    @property
    def dataframe_distinct(self) -> global___DataframeDistinct: ...
    @property
    def dataframe_drop(self) -> global___DataframeDrop: ...
    @property
    def dataframe_drop_duplicates(self) -> global___DataframeDropDuplicates: ...
    @property
    def dataframe_except(self) -> global___DataframeExcept: ...
    @property
    def dataframe_filter(self) -> global___DataframeFilter: ...
    @property
    def dataframe_first(self) -> global___DataframeFirst: ...
    @property
    def dataframe_flatten(self) -> global___DataframeFlatten: ...
    @property
    def dataframe_group_by(self) -> global___DataframeGroupBy: ...
    @property
    def dataframe_group_by_grouping_sets(self) -> global___DataframeGroupByGroupingSets: ...
    @property
    def dataframe_intersect(self) -> global___DataframeIntersect: ...
    @property
    def dataframe_join(self) -> global___DataframeJoin: ...
    @property
    def dataframe_join_table_function(self) -> global___DataframeJoinTableFunction: ...
    @property
    def dataframe_limit(self) -> global___DataframeLimit: ...
    @property
    def dataframe_na_drop__python(self) -> global___DataframeNaDrop_Python: ...
    @property
    def dataframe_na_drop__scala(self) -> global___DataframeNaDrop_Scala: ...
    @property
    def dataframe_na_fill(self) -> global___DataframeNaFill: ...
    @property
    def dataframe_na_replace(self) -> global___DataframeNaReplace: ...
    @property
    def dataframe_natural_join(self) -> global___DataframeNaturalJoin: ...
    @property
    def dataframe_pivot(self) -> global___DataframePivot: ...
    @property
    def dataframe_random_split(self) -> global___DataframeRandomSplit: ...
    @property
    def dataframe_reader(self) -> global___DataframeReader: ...
    @property
    def dataframe_ref(self) -> global___DataframeRef: ...
    @property
    def dataframe_rename(self) -> global___DataframeRename: ...
    @property
    def dataframe_rollup(self) -> global___DataframeRollup: ...
    @property
    def dataframe_sample(self) -> global___DataframeSample: ...
    @property
    def dataframe_select(self) -> global___DataframeSelect: ...
    @property
    def dataframe_show(self) -> global___DataframeShow: ...
    @property
    def dataframe_sort(self) -> global___DataframeSort: ...
    @property
    def dataframe_stat_approx_quantile(self) -> global___DataframeStatApproxQuantile: ...
    @property
    def dataframe_stat_corr(self) -> global___DataframeStatCorr: ...
    @property
    def dataframe_stat_cov(self) -> global___DataframeStatCov: ...
    @property
    def dataframe_stat_cross_tab(self) -> global___DataframeStatCrossTab: ...
    @property
    def dataframe_stat_sample_by(self) -> global___DataframeStatSampleBy: ...
    @property
    def dataframe_to_df(self) -> global___DataframeToDf: ...
    @property
    def dataframe_to_local_iterator(self) -> global___DataframeToLocalIterator: ...
    @property
    def dataframe_to_pandas(self) -> global___DataframeToPandas: ...
    @property
    def dataframe_to_pandas_batches(self) -> global___DataframeToPandasBatches: ...
    @property
    def dataframe_union(self) -> global___DataframeUnion: ...
    @property
    def dataframe_unpivot(self) -> global___DataframeUnpivot: ...
    @property
    def dataframe_with_column(self) -> global___DataframeWithColumn: ...
    @property
    def dataframe_with_column_renamed(self) -> global___DataframeWithColumnRenamed: ...
    @property
    def dataframe_with_columns(self) -> global___DataframeWithColumns: ...
    @property
    def dataframe_writer(self) -> global___DataframeWriter: ...
    @property
    def datatype_val(self) -> global___DatatypeVal: ...
    @property
    def div(self) -> global___Div: ...
    @property
    def eq(self) -> global___Eq: ...
    @property
    def flatten(self) -> global___Flatten: ...
    @property
    def float64_val(self) -> global___Float64Val: ...
    @property
    def fn_ref(self) -> global___FnRef: ...
    @property
    def generator(self) -> global___Generator: ...
    @property
    def geq(self) -> global___Geq: ...
    @property
    def grouping_sets(self) -> global___GroupingSets: ...
    @property
    def gt(self) -> global___Gt: ...
    @property
    def indirect_table_fn_id_ref(self) -> global___IndirectTableFnIdRef: ...
    @property
    def indirect_table_fn_name_ref(self) -> global___IndirectTableFnNameRef: ...
    @property
    def int64_val(self) -> global___Int64Val: ...
    @property
    def leq(self) -> global___Leq: ...
    @property
    def list_val(self) -> global___ListVal: ...
    @property
    def lt(self) -> global___Lt: ...
    @property
    def merge_delete_when_matched_clause(self) -> global___MergeDeleteWhenMatchedClause: ...
    @property
    def merge_insert_when_not_matched_clause(self) -> global___MergeInsertWhenNotMatchedClause: ...
    @property
    def merge_update_when_matched_clause(self) -> global___MergeUpdateWhenMatchedClause: ...
    @property
    def mod(self) -> global___Mod: ...
    @property
    def mul(self) -> global___Mul: ...
    @property
    def name_ref(self) -> global___NameRef: ...
    @property
    def neg(self) -> global___Neg: ...
    @property
    def neq(self) -> global___Neq: ...
    @property
    def null_val(self) -> global___NullVal: ...
    @property
    def object_get_item(self) -> global___ObjectGetItem: ...
    @property
    def pow(self) -> global___Pow: ...
    @property
    def python_date_val(self) -> global___PythonDateVal: ...
    @property
    def python_time_val(self) -> global___PythonTimeVal: ...
    @property
    def python_timestamp_val(self) -> global___PythonTimestampVal: ...
    @property
    def range(self) -> global___Range: ...
    @property
    def read_avro(self) -> global___ReadAvro: ...
    @property
    def read_csv(self) -> global___ReadCsv: ...
    @property
    def read_json(self) -> global___ReadJson: ...
    @property
    def read_load(self) -> global___ReadLoad: ...
    @property
    def read_orc(self) -> global___ReadOrc: ...
    @property
    def read_parquet(self) -> global___ReadParquet: ...
    @property
    def read_table(self) -> global___ReadTable: ...
    @property
    def read_xml(self) -> global___ReadXml: ...
    @property
    def redacted_const(self) -> global___RedactedConst: ...
    @property
    def relational_grouped_dataframe_agg(self) -> global___RelationalGroupedDataframeAgg: ...
    @property
    def relational_grouped_dataframe_apply_in_pandas(self) -> global___RelationalGroupedDataframeApplyInPandas: ...
    @property
    def relational_grouped_dataframe_builtin(self) -> global___RelationalGroupedDataframeBuiltin: ...
    @property
    def relational_grouped_dataframe_pivot(self) -> global___RelationalGroupedDataframePivot: ...
    @property
    def relational_grouped_dataframe_ref(self) -> global___RelationalGroupedDataframeRef: ...
    @property
    def row(self) -> global___Row: ...
    @property
    def seq_map_val(self) -> global___SeqMapVal: ...
    @property
    def session_table_function(self) -> global___SessionTableFunction: ...
    @property
    def sql(self) -> global___Sql: ...
    @property
    def sql_expr(self) -> global___SqlExpr: ...
    @property
    def stored_procedure(self) -> global___StoredProcedure: ...
    @property
    def string_val(self) -> global___StringVal: ...
    @property
    def sub(self) -> global___Sub: ...
    @property
    def table(self) -> global___Table: ...
    @property
    def table_delete(self) -> global___TableDelete: ...
    @property
    def table_drop_table(self) -> global___TableDropTable: ...
    @property
    def table_fn_call_alias(self) -> global___TableFnCallAlias: ...
    @property
    def table_fn_call_over(self) -> global___TableFnCallOver: ...
    @property
    def table_merge(self) -> global___TableMerge: ...
    @property
    def table_sample(self) -> global___TableSample: ...
    @property
    def table_update(self) -> global___TableUpdate: ...
    @property
    def to_snowpark_pandas(self) -> global___ToSnowparkPandas: ...
    @property
    def truncated_expr(self) -> global___TruncatedExpr: ...
    @property
    def tuple_val(self) -> global___TupleVal: ...
    @property
    def udaf(self) -> global___Udaf: ...
    @property
    def udf(self) -> global___Udf: ...
    @property
    def udtf(self) -> global___Udtf: ...
    @property
    def window_spec_empty(self) -> global___WindowSpecEmpty: ...
    @property
    def window_spec_order_by(self) -> global___WindowSpecOrderBy: ...
    @property
    def window_spec_partition_by(self) -> global___WindowSpecPartitionBy: ...
    @property
    def window_spec_range_between(self) -> global___WindowSpecRangeBetween: ...
    @property
    def window_spec_rows_between(self) -> global___WindowSpecRowsBetween: ...
    @property
    def write_copy_into_location(self) -> global___WriteCopyIntoLocation: ...
    @property
    def write_csv(self) -> global___WriteCsv: ...
    @property
    def write_insert_into(self) -> global___WriteInsertInto: ...
    @property
    def write_json(self) -> global___WriteJson: ...
    @property
    def write_pandas(self) -> global___WritePandas: ...
    @property
    def write_parquet(self) -> global___WriteParquet: ...
    @property
    def write_save(self) -> global___WriteSave: ...
    @property
    def write_table(self) -> global___WriteTable: ...
    def __init__(
        self,
        *,
        trait_bin_op: global___BinOp | None = ...,
        trait_column_fn: global___ColumnFn | None = ...,
        trait_const: global___Const | None = ...,
        trait_expr: global___Expr | None = ...,
        trait_extension_expr: global___ExtensionExpr | None = ...,
        trait_fn_id_ref_expr: global___FnIdRefExpr | None = ...,
        trait_fn_name_ref_expr: global___FnNameRefExpr | None = ...,
        trait_read_file: global___ReadFile | None = ...,
        trait_unary_op: global___UnaryOp | None = ...,
        trait_window_spec_expr: global___WindowSpecExpr | None = ...,
        trait_write_file: global___WriteFile | None = ...,
        add: global___Add | None = ...,
        apply_expr: global___ApplyExpr | None = ...,
        big_decimal_val: global___BigDecimalVal | None = ...,
        big_int_val: global___BigIntVal | None = ...,
        binary_val: global___BinaryVal | None = ...,
        bit_and: global___BitAnd | None = ...,
        bit_or: global___BitOr | None = ...,
        bit_xor: global___BitXor | None = ...,
        bool_val: global___BoolVal | None = ...,
        builtin_fn: global___BuiltinFn | None = ...,
        call_table_function_expr: global___CallTableFunctionExpr | None = ...,
        column_alias: global___ColumnAlias | None = ...,
        column_apply__int: global___ColumnApply_Int | None = ...,
        column_apply__string: global___ColumnApply_String | None = ...,
        column_asc: global___ColumnAsc | None = ...,
        column_between: global___ColumnBetween | None = ...,
        column_case_expr: global___ColumnCaseExpr | None = ...,
        column_case_expr_clause: global___ColumnCaseExprClause | None = ...,
        column_cast: global___ColumnCast | None = ...,
        column_desc: global___ColumnDesc | None = ...,
        column_equal_nan: global___ColumnEqualNan | None = ...,
        column_equal_null: global___ColumnEqualNull | None = ...,
        column_in: global___ColumnIn | None = ...,
        column_is_not_null: global___ColumnIsNotNull | None = ...,
        column_is_null: global___ColumnIsNull | None = ...,
        column_over: global___ColumnOver | None = ...,
        column_regexp: global___ColumnRegexp | None = ...,
        column_string_collate: global___ColumnStringCollate | None = ...,
        column_string_contains: global___ColumnStringContains | None = ...,
        column_string_ends_with: global___ColumnStringEndsWith | None = ...,
        column_string_like: global___ColumnStringLike | None = ...,
        column_string_starts_with: global___ColumnStringStartsWith | None = ...,
        column_string_substr: global___ColumnStringSubstr | None = ...,
        column_try_cast: global___ColumnTryCast | None = ...,
        column_within_group: global___ColumnWithinGroup | None = ...,
        create_dataframe: global___CreateDataframe | None = ...,
        dataframe_agg: global___DataframeAgg | None = ...,
        dataframe_alias: global___DataframeAlias | None = ...,
        dataframe_analytics_compute_lag: global___DataframeAnalyticsComputeLag | None = ...,
        dataframe_analytics_compute_lead: global___DataframeAnalyticsComputeLead | None = ...,
        dataframe_analytics_cumulative_agg: global___DataframeAnalyticsCumulativeAgg | None = ...,
        dataframe_analytics_moving_agg: global___DataframeAnalyticsMovingAgg | None = ...,
        dataframe_analytics_time_series_agg: global___DataframeAnalyticsTimeSeriesAgg | None = ...,
        dataframe_cache_result: global___DataframeCacheResult | None = ...,
        dataframe_col: global___DataframeCol | None = ...,
        dataframe_collect: global___DataframeCollect | None = ...,
        dataframe_copy_into_table: global___DataframeCopyIntoTable | None = ...,
        dataframe_count: global___DataframeCount | None = ...,
        dataframe_create_or_replace_dynamic_table: global___DataframeCreateOrReplaceDynamicTable | None = ...,
        dataframe_create_or_replace_view: global___DataframeCreateOrReplaceView | None = ...,
        dataframe_cross_join: global___DataframeCrossJoin | None = ...,
        dataframe_cube: global___DataframeCube | None = ...,
        dataframe_describe: global___DataframeDescribe | None = ...,
        dataframe_distinct: global___DataframeDistinct | None = ...,
        dataframe_drop: global___DataframeDrop | None = ...,
        dataframe_drop_duplicates: global___DataframeDropDuplicates | None = ...,
        dataframe_except: global___DataframeExcept | None = ...,
        dataframe_filter: global___DataframeFilter | None = ...,
        dataframe_first: global___DataframeFirst | None = ...,
        dataframe_flatten: global___DataframeFlatten | None = ...,
        dataframe_group_by: global___DataframeGroupBy | None = ...,
        dataframe_group_by_grouping_sets: global___DataframeGroupByGroupingSets | None = ...,
        dataframe_intersect: global___DataframeIntersect | None = ...,
        dataframe_join: global___DataframeJoin | None = ...,
        dataframe_join_table_function: global___DataframeJoinTableFunction | None = ...,
        dataframe_limit: global___DataframeLimit | None = ...,
        dataframe_na_drop__python: global___DataframeNaDrop_Python | None = ...,
        dataframe_na_drop__scala: global___DataframeNaDrop_Scala | None = ...,
        dataframe_na_fill: global___DataframeNaFill | None = ...,
        dataframe_na_replace: global___DataframeNaReplace | None = ...,
        dataframe_natural_join: global___DataframeNaturalJoin | None = ...,
        dataframe_pivot: global___DataframePivot | None = ...,
        dataframe_random_split: global___DataframeRandomSplit | None = ...,
        dataframe_reader: global___DataframeReader | None = ...,
        dataframe_ref: global___DataframeRef | None = ...,
        dataframe_rename: global___DataframeRename | None = ...,
        dataframe_rollup: global___DataframeRollup | None = ...,
        dataframe_sample: global___DataframeSample | None = ...,
        dataframe_select: global___DataframeSelect | None = ...,
        dataframe_show: global___DataframeShow | None = ...,
        dataframe_sort: global___DataframeSort | None = ...,
        dataframe_stat_approx_quantile: global___DataframeStatApproxQuantile | None = ...,
        dataframe_stat_corr: global___DataframeStatCorr | None = ...,
        dataframe_stat_cov: global___DataframeStatCov | None = ...,
        dataframe_stat_cross_tab: global___DataframeStatCrossTab | None = ...,
        dataframe_stat_sample_by: global___DataframeStatSampleBy | None = ...,
        dataframe_to_df: global___DataframeToDf | None = ...,
        dataframe_to_local_iterator: global___DataframeToLocalIterator | None = ...,
        dataframe_to_pandas: global___DataframeToPandas | None = ...,
        dataframe_to_pandas_batches: global___DataframeToPandasBatches | None = ...,
        dataframe_union: global___DataframeUnion | None = ...,
        dataframe_unpivot: global___DataframeUnpivot | None = ...,
        dataframe_with_column: global___DataframeWithColumn | None = ...,
        dataframe_with_column_renamed: global___DataframeWithColumnRenamed | None = ...,
        dataframe_with_columns: global___DataframeWithColumns | None = ...,
        dataframe_writer: global___DataframeWriter | None = ...,
        datatype_val: global___DatatypeVal | None = ...,
        div: global___Div | None = ...,
        eq: global___Eq | None = ...,
        flatten: global___Flatten | None = ...,
        float64_val: global___Float64Val | None = ...,
        fn_ref: global___FnRef | None = ...,
        generator: global___Generator | None = ...,
        geq: global___Geq | None = ...,
        grouping_sets: global___GroupingSets | None = ...,
        gt: global___Gt | None = ...,
        indirect_table_fn_id_ref: global___IndirectTableFnIdRef | None = ...,
        indirect_table_fn_name_ref: global___IndirectTableFnNameRef | None = ...,
        int64_val: global___Int64Val | None = ...,
        leq: global___Leq | None = ...,
        list_val: global___ListVal | None = ...,
        lt: global___Lt | None = ...,
        merge_delete_when_matched_clause: global___MergeDeleteWhenMatchedClause | None = ...,
        merge_insert_when_not_matched_clause: global___MergeInsertWhenNotMatchedClause | None = ...,
        merge_update_when_matched_clause: global___MergeUpdateWhenMatchedClause | None = ...,
        mod: global___Mod | None = ...,
        mul: global___Mul | None = ...,
        name_ref: global___NameRef | None = ...,
        neg: global___Neg | None = ...,
        neq: global___Neq | None = ...,
        null_val: global___NullVal | None = ...,
        object_get_item: global___ObjectGetItem | None = ...,
        pow: global___Pow | None = ...,
        python_date_val: global___PythonDateVal | None = ...,
        python_time_val: global___PythonTimeVal | None = ...,
        python_timestamp_val: global___PythonTimestampVal | None = ...,
        range: global___Range | None = ...,
        read_avro: global___ReadAvro | None = ...,
        read_csv: global___ReadCsv | None = ...,
        read_json: global___ReadJson | None = ...,
        read_load: global___ReadLoad | None = ...,
        read_orc: global___ReadOrc | None = ...,
        read_parquet: global___ReadParquet | None = ...,
        read_table: global___ReadTable | None = ...,
        read_xml: global___ReadXml | None = ...,
        redacted_const: global___RedactedConst | None = ...,
        relational_grouped_dataframe_agg: global___RelationalGroupedDataframeAgg | None = ...,
        relational_grouped_dataframe_apply_in_pandas: global___RelationalGroupedDataframeApplyInPandas | None = ...,
        relational_grouped_dataframe_builtin: global___RelationalGroupedDataframeBuiltin | None = ...,
        relational_grouped_dataframe_pivot: global___RelationalGroupedDataframePivot | None = ...,
        relational_grouped_dataframe_ref: global___RelationalGroupedDataframeRef | None = ...,
        row: global___Row | None = ...,
        seq_map_val: global___SeqMapVal | None = ...,
        session_table_function: global___SessionTableFunction | None = ...,
        sql: global___Sql | None = ...,
        sql_expr: global___SqlExpr | None = ...,
        stored_procedure: global___StoredProcedure | None = ...,
        string_val: global___StringVal | None = ...,
        sub: global___Sub | None = ...,
        table: global___Table | None = ...,
        table_delete: global___TableDelete | None = ...,
        table_drop_table: global___TableDropTable | None = ...,
        table_fn_call_alias: global___TableFnCallAlias | None = ...,
        table_fn_call_over: global___TableFnCallOver | None = ...,
        table_merge: global___TableMerge | None = ...,
        table_sample: global___TableSample | None = ...,
        table_update: global___TableUpdate | None = ...,
        to_snowpark_pandas: global___ToSnowparkPandas | None = ...,
        truncated_expr: global___TruncatedExpr | None = ...,
        tuple_val: global___TupleVal | None = ...,
        udaf: global___Udaf | None = ...,
        udf: global___Udf | None = ...,
        udtf: global___Udtf | None = ...,
        window_spec_empty: global___WindowSpecEmpty | None = ...,
        window_spec_order_by: global___WindowSpecOrderBy | None = ...,
        window_spec_partition_by: global___WindowSpecPartitionBy | None = ...,
        window_spec_range_between: global___WindowSpecRangeBetween | None = ...,
        window_spec_rows_between: global___WindowSpecRowsBetween | None = ...,
        write_copy_into_location: global___WriteCopyIntoLocation | None = ...,
        write_csv: global___WriteCsv | None = ...,
        write_insert_into: global___WriteInsertInto | None = ...,
        write_json: global___WriteJson | None = ...,
        write_pandas: global___WritePandas | None = ...,
        write_parquet: global___WriteParquet | None = ...,
        write_save: global___WriteSave | None = ...,
        write_table: global___WriteTable | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["add", b"add", "and", b"and", "apply_expr", b"apply_expr", "big_decimal_val", b"big_decimal_val", "big_int_val", b"big_int_val", "binary_val", b"binary_val", "bit_and", b"bit_and", "bit_or", b"bit_or", "bit_xor", b"bit_xor", "bool_val", b"bool_val", "builtin_fn", b"builtin_fn", "call_table_function_expr", b"call_table_function_expr", "column_alias", b"column_alias", "column_apply__int", b"column_apply__int", "column_apply__string", b"column_apply__string", "column_asc", b"column_asc", "column_between", b"column_between", "column_case_expr", b"column_case_expr", "column_case_expr_clause", b"column_case_expr_clause", "column_cast", b"column_cast", "column_desc", b"column_desc", "column_equal_nan", b"column_equal_nan", "column_equal_null", b"column_equal_null", "column_in", b"column_in", "column_is_not_null", b"column_is_not_null", "column_is_null", b"column_is_null", "column_over", b"column_over", "column_regexp", b"column_regexp", "column_string_collate", b"column_string_collate", "column_string_contains", b"column_string_contains", "column_string_ends_with", b"column_string_ends_with", "column_string_like", b"column_string_like", "column_string_starts_with", b"column_string_starts_with", "column_string_substr", b"column_string_substr", "column_try_cast", b"column_try_cast", "column_within_group", b"column_within_group", "create_dataframe", b"create_dataframe", "dataframe_agg", b"dataframe_agg", "dataframe_alias", b"dataframe_alias", "dataframe_analytics_compute_lag", b"dataframe_analytics_compute_lag", "dataframe_analytics_compute_lead", b"dataframe_analytics_compute_lead", "dataframe_analytics_cumulative_agg", b"dataframe_analytics_cumulative_agg", "dataframe_analytics_moving_agg", b"dataframe_analytics_moving_agg", "dataframe_analytics_time_series_agg", b"dataframe_analytics_time_series_agg", "dataframe_cache_result", b"dataframe_cache_result", "dataframe_col", b"dataframe_col", "dataframe_collect", b"dataframe_collect", "dataframe_copy_into_table", b"dataframe_copy_into_table", "dataframe_count", b"dataframe_count", "dataframe_create_or_replace_dynamic_table", b"dataframe_create_or_replace_dynamic_table", "dataframe_create_or_replace_view", b"dataframe_create_or_replace_view", "dataframe_cross_join", b"dataframe_cross_join", "dataframe_cube", b"dataframe_cube", "dataframe_describe", b"dataframe_describe", "dataframe_distinct", b"dataframe_distinct", "dataframe_drop", b"dataframe_drop", "dataframe_drop_duplicates", b"dataframe_drop_duplicates", "dataframe_except", b"dataframe_except", "dataframe_filter", b"dataframe_filter", "dataframe_first", b"dataframe_first", "dataframe_flatten", b"dataframe_flatten", "dataframe_group_by", b"dataframe_group_by", "dataframe_group_by_grouping_sets", b"dataframe_group_by_grouping_sets", "dataframe_intersect", b"dataframe_intersect", "dataframe_join", b"dataframe_join", "dataframe_join_table_function", b"dataframe_join_table_function", "dataframe_limit", b"dataframe_limit", "dataframe_na_drop__python", b"dataframe_na_drop__python", "dataframe_na_drop__scala", b"dataframe_na_drop__scala", "dataframe_na_fill", b"dataframe_na_fill", "dataframe_na_replace", b"dataframe_na_replace", "dataframe_natural_join", b"dataframe_natural_join", "dataframe_pivot", b"dataframe_pivot", "dataframe_random_split", b"dataframe_random_split", "dataframe_reader", b"dataframe_reader", "dataframe_ref", b"dataframe_ref", "dataframe_rename", b"dataframe_rename", "dataframe_rollup", b"dataframe_rollup", "dataframe_sample", b"dataframe_sample", "dataframe_select", b"dataframe_select", "dataframe_show", b"dataframe_show", "dataframe_sort", b"dataframe_sort", "dataframe_stat_approx_quantile", b"dataframe_stat_approx_quantile", "dataframe_stat_corr", b"dataframe_stat_corr", "dataframe_stat_cov", b"dataframe_stat_cov", "dataframe_stat_cross_tab", b"dataframe_stat_cross_tab", "dataframe_stat_sample_by", b"dataframe_stat_sample_by", "dataframe_to_df", b"dataframe_to_df", "dataframe_to_local_iterator", b"dataframe_to_local_iterator", "dataframe_to_pandas", b"dataframe_to_pandas", "dataframe_to_pandas_batches", b"dataframe_to_pandas_batches", "dataframe_union", b"dataframe_union", "dataframe_unpivot", b"dataframe_unpivot", "dataframe_with_column", b"dataframe_with_column", "dataframe_with_column_renamed", b"dataframe_with_column_renamed", "dataframe_with_columns", b"dataframe_with_columns", "dataframe_writer", b"dataframe_writer", "datatype_val", b"datatype_val", "div", b"div", "eq", b"eq", "flatten", b"flatten", "float64_val", b"float64_val", "fn_ref", b"fn_ref", "generator", b"generator", "geq", b"geq", "grouping_sets", b"grouping_sets", "gt", b"gt", "indirect_table_fn_id_ref", b"indirect_table_fn_id_ref", "indirect_table_fn_name_ref", b"indirect_table_fn_name_ref", "int64_val", b"int64_val", "leq", b"leq", "list_val", b"list_val", "lt", b"lt", "merge_delete_when_matched_clause", b"merge_delete_when_matched_clause", "merge_insert_when_not_matched_clause", b"merge_insert_when_not_matched_clause", "merge_update_when_matched_clause", b"merge_update_when_matched_clause", "mod", b"mod", "mul", b"mul", "name_ref", b"name_ref", "neg", b"neg", "neq", b"neq", "not", b"not", "null_val", b"null_val", "object_get_item", b"object_get_item", "or", b"or", "pow", b"pow", "python_date_val", b"python_date_val", "python_time_val", b"python_time_val", "python_timestamp_val", b"python_timestamp_val", "range", b"range", "read_avro", b"read_avro", "read_csv", b"read_csv", "read_json", b"read_json", "read_load", b"read_load", "read_orc", b"read_orc", "read_parquet", b"read_parquet", "read_table", b"read_table", "read_xml", b"read_xml", "redacted_const", b"redacted_const", "relational_grouped_dataframe_agg", b"relational_grouped_dataframe_agg", "relational_grouped_dataframe_apply_in_pandas", b"relational_grouped_dataframe_apply_in_pandas", "relational_grouped_dataframe_builtin", b"relational_grouped_dataframe_builtin", "relational_grouped_dataframe_pivot", b"relational_grouped_dataframe_pivot", "relational_grouped_dataframe_ref", b"relational_grouped_dataframe_ref", "row", b"row", "seq_map_val", b"seq_map_val", "session_table_function", b"session_table_function", "sql", b"sql", "sql_expr", b"sql_expr", "stored_procedure", b"stored_procedure", "string_val", b"string_val", "sub", b"sub", "table", b"table", "table_delete", b"table_delete", "table_drop_table", b"table_drop_table", "table_fn_call_alias", b"table_fn_call_alias", "table_fn_call_over", b"table_fn_call_over", "table_merge", b"table_merge", "table_sample", b"table_sample", "table_update", b"table_update", "to_snowpark_pandas", b"to_snowpark_pandas", "trait_bin_op", b"trait_bin_op", "trait_column_fn", b"trait_column_fn", "trait_const", b"trait_const", "trait_expr", b"trait_expr", "trait_extension_expr", b"trait_extension_expr", "trait_fn_id_ref_expr", b"trait_fn_id_ref_expr", "trait_fn_name_ref_expr", b"trait_fn_name_ref_expr", "trait_read_file", b"trait_read_file", "trait_unary_op", b"trait_unary_op", "trait_window_spec_expr", b"trait_window_spec_expr", "trait_write_file", b"trait_write_file", "truncated_expr", b"truncated_expr", "tuple_val", b"tuple_val", "udaf", b"udaf", "udf", b"udf", "udtf", b"udtf", "variant", b"variant", "window_spec_empty", b"window_spec_empty", "window_spec_order_by", b"window_spec_order_by", "window_spec_partition_by", b"window_spec_partition_by", "window_spec_range_between", b"window_spec_range_between", "window_spec_rows_between", b"window_spec_rows_between", "write_copy_into_location", b"write_copy_into_location", "write_csv", b"write_csv", "write_insert_into", b"write_insert_into", "write_json", b"write_json", "write_pandas", b"write_pandas", "write_parquet", b"write_parquet", "write_save", b"write_save", "write_table", b"write_table"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["add", b"add", "and", b"and", "apply_expr", b"apply_expr", "big_decimal_val", b"big_decimal_val", "big_int_val", b"big_int_val", "binary_val", b"binary_val", "bit_and", b"bit_and", "bit_or", b"bit_or", "bit_xor", b"bit_xor", "bool_val", b"bool_val", "builtin_fn", b"builtin_fn", "call_table_function_expr", b"call_table_function_expr", "column_alias", b"column_alias", "column_apply__int", b"column_apply__int", "column_apply__string", b"column_apply__string", "column_asc", b"column_asc", "column_between", b"column_between", "column_case_expr", b"column_case_expr", "column_case_expr_clause", b"column_case_expr_clause", "column_cast", b"column_cast", "column_desc", b"column_desc", "column_equal_nan", b"column_equal_nan", "column_equal_null", b"column_equal_null", "column_in", b"column_in", "column_is_not_null", b"column_is_not_null", "column_is_null", b"column_is_null", "column_over", b"column_over", "column_regexp", b"column_regexp", "column_string_collate", b"column_string_collate", "column_string_contains", b"column_string_contains", "column_string_ends_with", b"column_string_ends_with", "column_string_like", b"column_string_like", "column_string_starts_with", b"column_string_starts_with", "column_string_substr", b"column_string_substr", "column_try_cast", b"column_try_cast", "column_within_group", b"column_within_group", "create_dataframe", b"create_dataframe", "dataframe_agg", b"dataframe_agg", "dataframe_alias", b"dataframe_alias", "dataframe_analytics_compute_lag", b"dataframe_analytics_compute_lag", "dataframe_analytics_compute_lead", b"dataframe_analytics_compute_lead", "dataframe_analytics_cumulative_agg", b"dataframe_analytics_cumulative_agg", "dataframe_analytics_moving_agg", b"dataframe_analytics_moving_agg", "dataframe_analytics_time_series_agg", b"dataframe_analytics_time_series_agg", "dataframe_cache_result", b"dataframe_cache_result", "dataframe_col", b"dataframe_col", "dataframe_collect", b"dataframe_collect", "dataframe_copy_into_table", b"dataframe_copy_into_table", "dataframe_count", b"dataframe_count", "dataframe_create_or_replace_dynamic_table", b"dataframe_create_or_replace_dynamic_table", "dataframe_create_or_replace_view", b"dataframe_create_or_replace_view", "dataframe_cross_join", b"dataframe_cross_join", "dataframe_cube", b"dataframe_cube", "dataframe_describe", b"dataframe_describe", "dataframe_distinct", b"dataframe_distinct", "dataframe_drop", b"dataframe_drop", "dataframe_drop_duplicates", b"dataframe_drop_duplicates", "dataframe_except", b"dataframe_except", "dataframe_filter", b"dataframe_filter", "dataframe_first", b"dataframe_first", "dataframe_flatten", b"dataframe_flatten", "dataframe_group_by", b"dataframe_group_by", "dataframe_group_by_grouping_sets", b"dataframe_group_by_grouping_sets", "dataframe_intersect", b"dataframe_intersect", "dataframe_join", b"dataframe_join", "dataframe_join_table_function", b"dataframe_join_table_function", "dataframe_limit", b"dataframe_limit", "dataframe_na_drop__python", b"dataframe_na_drop__python", "dataframe_na_drop__scala", b"dataframe_na_drop__scala", "dataframe_na_fill", b"dataframe_na_fill", "dataframe_na_replace", b"dataframe_na_replace", "dataframe_natural_join", b"dataframe_natural_join", "dataframe_pivot", b"dataframe_pivot", "dataframe_random_split", b"dataframe_random_split", "dataframe_reader", b"dataframe_reader", "dataframe_ref", b"dataframe_ref", "dataframe_rename", b"dataframe_rename", "dataframe_rollup", b"dataframe_rollup", "dataframe_sample", b"dataframe_sample", "dataframe_select", b"dataframe_select", "dataframe_show", b"dataframe_show", "dataframe_sort", b"dataframe_sort", "dataframe_stat_approx_quantile", b"dataframe_stat_approx_quantile", "dataframe_stat_corr", b"dataframe_stat_corr", "dataframe_stat_cov", b"dataframe_stat_cov", "dataframe_stat_cross_tab", b"dataframe_stat_cross_tab", "dataframe_stat_sample_by", b"dataframe_stat_sample_by", "dataframe_to_df", b"dataframe_to_df", "dataframe_to_local_iterator", b"dataframe_to_local_iterator", "dataframe_to_pandas", b"dataframe_to_pandas", "dataframe_to_pandas_batches", b"dataframe_to_pandas_batches", "dataframe_union", b"dataframe_union", "dataframe_unpivot", b"dataframe_unpivot", "dataframe_with_column", b"dataframe_with_column", "dataframe_with_column_renamed", b"dataframe_with_column_renamed", "dataframe_with_columns", b"dataframe_with_columns", "dataframe_writer", b"dataframe_writer", "datatype_val", b"datatype_val", "div", b"div", "eq", b"eq", "flatten", b"flatten", "float64_val", b"float64_val", "fn_ref", b"fn_ref", "generator", b"generator", "geq", b"geq", "grouping_sets", b"grouping_sets", "gt", b"gt", "indirect_table_fn_id_ref", b"indirect_table_fn_id_ref", "indirect_table_fn_name_ref", b"indirect_table_fn_name_ref", "int64_val", b"int64_val", "leq", b"leq", "list_val", b"list_val", "lt", b"lt", "merge_delete_when_matched_clause", b"merge_delete_when_matched_clause", "merge_insert_when_not_matched_clause", b"merge_insert_when_not_matched_clause", "merge_update_when_matched_clause", b"merge_update_when_matched_clause", "mod", b"mod", "mul", b"mul", "name_ref", b"name_ref", "neg", b"neg", "neq", b"neq", "not", b"not", "null_val", b"null_val", "object_get_item", b"object_get_item", "or", b"or", "pow", b"pow", "python_date_val", b"python_date_val", "python_time_val", b"python_time_val", "python_timestamp_val", b"python_timestamp_val", "range", b"range", "read_avro", b"read_avro", "read_csv", b"read_csv", "read_json", b"read_json", "read_load", b"read_load", "read_orc", b"read_orc", "read_parquet", b"read_parquet", "read_table", b"read_table", "read_xml", b"read_xml", "redacted_const", b"redacted_const", "relational_grouped_dataframe_agg", b"relational_grouped_dataframe_agg", "relational_grouped_dataframe_apply_in_pandas", b"relational_grouped_dataframe_apply_in_pandas", "relational_grouped_dataframe_builtin", b"relational_grouped_dataframe_builtin", "relational_grouped_dataframe_pivot", b"relational_grouped_dataframe_pivot", "relational_grouped_dataframe_ref", b"relational_grouped_dataframe_ref", "row", b"row", "seq_map_val", b"seq_map_val", "session_table_function", b"session_table_function", "sql", b"sql", "sql_expr", b"sql_expr", "stored_procedure", b"stored_procedure", "string_val", b"string_val", "sub", b"sub", "table", b"table", "table_delete", b"table_delete", "table_drop_table", b"table_drop_table", "table_fn_call_alias", b"table_fn_call_alias", "table_fn_call_over", b"table_fn_call_over", "table_merge", b"table_merge", "table_sample", b"table_sample", "table_update", b"table_update", "to_snowpark_pandas", b"to_snowpark_pandas", "trait_bin_op", b"trait_bin_op", "trait_column_fn", b"trait_column_fn", "trait_const", b"trait_const", "trait_expr", b"trait_expr", "trait_extension_expr", b"trait_extension_expr", "trait_fn_id_ref_expr", b"trait_fn_id_ref_expr", "trait_fn_name_ref_expr", b"trait_fn_name_ref_expr", "trait_read_file", b"trait_read_file", "trait_unary_op", b"trait_unary_op", "trait_window_spec_expr", b"trait_window_spec_expr", "trait_write_file", b"trait_write_file", "truncated_expr", b"truncated_expr", "tuple_val", b"tuple_val", "udaf", b"udaf", "udf", b"udf", "udtf", b"udtf", "variant", b"variant", "window_spec_empty", b"window_spec_empty", "window_spec_order_by", b"window_spec_order_by", "window_spec_partition_by", b"window_spec_partition_by", "window_spec_range_between", b"window_spec_range_between", "window_spec_rows_between", b"window_spec_rows_between", "write_copy_into_location", b"write_copy_into_location", "write_csv", b"write_csv", "write_insert_into", b"write_insert_into", "write_json", b"write_json", "write_pandas", b"write_pandas", "write_parquet", b"write_parquet", "write_save", b"write_save", "write_table", b"write_table"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["variant", b"variant"]) -> typing.Literal["trait_bin_op", "trait_column_fn", "trait_const", "trait_expr", "trait_extension_expr", "trait_fn_id_ref_expr", "trait_fn_name_ref_expr", "trait_read_file", "trait_unary_op", "trait_window_spec_expr", "trait_write_file", "add", "and", "apply_expr", "big_decimal_val", "big_int_val", "binary_val", "bit_and", "bit_or", "bit_xor", "bool_val", "builtin_fn", "call_table_function_expr", "column_alias", "column_apply__int", "column_apply__string", "column_asc", "column_between", "column_case_expr", "column_case_expr_clause", "column_cast", "column_desc", "column_equal_nan", "column_equal_null", "column_in", "column_is_not_null", "column_is_null", "column_over", "column_regexp", "column_string_collate", "column_string_contains", "column_string_ends_with", "column_string_like", "column_string_starts_with", "column_string_substr", "column_try_cast", "column_within_group", "create_dataframe", "dataframe_agg", "dataframe_alias", "dataframe_analytics_compute_lag", "dataframe_analytics_compute_lead", "dataframe_analytics_cumulative_agg", "dataframe_analytics_moving_agg", "dataframe_analytics_time_series_agg", "dataframe_cache_result", "dataframe_col", "dataframe_collect", "dataframe_copy_into_table", "dataframe_count", "dataframe_create_or_replace_dynamic_table", "dataframe_create_or_replace_view", "dataframe_cross_join", "dataframe_cube", "dataframe_describe", "dataframe_distinct", "dataframe_drop", "dataframe_drop_duplicates", "dataframe_except", "dataframe_filter", "dataframe_first", "dataframe_flatten", "dataframe_group_by", "dataframe_group_by_grouping_sets", "dataframe_intersect", "dataframe_join", "dataframe_join_table_function", "dataframe_limit", "dataframe_na_drop__python", "dataframe_na_drop__scala", "dataframe_na_fill", "dataframe_na_replace", "dataframe_natural_join", "dataframe_pivot", "dataframe_random_split", "dataframe_reader", "dataframe_ref", "dataframe_rename", "dataframe_rollup", "dataframe_sample", "dataframe_select", "dataframe_show", "dataframe_sort", "dataframe_stat_approx_quantile", "dataframe_stat_corr", "dataframe_stat_cov", "dataframe_stat_cross_tab", "dataframe_stat_sample_by", "dataframe_to_df", "dataframe_to_local_iterator", "dataframe_to_pandas", "dataframe_to_pandas_batches", "dataframe_union", "dataframe_unpivot", "dataframe_with_column", "dataframe_with_column_renamed", "dataframe_with_columns", "dataframe_writer", "datatype_val", "div", "eq", "flatten", "float64_val", "fn_ref", "generator", "geq", "grouping_sets", "gt", "indirect_table_fn_id_ref", "indirect_table_fn_name_ref", "int64_val", "leq", "list_val", "lt", "merge_delete_when_matched_clause", "merge_insert_when_not_matched_clause", "merge_update_when_matched_clause", "mod", "mul", "name_ref", "neg", "neq", "not", "null_val", "object_get_item", "or", "pow", "python_date_val", "python_time_val", "python_timestamp_val", "range", "read_avro", "read_csv", "read_json", "read_load", "read_orc", "read_parquet", "read_table", "read_xml", "redacted_const", "relational_grouped_dataframe_agg", "relational_grouped_dataframe_apply_in_pandas", "relational_grouped_dataframe_builtin", "relational_grouped_dataframe_pivot", "relational_grouped_dataframe_ref", "row", "seq_map_val", "session_table_function", "sql", "sql_expr", "stored_procedure", "string_val", "sub", "table", "table_delete", "table_drop_table", "table_fn_call_alias", "table_fn_call_over", "table_merge", "table_sample", "table_update", "to_snowpark_pandas", "truncated_expr", "tuple_val", "udaf", "udf", "udtf", "window_spec_empty", "window_spec_order_by", "window_spec_partition_by", "window_spec_range_between", "window_spec_rows_between", "write_copy_into_location", "write_csv", "write_insert_into", "write_json", "write_pandas", "write_parquet", "write_save", "write_table"] | None: ...

global___HasSrcPosition = HasSrcPosition

@typing.final
class IndirectTableFnIdRef(google.protobuf.message.Message):
    """fn.ir:145"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    id: builtins.int
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        id: builtins.int = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["id", b"id", "src", b"src"]) -> None: ...

global___IndirectTableFnIdRef = IndirectTableFnIdRef

@typing.final
class IndirectTableFnNameRef(google.protobuf.message.Message):
    """fn.ir:141"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def name(self) -> global___NameRef: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        name: global___NameRef | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["name", b"name", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["name", b"name", "src", b"src"]) -> None: ...

global___IndirectTableFnNameRef = IndirectTableFnNameRef

@typing.final
class Int64Val(google.protobuf.message.Message):
    """const.ir:20"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SRC_FIELD_NUMBER: builtins.int
    V_FIELD_NUMBER: builtins.int
    v: builtins.int
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        src: global___SrcPosition | None = ...,
        v: builtins.int = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["src", b"src", "v", b"v"]) -> None: ...

global___Int64Val = Int64Val

@typing.final
class Leq(google.protobuf.message.Message):
    """op.ir:30"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LHS_FIELD_NUMBER: builtins.int
    RHS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def lhs(self) -> global___Expr: ...
    @property
    def rhs(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        lhs: global___Expr | None = ...,
        rhs: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> None: ...

global___Leq = Leq

@typing.final
class ListVal(google.protobuf.message.Message):
    """expr.ir:9"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SRC_FIELD_NUMBER: builtins.int
    VS_FIELD_NUMBER: builtins.int
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def vs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Expr]: ...
    def __init__(
        self,
        *,
        src: global___SrcPosition | None = ...,
        vs: collections.abc.Iterable[global___Expr] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["src", b"src", "vs", b"vs"]) -> None: ...

global___ListVal = ListVal

@typing.final
class Lt(google.protobuf.message.Message):
    """op.ir:28"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LHS_FIELD_NUMBER: builtins.int
    RHS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def lhs(self) -> global___Expr: ...
    @property
    def rhs(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        lhs: global___Expr | None = ...,
        rhs: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> None: ...

global___Lt = Lt

@typing.final
class MergeDeleteWhenMatchedClause(google.protobuf.message.Message):
    """table.ir:44"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CONDITION_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def condition(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        condition: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["condition", b"condition", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["condition", b"condition", "src", b"src"]) -> None: ...

global___MergeDeleteWhenMatchedClause = MergeDeleteWhenMatchedClause

@typing.final
class MergeInsertWhenNotMatchedClause(google.protobuf.message.Message):
    """table.ir:48"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CONDITION_FIELD_NUMBER: builtins.int
    INSERT_KEYS_FIELD_NUMBER: builtins.int
    INSERT_VALUES_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def condition(self) -> global___Expr: ...
    @property
    def insert_keys(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Expr]: ...
    @property
    def insert_values(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Expr]: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        condition: global___Expr | None = ...,
        insert_keys: collections.abc.Iterable[global___Expr] | None = ...,
        insert_values: collections.abc.Iterable[global___Expr] | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["condition", b"condition", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["condition", b"condition", "insert_keys", b"insert_keys", "insert_values", b"insert_values", "src", b"src"]) -> None: ...

global___MergeInsertWhenNotMatchedClause = MergeInsertWhenNotMatchedClause

@typing.final
class MergeUpdateWhenMatchedClause(google.protobuf.message.Message):
    """table.ir:39"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CONDITION_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    UPDATE_ASSIGNMENTS_FIELD_NUMBER: builtins.int
    @property
    def condition(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def update_assignments(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_Expr_Expr]: ...
    def __init__(
        self,
        *,
        condition: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        update_assignments: collections.abc.Iterable[global___Tuple_Expr_Expr] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["condition", b"condition", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["condition", b"condition", "src", b"src", "update_assignments", b"update_assignments"]) -> None: ...

global___MergeUpdateWhenMatchedClause = MergeUpdateWhenMatchedClause

@typing.final
class Mod(google.protobuf.message.Message):
    """op.ir:50"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LHS_FIELD_NUMBER: builtins.int
    RHS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def lhs(self) -> global___Expr: ...
    @property
    def rhs(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        lhs: global___Expr | None = ...,
        rhs: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> None: ...

global___Mod = Mod

@typing.final
class Mul(google.protobuf.message.Message):
    """op.ir:46"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LHS_FIELD_NUMBER: builtins.int
    RHS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def lhs(self) -> global___Expr: ...
    @property
    def rhs(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        lhs: global___Expr | None = ...,
        rhs: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> None: ...

global___Mul = Mul

@typing.final
class NameRef(google.protobuf.message.Message):
    """ast.ir:126"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def name(self) -> global___Name: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        name: global___Name | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["name", b"name", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["name", b"name", "src", b"src"]) -> None: ...

global___NameRef = NameRef

@typing.final
class Neg(google.protobuf.message.Message):
    """op.ir:40"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    OPERAND_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def operand(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        operand: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["operand", b"operand", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["operand", b"operand", "src", b"src"]) -> None: ...

global___Neg = Neg

@typing.final
class Neq(google.protobuf.message.Message):
    """op.ir:26"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LHS_FIELD_NUMBER: builtins.int
    RHS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def lhs(self) -> global___Expr: ...
    @property
    def rhs(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        lhs: global___Expr | None = ...,
        rhs: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> None: ...

global___Neq = Neq

@typing.final
class Not(google.protobuf.message.Message):
    """op.ir:14"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    OPERAND_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def operand(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        operand: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["operand", b"operand", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["operand", b"operand", "src", b"src"]) -> None: ...

global___Not = Not

@typing.final
class NullVal(google.protobuf.message.Message):
    """const.ir:14"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SRC_FIELD_NUMBER: builtins.int
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["src", b"src"]) -> None: ...

global___NullVal = NullVal

@typing.final
class ObjectGetItem(google.protobuf.message.Message):
    """expr.ir:25"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ARGS_FIELD_NUMBER: builtins.int
    OBJ_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    obj: builtins.int
    @property
    def args(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Expr]: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        args: collections.abc.Iterable[global___Expr] | None = ...,
        obj: builtins.int = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["args", b"args", "obj", b"obj", "src", b"src"]) -> None: ...

global___ObjectGetItem = ObjectGetItem

@typing.final
class Or(google.protobuf.message.Message):
    """op.ir:18"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LHS_FIELD_NUMBER: builtins.int
    RHS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def lhs(self) -> global___Expr: ...
    @property
    def rhs(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        lhs: global___Expr | None = ...,
        rhs: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> None: ...

global___Or = Or

@typing.final
class Pow(google.protobuf.message.Message):
    """op.ir:52"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LHS_FIELD_NUMBER: builtins.int
    RHS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def lhs(self) -> global___Expr: ...
    @property
    def rhs(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        lhs: global___Expr | None = ...,
        rhs: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> None: ...

global___Pow = Pow

@typing.final
class PythonDateVal(google.protobuf.message.Message):
    """const.ir:68"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DAY_FIELD_NUMBER: builtins.int
    MONTH_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    YEAR_FIELD_NUMBER: builtins.int
    day: builtins.int
    month: builtins.int
    year: builtins.int
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        day: builtins.int = ...,
        month: builtins.int = ...,
        src: global___SrcPosition | None = ...,
        year: builtins.int = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["day", b"day", "month", b"month", "src", b"src", "year", b"year"]) -> None: ...

global___PythonDateVal = PythonDateVal

@typing.final
class PythonTimeVal(google.protobuf.message.Message):
    """const.ir:74"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    HOUR_FIELD_NUMBER: builtins.int
    MICROSECOND_FIELD_NUMBER: builtins.int
    MINUTE_FIELD_NUMBER: builtins.int
    SECOND_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    TZ_FIELD_NUMBER: builtins.int
    hour: builtins.int
    microsecond: builtins.int
    minute: builtins.int
    second: builtins.int
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def tz(self) -> global___PythonTimeZone: ...
    def __init__(
        self,
        *,
        hour: builtins.int = ...,
        microsecond: builtins.int = ...,
        minute: builtins.int = ...,
        second: builtins.int = ...,
        src: global___SrcPosition | None = ...,
        tz: global___PythonTimeZone | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["src", b"src", "tz", b"tz"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["hour", b"hour", "microsecond", b"microsecond", "minute", b"minute", "second", b"second", "src", b"src", "tz", b"tz"]) -> None: ...

global___PythonTimeVal = PythonTimeVal

@typing.final
class PythonTimestampVal(google.protobuf.message.Message):
    """const.ir:57"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DAY_FIELD_NUMBER: builtins.int
    HOUR_FIELD_NUMBER: builtins.int
    MICROSECOND_FIELD_NUMBER: builtins.int
    MINUTE_FIELD_NUMBER: builtins.int
    MONTH_FIELD_NUMBER: builtins.int
    SECOND_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    TZ_FIELD_NUMBER: builtins.int
    YEAR_FIELD_NUMBER: builtins.int
    day: builtins.int
    hour: builtins.int
    microsecond: builtins.int
    minute: builtins.int
    month: builtins.int
    second: builtins.int
    year: builtins.int
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def tz(self) -> global___PythonTimeZone: ...
    def __init__(
        self,
        *,
        day: builtins.int = ...,
        hour: builtins.int = ...,
        microsecond: builtins.int = ...,
        minute: builtins.int = ...,
        month: builtins.int = ...,
        second: builtins.int = ...,
        src: global___SrcPosition | None = ...,
        tz: global___PythonTimeZone | None = ...,
        year: builtins.int = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["src", b"src", "tz", b"tz"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["day", b"day", "hour", b"hour", "microsecond", b"microsecond", "minute", b"minute", "month", b"month", "second", b"second", "src", b"src", "tz", b"tz", "year", b"year"]) -> None: ...

global___PythonTimestampVal = PythonTimestampVal

@typing.final
class Range(google.protobuf.message.Message):
    """dataframe.ir:85"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    END_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    START_FIELD_NUMBER: builtins.int
    STEP_FIELD_NUMBER: builtins.int
    start: builtins.int
    @property
    def end(self) -> google.protobuf.wrappers_pb2.Int64Value: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def step(self) -> google.protobuf.wrappers_pb2.Int64Value: ...
    def __init__(
        self,
        *,
        end: google.protobuf.wrappers_pb2.Int64Value | None = ...,
        src: global___SrcPosition | None = ...,
        start: builtins.int = ...,
        step: google.protobuf.wrappers_pb2.Int64Value | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["end", b"end", "src", b"src", "step", b"step"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["end", b"end", "src", b"src", "start", b"start", "step", b"step"]) -> None: ...

global___Range = Range

@typing.final
class ReadAvro(google.protobuf.message.Message):
    """dataframe-io.ir:37"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PATH_FIELD_NUMBER: builtins.int
    READER_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    path: builtins.str
    @property
    def reader(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        path: builtins.str = ...,
        reader: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["reader", b"reader", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["path", b"path", "reader", b"reader", "src", b"src"]) -> None: ...

global___ReadAvro = ReadAvro

@typing.final
class ReadCsv(google.protobuf.message.Message):
    """dataframe-io.ir:33"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PATH_FIELD_NUMBER: builtins.int
    READER_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    path: builtins.str
    @property
    def reader(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        path: builtins.str = ...,
        reader: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["reader", b"reader", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["path", b"path", "reader", b"reader", "src", b"src"]) -> None: ...

global___ReadCsv = ReadCsv

@typing.final
class ReadFile(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    READ_AVRO_FIELD_NUMBER: builtins.int
    READ_CSV_FIELD_NUMBER: builtins.int
    READ_JSON_FIELD_NUMBER: builtins.int
    READ_LOAD_FIELD_NUMBER: builtins.int
    READ_ORC_FIELD_NUMBER: builtins.int
    READ_PARQUET_FIELD_NUMBER: builtins.int
    READ_XML_FIELD_NUMBER: builtins.int
    @property
    def read_avro(self) -> global___ReadAvro: ...
    @property
    def read_csv(self) -> global___ReadCsv: ...
    @property
    def read_json(self) -> global___ReadJson: ...
    @property
    def read_load(self) -> global___ReadLoad: ...
    @property
    def read_orc(self) -> global___ReadOrc: ...
    @property
    def read_parquet(self) -> global___ReadParquet: ...
    @property
    def read_xml(self) -> global___ReadXml: ...
    def __init__(
        self,
        *,
        read_avro: global___ReadAvro | None = ...,
        read_csv: global___ReadCsv | None = ...,
        read_json: global___ReadJson | None = ...,
        read_load: global___ReadLoad | None = ...,
        read_orc: global___ReadOrc | None = ...,
        read_parquet: global___ReadParquet | None = ...,
        read_xml: global___ReadXml | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["read_avro", b"read_avro", "read_csv", b"read_csv", "read_json", b"read_json", "read_load", b"read_load", "read_orc", b"read_orc", "read_parquet", b"read_parquet", "read_xml", b"read_xml", "variant", b"variant"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["read_avro", b"read_avro", "read_csv", b"read_csv", "read_json", b"read_json", "read_load", b"read_load", "read_orc", b"read_orc", "read_parquet", b"read_parquet", "read_xml", b"read_xml", "variant", b"variant"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["variant", b"variant"]) -> typing.Literal["read_avro", "read_csv", "read_json", "read_load", "read_orc", "read_parquet", "read_xml"] | None: ...

global___ReadFile = ReadFile

@typing.final
class ReadJson(google.protobuf.message.Message):
    """dataframe-io.ir:35"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PATH_FIELD_NUMBER: builtins.int
    READER_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    path: builtins.str
    @property
    def reader(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        path: builtins.str = ...,
        reader: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["reader", b"reader", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["path", b"path", "reader", b"reader", "src", b"src"]) -> None: ...

global___ReadJson = ReadJson

@typing.final
class ReadLoad(google.protobuf.message.Message):
    """dataframe-io.ir:31"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PATH_FIELD_NUMBER: builtins.int
    READER_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    path: builtins.str
    @property
    def reader(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        path: builtins.str = ...,
        reader: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["reader", b"reader", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["path", b"path", "reader", b"reader", "src", b"src"]) -> None: ...

global___ReadLoad = ReadLoad

@typing.final
class ReadOrc(google.protobuf.message.Message):
    """dataframe-io.ir:39"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PATH_FIELD_NUMBER: builtins.int
    READER_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    path: builtins.str
    @property
    def reader(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        path: builtins.str = ...,
        reader: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["reader", b"reader", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["path", b"path", "reader", b"reader", "src", b"src"]) -> None: ...

global___ReadOrc = ReadOrc

@typing.final
class ReadParquet(google.protobuf.message.Message):
    """dataframe-io.ir:41"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PATH_FIELD_NUMBER: builtins.int
    READER_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    path: builtins.str
    @property
    def reader(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        path: builtins.str = ...,
        reader: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["reader", b"reader", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["path", b"path", "reader", b"reader", "src", b"src"]) -> None: ...

global___ReadParquet = ReadParquet

@typing.final
class ReadTable(google.protobuf.message.Message):
    """dataframe-io.ir:20"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    READER_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def name(self) -> global___NameRef: ...
    @property
    def reader(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        name: global___NameRef | None = ...,
        reader: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["name", b"name", "reader", b"reader", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["name", b"name", "reader", b"reader", "src", b"src"]) -> None: ...

global___ReadTable = ReadTable

@typing.final
class ReadXml(google.protobuf.message.Message):
    """dataframe-io.ir:43"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PATH_FIELD_NUMBER: builtins.int
    READER_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    path: builtins.str
    @property
    def reader(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        path: builtins.str = ...,
        reader: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["reader", b"reader", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["path", b"path", "reader", b"reader", "src", b"src"]) -> None: ...

global___ReadXml = ReadXml

@typing.final
class RedactedConst(google.protobuf.message.Message):
    """const.ir:8"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PROXY_VALUE_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def proxy_value(self) -> global___Const: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        proxy_value: global___Const | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["proxy_value", b"proxy_value", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["proxy_value", b"proxy_value", "src", b"src"]) -> None: ...

global___RedactedConst = RedactedConst

@typing.final
class RelationalGroupedDataframeAgg(google.protobuf.message.Message):
    """dataframe-grouped.ir:33"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    EXPRS_FIELD_NUMBER: builtins.int
    GROUPED_DF_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def exprs(self) -> global___ExprArgList: ...
    @property
    def grouped_df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        exprs: global___ExprArgList | None = ...,
        grouped_df: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["exprs", b"exprs", "grouped_df", b"grouped_df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["exprs", b"exprs", "grouped_df", b"grouped_df", "src", b"src"]) -> None: ...

global___RelationalGroupedDataframeAgg = RelationalGroupedDataframeAgg

@typing.final
class RelationalGroupedDataframeApplyInPandas(google.protobuf.message.Message):
    """dataframe-grouped.ir:45"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNC_FIELD_NUMBER: builtins.int
    GROUPED_DF_FIELD_NUMBER: builtins.int
    KWARGS_FIELD_NUMBER: builtins.int
    OUTPUT_SCHEMA_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def func(self) -> global___Callable: ...
    @property
    def grouped_df(self) -> global___Expr: ...
    @property
    def kwargs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_Expr]: ...
    @property
    def output_schema(self) -> global___StructType: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        func: global___Callable | None = ...,
        grouped_df: global___Expr | None = ...,
        kwargs: collections.abc.Iterable[global___Tuple_String_Expr] | None = ...,
        output_schema: global___StructType | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["func", b"func", "grouped_df", b"grouped_df", "output_schema", b"output_schema", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["func", b"func", "grouped_df", b"grouped_df", "kwargs", b"kwargs", "output_schema", b"output_schema", "src", b"src"]) -> None: ...

global___RelationalGroupedDataframeApplyInPandas = RelationalGroupedDataframeApplyInPandas

@typing.final
class RelationalGroupedDataframeBuiltin(google.protobuf.message.Message):
    """dataframe-grouped.ir:39"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    AGG_NAME_FIELD_NUMBER: builtins.int
    COLS_FIELD_NUMBER: builtins.int
    GROUPED_DF_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    agg_name: builtins.str
    @property
    def cols(self) -> global___ExprArgList: ...
    @property
    def grouped_df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        agg_name: builtins.str = ...,
        cols: global___ExprArgList | None = ...,
        grouped_df: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["cols", b"cols", "grouped_df", b"grouped_df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["agg_name", b"agg_name", "cols", b"cols", "grouped_df", b"grouped_df", "src", b"src"]) -> None: ...

global___RelationalGroupedDataframeBuiltin = RelationalGroupedDataframeBuiltin

@typing.final
class RelationalGroupedDataframePivot(google.protobuf.message.Message):
    """dataframe-grouped.ir:52"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DEFAULT_ON_NULL_FIELD_NUMBER: builtins.int
    GROUPED_DF_FIELD_NUMBER: builtins.int
    PIVOT_COL_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    VALUES_FIELD_NUMBER: builtins.int
    @property
    def default_on_null(self) -> global___Expr: ...
    @property
    def grouped_df(self) -> global___Expr: ...
    @property
    def pivot_col(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def values(self) -> global___Expr: ...
    def __init__(
        self,
        *,
        default_on_null: global___Expr | None = ...,
        grouped_df: global___Expr | None = ...,
        pivot_col: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        values: global___Expr | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["default_on_null", b"default_on_null", "grouped_df", b"grouped_df", "pivot_col", b"pivot_col", "src", b"src", "values", b"values"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["default_on_null", b"default_on_null", "grouped_df", b"grouped_df", "pivot_col", b"pivot_col", "src", b"src", "values", b"values"]) -> None: ...

global___RelationalGroupedDataframePivot = RelationalGroupedDataframePivot

@typing.final
class RelationalGroupedDataframeRef(google.protobuf.message.Message):
    """dataframe-grouped.ir:2"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    id: builtins.int
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        id: builtins.int = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["id", b"id", "src", b"src"]) -> None: ...

global___RelationalGroupedDataframeRef = RelationalGroupedDataframeRef

@typing.final
class Request(google.protobuf.message.Message):
    """ast.ir:12"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    INTERNED_VALUE_TABLE_FIELD_NUMBER: builtins.int
    BODY_FIELD_NUMBER: builtins.int
    CLIENT_AST_VERSION_FIELD_NUMBER: builtins.int
    CLIENT_LANGUAGE_FIELD_NUMBER: builtins.int
    CLIENT_VERSION_FIELD_NUMBER: builtins.int
    ID_FIELD_NUMBER: builtins.int
    client_ast_version: builtins.int
    id: builtins.bytes
    @property
    def interned_value_table(self) -> global___InternedValueTable: ...
    @property
    def body(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Stmt]: ...
    @property
    def client_language(self) -> global___Language: ...
    @property
    def client_version(self) -> global___Version: ...
    def __init__(
        self,
        *,
        interned_value_table: global___InternedValueTable | None = ...,
        body: collections.abc.Iterable[global___Stmt] | None = ...,
        client_ast_version: builtins.int = ...,
        client_language: global___Language | None = ...,
        client_version: global___Version | None = ...,
        id: builtins.bytes = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["client_language", b"client_language", "client_version", b"client_version", "interned_value_table", b"interned_value_table"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["body", b"body", "client_ast_version", b"client_ast_version", "client_language", b"client_language", "client_version", b"client_version", "id", b"id", "interned_value_table", b"interned_value_table"]) -> None: ...

global___Request = Request

@typing.final
class Response(google.protobuf.message.Message):
    """ast.ir:21"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    INTERNED_VALUE_TABLE_FIELD_NUMBER: builtins.int
    BODY_FIELD_NUMBER: builtins.int
    @property
    def interned_value_table(self) -> global___InternedValueTable: ...
    @property
    def body(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Result]: ...
    def __init__(
        self,
        *,
        interned_value_table: global___InternedValueTable | None = ...,
        body: collections.abc.Iterable[global___Result] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["interned_value_table", b"interned_value_table"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["body", b"body", "interned_value_table", b"interned_value_table"]) -> None: ...

global___Response = Response

@typing.final
class Result(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TRAIT_ERROR_FIELD_NUMBER: builtins.int
    EVAL_OK_FIELD_NUMBER: builtins.int
    EXTENSION_ERROR_FIELD_NUMBER: builtins.int
    SESSION_RESET_REQUIRED_ERROR_FIELD_NUMBER: builtins.int
    @property
    def trait_error(self) -> global___Error: ...
    @property
    def eval_ok(self) -> global___EvalOk: ...
    @property
    def extension_error(self) -> global___ExtensionError: ...
    @property
    def session_reset_required_error(self) -> global___SessionResetRequiredError: ...
    def __init__(
        self,
        *,
        trait_error: global___Error | None = ...,
        eval_ok: global___EvalOk | None = ...,
        extension_error: global___ExtensionError | None = ...,
        session_reset_required_error: global___SessionResetRequiredError | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["eval_ok", b"eval_ok", "extension_error", b"extension_error", "session_reset_required_error", b"session_reset_required_error", "trait_error", b"trait_error", "variant", b"variant"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["eval_ok", b"eval_ok", "extension_error", b"extension_error", "session_reset_required_error", b"session_reset_required_error", "trait_error", b"trait_error", "variant", b"variant"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["variant", b"variant"]) -> typing.Literal["trait_error", "eval_ok", "extension_error", "session_reset_required_error"] | None: ...

global___Result = Result

@typing.final
class Row(google.protobuf.message.Message):
    """expr.ir:19"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAMES_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    VS_FIELD_NUMBER: builtins.int
    @property
    def names(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def vs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Expr]: ...
    def __init__(
        self,
        *,
        names: collections.abc.Iterable[builtins.str] | None = ...,
        src: global___SrcPosition | None = ...,
        vs: collections.abc.Iterable[global___Expr] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["names", b"names", "src", b"src", "vs", b"vs"]) -> None: ...

global___Row = Row

@typing.final
class SeqMapVal(google.protobuf.message.Message):
    """expr.ir:14"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    KVS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def kvs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___TupleVal]: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        kvs: collections.abc.Iterable[global___TupleVal] | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["kvs", b"kvs", "src", b"src"]) -> None: ...

global___SeqMapVal = SeqMapVal

@typing.final
class SessionResetRequiredError(google.protobuf.message.Message):
    """ast.ir:115"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BIND_ID_FIELD_NUMBER: builtins.int
    UID_FIELD_NUMBER: builtins.int
    bind_id: builtins.int
    uid: builtins.int
    def __init__(
        self,
        *,
        bind_id: builtins.int = ...,
        uid: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["bind_id", b"bind_id", "uid", b"uid"]) -> None: ...

global___SessionResetRequiredError = SessionResetRequiredError

@typing.final
class SessionTableFunction(google.protobuf.message.Message):
    """dataframe.ir:104"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FN_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def fn(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        fn: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["fn", b"fn", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["fn", b"fn", "src", b"src"]) -> None: ...

global___SessionTableFunction = SessionTableFunction

@typing.final
class SfQueryResult(google.protobuf.message.Message):
    """ast.ir:93"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    UUID_FIELD_NUMBER: builtins.int
    uuid: builtins.str
    def __init__(
        self,
        *,
        uuid: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["uuid", b"uuid"]) -> None: ...

global___SfQueryResult = SfQueryResult

@typing.final
class ShowResult(google.protobuf.message.Message):
    """ast.ir:99"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___ShowResult = ShowResult

@typing.final
class Sql(google.protobuf.message.Message):
    """dataframe.ir:91"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARAMS_FIELD_NUMBER: builtins.int
    QUERY_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    query: builtins.str
    @property
    def params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Expr]: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        params: collections.abc.Iterable[global___Expr] | None = ...,
        query: builtins.str = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["params", b"params", "query", b"query", "src", b"src"]) -> None: ...

global___Sql = Sql

@typing.final
class SqlExpr(google.protobuf.message.Message):
    """column.ir:7"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DF_ALIAS_FIELD_NUMBER: builtins.int
    SQL_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    sql: builtins.str
    @property
    def df_alias(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        df_alias: google.protobuf.wrappers_pb2.StringValue | None = ...,
        sql: builtins.str = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df_alias", b"df_alias", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["df_alias", b"df_alias", "sql", b"sql", "src", b"src"]) -> None: ...

global___SqlExpr = SqlExpr

@typing.final
class Stmt(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BIND_FIELD_NUMBER: builtins.int
    EVAL_FIELD_NUMBER: builtins.int
    EXTENSION_STMT_FIELD_NUMBER: builtins.int
    TRUNCATED_STMT_FIELD_NUMBER: builtins.int
    @property
    def bind(self) -> global___Bind: ...
    @property
    def eval(self) -> global___Eval: ...
    @property
    def extension_stmt(self) -> global___ExtensionStmt: ...
    @property
    def truncated_stmt(self) -> global___TruncatedStmt: ...
    def __init__(
        self,
        *,
        bind: global___Bind | None = ...,
        eval: global___Eval | None = ...,
        extension_stmt: global___ExtensionStmt | None = ...,
        truncated_stmt: global___TruncatedStmt | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["bind", b"bind", "eval", b"eval", "extension_stmt", b"extension_stmt", "truncated_stmt", b"truncated_stmt", "variant", b"variant"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["bind", b"bind", "eval", b"eval", "extension_stmt", b"extension_stmt", "truncated_stmt", b"truncated_stmt", "variant", b"variant"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["variant", b"variant"]) -> typing.Literal["bind", "eval", "extension_stmt", "truncated_stmt"] | None: ...

global___Stmt = Stmt

@typing.final
class StoredProcedure(google.protobuf.message.Message):
    """fn.ir:37"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ARTIFACT_REPOSITORY_FIELD_NUMBER: builtins.int
    COMMENT_FIELD_NUMBER: builtins.int
    EXECUTE_AS_FIELD_NUMBER: builtins.int
    EXTERNAL_ACCESS_INTEGRATIONS_FIELD_NUMBER: builtins.int
    FUNC_FIELD_NUMBER: builtins.int
    IF_NOT_EXISTS_FIELD_NUMBER: builtins.int
    IMPORTS_FIELD_NUMBER: builtins.int
    INPUT_TYPES_FIELD_NUMBER: builtins.int
    IS_PERMANENT_FIELD_NUMBER: builtins.int
    KWARGS_FIELD_NUMBER: builtins.int
    LOG_ON_EXCEPTION_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    PACKAGES_FIELD_NUMBER: builtins.int
    PARALLEL_FIELD_NUMBER: builtins.int
    REPLACE_FIELD_NUMBER: builtins.int
    RESOURCE_CONSTRAINT_FIELD_NUMBER: builtins.int
    RETURN_TYPE_FIELD_NUMBER: builtins.int
    SECRETS_FIELD_NUMBER: builtins.int
    SOURCE_CODE_DISPLAY_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STAGE_LOCATION_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    STRICT_FIELD_NUMBER: builtins.int
    execute_as: builtins.str
    if_not_exists: builtins.bool
    is_permanent: builtins.bool
    parallel: builtins.int
    replace: builtins.bool
    source_code_display: builtins.bool
    stage_location: builtins.str
    strict: builtins.bool
    @property
    def artifact_repository(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def comment(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def external_access_integrations(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def func(self) -> global___Callable: ...
    @property
    def imports(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___NameRef]: ...
    @property
    def input_types(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___DataType]: ...
    @property
    def kwargs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_Expr]: ...
    @property
    def log_on_exception(self) -> google.protobuf.wrappers_pb2.BoolValue: ...
    @property
    def name(self) -> global___NameRef: ...
    @property
    def packages(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def resource_constraint(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    @property
    def return_type(self) -> global___DataType: ...
    @property
    def secrets(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    def __init__(
        self,
        *,
        artifact_repository: google.protobuf.wrappers_pb2.StringValue | None = ...,
        comment: google.protobuf.wrappers_pb2.StringValue | None = ...,
        execute_as: builtins.str = ...,
        external_access_integrations: collections.abc.Iterable[builtins.str] | None = ...,
        func: global___Callable | None = ...,
        if_not_exists: builtins.bool = ...,
        imports: collections.abc.Iterable[global___NameRef] | None = ...,
        input_types: collections.abc.Iterable[global___DataType] | None = ...,
        is_permanent: builtins.bool = ...,
        kwargs: collections.abc.Iterable[global___Tuple_String_Expr] | None = ...,
        log_on_exception: google.protobuf.wrappers_pb2.BoolValue | None = ...,
        name: global___NameRef | None = ...,
        packages: collections.abc.Iterable[builtins.str] | None = ...,
        parallel: builtins.int = ...,
        replace: builtins.bool = ...,
        resource_constraint: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
        return_type: global___DataType | None = ...,
        secrets: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
        source_code_display: builtins.bool = ...,
        src: global___SrcPosition | None = ...,
        stage_location: builtins.str = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
        strict: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["artifact_repository", b"artifact_repository", "comment", b"comment", "func", b"func", "log_on_exception", b"log_on_exception", "name", b"name", "return_type", b"return_type", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["artifact_repository", b"artifact_repository", "comment", b"comment", "execute_as", b"execute_as", "external_access_integrations", b"external_access_integrations", "func", b"func", "if_not_exists", b"if_not_exists", "imports", b"imports", "input_types", b"input_types", "is_permanent", b"is_permanent", "kwargs", b"kwargs", "log_on_exception", b"log_on_exception", "name", b"name", "packages", b"packages", "parallel", b"parallel", "replace", b"replace", "resource_constraint", b"resource_constraint", "return_type", b"return_type", "secrets", b"secrets", "source_code_display", b"source_code_display", "src", b"src", "stage_location", b"stage_location", "statement_params", b"statement_params", "strict", b"strict"]) -> None: ...

global___StoredProcedure = StoredProcedure

@typing.final
class StringVal(google.protobuf.message.Message):
    """const.ir:41"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SRC_FIELD_NUMBER: builtins.int
    V_FIELD_NUMBER: builtins.int
    v: builtins.str
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        src: global___SrcPosition | None = ...,
        v: builtins.str = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["src", b"src", "v", b"v"]) -> None: ...

global___StringVal = StringVal

@typing.final
class Sub(google.protobuf.message.Message):
    """op.ir:44"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LHS_FIELD_NUMBER: builtins.int
    RHS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def lhs(self) -> global___Expr: ...
    @property
    def rhs(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        lhs: global___Expr | None = ...,
        rhs: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["lhs", b"lhs", "rhs", b"rhs", "src", b"src"]) -> None: ...

global___Sub = Sub

@typing.final
class Table(google.protobuf.message.Message):
    """dataframe.ir:98"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IS_TEMP_TABLE_FOR_CLEANUP_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    VARIANT_FIELD_NUMBER: builtins.int
    is_temp_table_for_cleanup: builtins.bool
    @property
    def name(self) -> global___NameRef: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def variant(self) -> global___TableVariant: ...
    def __init__(
        self,
        *,
        is_temp_table_for_cleanup: builtins.bool = ...,
        name: global___NameRef | None = ...,
        src: global___SrcPosition | None = ...,
        variant: global___TableVariant | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["name", b"name", "src", b"src", "variant", b"variant"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["is_temp_table_for_cleanup", b"is_temp_table_for_cleanup", "name", b"name", "src", b"src", "variant", b"variant"]) -> None: ...

global___Table = Table

@typing.final
class TableDelete(google.protobuf.message.Message):
    """table.ir:1"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BLOCK_FIELD_NUMBER: builtins.int
    CONDITION_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    SOURCE_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    block: builtins.bool
    @property
    def condition(self) -> global___Expr: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def source(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    def __init__(
        self,
        *,
        block: builtins.bool = ...,
        condition: global___Expr | None = ...,
        df: global___Expr | None = ...,
        source: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["condition", b"condition", "df", b"df", "source", b"source", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["block", b"block", "condition", b"condition", "df", b"df", "source", b"source", "src", b"src", "statement_params", b"statement_params"]) -> None: ...

global___TableDelete = TableDelete

@typing.final
class TableDropTable(google.protobuf.message.Message):
    """table.ir:9"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DF_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def df(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        df: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["df", b"df", "src", b"src"]) -> None: ...

global___TableDropTable = TableDropTable

@typing.final
class TableFnCallAlias(google.protobuf.message.Message):
    """fn.ir:165"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ALIASES_FIELD_NUMBER: builtins.int
    LHS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def aliases(self) -> global___ExprArgList: ...
    @property
    def lhs(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        aliases: global___ExprArgList | None = ...,
        lhs: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["aliases", b"aliases", "lhs", b"lhs", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["aliases", b"aliases", "lhs", b"lhs", "src", b"src"]) -> None: ...

global___TableFnCallAlias = TableFnCallAlias

@typing.final
class TableFnCallOver(google.protobuf.message.Message):
    """fn.ir:159"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LHS_FIELD_NUMBER: builtins.int
    ORDER_BY_FIELD_NUMBER: builtins.int
    PARTITION_BY_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def lhs(self) -> global___Expr: ...
    @property
    def order_by(self) -> global___ExprArgList: ...
    @property
    def partition_by(self) -> global___ExprArgList: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        lhs: global___Expr | None = ...,
        order_by: global___ExprArgList | None = ...,
        partition_by: global___ExprArgList | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["lhs", b"lhs", "order_by", b"order_by", "partition_by", b"partition_by", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["lhs", b"lhs", "order_by", b"order_by", "partition_by", b"partition_by", "src", b"src"]) -> None: ...

global___TableFnCallOver = TableFnCallOver

@typing.final
class TableMerge(google.protobuf.message.Message):
    """table.ir:13"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BLOCK_FIELD_NUMBER: builtins.int
    CLAUSES_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    JOIN_EXPR_FIELD_NUMBER: builtins.int
    SOURCE_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    block: builtins.bool
    @property
    def clauses(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Expr]: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def join_expr(self) -> global___Expr: ...
    @property
    def source(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    def __init__(
        self,
        *,
        block: builtins.bool = ...,
        clauses: collections.abc.Iterable[global___Expr] | None = ...,
        df: global___Expr | None = ...,
        join_expr: global___Expr | None = ...,
        source: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "join_expr", b"join_expr", "source", b"source", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["block", b"block", "clauses", b"clauses", "df", b"df", "join_expr", b"join_expr", "source", b"source", "src", b"src", "statement_params", b"statement_params"]) -> None: ...

global___TableMerge = TableMerge

@typing.final
class TableSample(google.protobuf.message.Message):
    """table.ir:22"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DF_FIELD_NUMBER: builtins.int
    NUM_FIELD_NUMBER: builtins.int
    PROBABILITY_FRACTION_FIELD_NUMBER: builtins.int
    SAMPLING_METHOD_FIELD_NUMBER: builtins.int
    SEED_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def df(self) -> global___Expr: ...
    @property
    def num(self) -> google.protobuf.wrappers_pb2.Int64Value: ...
    @property
    def probability_fraction(self) -> google.protobuf.wrappers_pb2.DoubleValue: ...
    @property
    def sampling_method(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def seed(self) -> google.protobuf.wrappers_pb2.Int64Value: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        df: global___Expr | None = ...,
        num: google.protobuf.wrappers_pb2.Int64Value | None = ...,
        probability_fraction: google.protobuf.wrappers_pb2.DoubleValue | None = ...,
        sampling_method: google.protobuf.wrappers_pb2.StringValue | None = ...,
        seed: google.protobuf.wrappers_pb2.Int64Value | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "num", b"num", "probability_fraction", b"probability_fraction", "sampling_method", b"sampling_method", "seed", b"seed", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["df", b"df", "num", b"num", "probability_fraction", b"probability_fraction", "sampling_method", b"sampling_method", "seed", b"seed", "src", b"src"]) -> None: ...

global___TableSample = TableSample

@typing.final
class TableUpdate(google.protobuf.message.Message):
    """table.ir:30"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASSIGNMENTS_FIELD_NUMBER: builtins.int
    BLOCK_FIELD_NUMBER: builtins.int
    CONDITION_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    SOURCE_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    block: builtins.bool
    @property
    def assignments(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_Expr]: ...
    @property
    def condition(self) -> global___Expr: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def source(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    def __init__(
        self,
        *,
        assignments: collections.abc.Iterable[global___Tuple_String_Expr] | None = ...,
        block: builtins.bool = ...,
        condition: global___Expr | None = ...,
        df: global___Expr | None = ...,
        source: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["condition", b"condition", "df", b"df", "source", b"source", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["assignments", b"assignments", "block", b"block", "condition", b"condition", "df", b"df", "source", b"source", "src", b"src", "statement_params", b"statement_params"]) -> None: ...

global___TableUpdate = TableUpdate

@typing.final
class ToSnowparkPandas(google.protobuf.message.Message):
    """dataframe.ir:342"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLUMNS_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    INDEX_COL_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def columns(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def df(self) -> global___Expr: ...
    @property
    def index_col(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self,
        *,
        columns: collections.abc.Iterable[builtins.str] | None = ...,
        df: global___Expr | None = ...,
        index_col: collections.abc.Iterable[builtins.str] | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["df", b"df", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["columns", b"columns", "df", b"df", "index_col", b"index_col", "src", b"src"]) -> None: ...

global___ToSnowparkPandas = ToSnowparkPandas

@typing.final
class TruncatedExpr(google.protobuf.message.Message):
    """expr.ir:48"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SELF_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    @property
    def self(self) -> google.protobuf.wrappers_pb2.Int64Value: ...
    @property
    def src(self) -> global___SrcPosition: ...
    def __init__(
        self_,  # pyright: ignore[reportSelfClsParameterName]
        *,
        self: google.protobuf.wrappers_pb2.Int64Value | None = ...,
        src: global___SrcPosition | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["self", b"self", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["self", b"self", "src", b"src"]) -> None: ...

global___TruncatedExpr = TruncatedExpr

@typing.final
class TruncatedStmt(google.protobuf.message.Message):
    """ast.ir:58"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BIND_IDS_FIELD_NUMBER: builtins.int
    @property
    def bind_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    def __init__(
        self,
        *,
        bind_ids: collections.abc.Iterable[builtins.int] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["bind_ids", b"bind_ids"]) -> None: ...

global___TruncatedStmt = TruncatedStmt

@typing.final
class TupleVal(google.protobuf.message.Message):
    """expr.ir:4"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SRC_FIELD_NUMBER: builtins.int
    VS_FIELD_NUMBER: builtins.int
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def vs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Expr]: ...
    def __init__(
        self,
        *,
        src: global___SrcPosition | None = ...,
        vs: collections.abc.Iterable[global___Expr] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["src", b"src", "vs", b"vs"]) -> None: ...

global___TupleVal = TupleVal

@typing.final
class Udaf(google.protobuf.message.Message):
    """fn.ir:118"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ARTIFACT_REPOSITORY_FIELD_NUMBER: builtins.int
    COMMENT_FIELD_NUMBER: builtins.int
    EXTERNAL_ACCESS_INTEGRATIONS_FIELD_NUMBER: builtins.int
    HANDLER_FIELD_NUMBER: builtins.int
    IF_NOT_EXISTS_FIELD_NUMBER: builtins.int
    IMMUTABLE_FIELD_NUMBER: builtins.int
    IMPORTS_FIELD_NUMBER: builtins.int
    INPUT_TYPES_FIELD_NUMBER: builtins.int
    IS_PERMANENT_FIELD_NUMBER: builtins.int
    KWARGS_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    PACKAGES_FIELD_NUMBER: builtins.int
    PARALLEL_FIELD_NUMBER: builtins.int
    REPLACE_FIELD_NUMBER: builtins.int
    RESOURCE_CONSTRAINT_FIELD_NUMBER: builtins.int
    RETURN_TYPE_FIELD_NUMBER: builtins.int
    SECRETS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STAGE_LOCATION_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    if_not_exists: builtins.bool
    immutable: builtins.bool
    is_permanent: builtins.bool
    parallel: builtins.int
    replace: builtins.bool
    @property
    def artifact_repository(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def comment(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def external_access_integrations(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def handler(self) -> global___Callable: ...
    @property
    def imports(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___NameRef]: ...
    @property
    def input_types(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___DataType]: ...
    @property
    def kwargs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_Expr]: ...
    @property
    def name(self) -> global___NameRef: ...
    @property
    def packages(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def resource_constraint(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    @property
    def return_type(self) -> global___DataType: ...
    @property
    def secrets(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def stage_location(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    def __init__(
        self,
        *,
        artifact_repository: google.protobuf.wrappers_pb2.StringValue | None = ...,
        comment: google.protobuf.wrappers_pb2.StringValue | None = ...,
        external_access_integrations: collections.abc.Iterable[builtins.str] | None = ...,
        handler: global___Callable | None = ...,
        if_not_exists: builtins.bool = ...,
        immutable: builtins.bool = ...,
        imports: collections.abc.Iterable[global___NameRef] | None = ...,
        input_types: collections.abc.Iterable[global___DataType] | None = ...,
        is_permanent: builtins.bool = ...,
        kwargs: collections.abc.Iterable[global___Tuple_String_Expr] | None = ...,
        name: global___NameRef | None = ...,
        packages: collections.abc.Iterable[builtins.str] | None = ...,
        parallel: builtins.int = ...,
        replace: builtins.bool = ...,
        resource_constraint: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
        return_type: global___DataType | None = ...,
        secrets: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
        src: global___SrcPosition | None = ...,
        stage_location: google.protobuf.wrappers_pb2.StringValue | None = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["artifact_repository", b"artifact_repository", "comment", b"comment", "handler", b"handler", "name", b"name", "return_type", b"return_type", "src", b"src", "stage_location", b"stage_location"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["artifact_repository", b"artifact_repository", "comment", b"comment", "external_access_integrations", b"external_access_integrations", "handler", b"handler", "if_not_exists", b"if_not_exists", "immutable", b"immutable", "imports", b"imports", "input_types", b"input_types", "is_permanent", b"is_permanent", "kwargs", b"kwargs", "name", b"name", "packages", b"packages", "parallel", b"parallel", "replace", b"replace", "resource_constraint", b"resource_constraint", "return_type", b"return_type", "secrets", b"secrets", "src", b"src", "stage_location", b"stage_location", "statement_params", b"statement_params"]) -> None: ...

global___Udaf = Udaf

@typing.final
class Udf(google.protobuf.message.Message):
    """fn.ir:62"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ARTIFACT_REPOSITORY_FIELD_NUMBER: builtins.int
    COMMENT_FIELD_NUMBER: builtins.int
    EXTERNAL_ACCESS_INTEGRATIONS_FIELD_NUMBER: builtins.int
    FUNC_FIELD_NUMBER: builtins.int
    IF_NOT_EXISTS_FIELD_NUMBER: builtins.int
    IMMUTABLE_FIELD_NUMBER: builtins.int
    IMPORTS_FIELD_NUMBER: builtins.int
    INPUT_TYPES_FIELD_NUMBER: builtins.int
    IS_PERMANENT_FIELD_NUMBER: builtins.int
    KWARGS_FIELD_NUMBER: builtins.int
    MAX_BATCH_SIZE_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    PACKAGES_FIELD_NUMBER: builtins.int
    PARALLEL_FIELD_NUMBER: builtins.int
    REPLACE_FIELD_NUMBER: builtins.int
    RESOURCE_CONSTRAINT_FIELD_NUMBER: builtins.int
    RETURN_TYPE_FIELD_NUMBER: builtins.int
    SECRETS_FIELD_NUMBER: builtins.int
    SECURE_FIELD_NUMBER: builtins.int
    SOURCE_CODE_DISPLAY_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STAGE_LOCATION_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    STRICT_FIELD_NUMBER: builtins.int
    if_not_exists: builtins.bool
    immutable: builtins.bool
    is_permanent: builtins.bool
    parallel: builtins.int
    replace: builtins.bool
    secure: builtins.bool
    source_code_display: builtins.bool
    stage_location: builtins.str
    strict: builtins.bool
    @property
    def artifact_repository(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def comment(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def external_access_integrations(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def func(self) -> global___Callable: ...
    @property
    def imports(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___NameRef]: ...
    @property
    def input_types(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___DataType]: ...
    @property
    def kwargs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_Expr]: ...
    @property
    def max_batch_size(self) -> google.protobuf.wrappers_pb2.Int64Value: ...
    @property
    def name(self) -> global___NameRef: ...
    @property
    def packages(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def resource_constraint(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    @property
    def return_type(self) -> global___DataType: ...
    @property
    def secrets(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    def __init__(
        self,
        *,
        artifact_repository: google.protobuf.wrappers_pb2.StringValue | None = ...,
        comment: google.protobuf.wrappers_pb2.StringValue | None = ...,
        external_access_integrations: collections.abc.Iterable[builtins.str] | None = ...,
        func: global___Callable | None = ...,
        if_not_exists: builtins.bool = ...,
        immutable: builtins.bool = ...,
        imports: collections.abc.Iterable[global___NameRef] | None = ...,
        input_types: collections.abc.Iterable[global___DataType] | None = ...,
        is_permanent: builtins.bool = ...,
        kwargs: collections.abc.Iterable[global___Tuple_String_Expr] | None = ...,
        max_batch_size: google.protobuf.wrappers_pb2.Int64Value | None = ...,
        name: global___NameRef | None = ...,
        packages: collections.abc.Iterable[builtins.str] | None = ...,
        parallel: builtins.int = ...,
        replace: builtins.bool = ...,
        resource_constraint: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
        return_type: global___DataType | None = ...,
        secrets: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
        secure: builtins.bool = ...,
        source_code_display: builtins.bool = ...,
        src: global___SrcPosition | None = ...,
        stage_location: builtins.str = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
        strict: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["artifact_repository", b"artifact_repository", "comment", b"comment", "func", b"func", "max_batch_size", b"max_batch_size", "name", b"name", "return_type", b"return_type", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["artifact_repository", b"artifact_repository", "comment", b"comment", "external_access_integrations", b"external_access_integrations", "func", b"func", "if_not_exists", b"if_not_exists", "immutable", b"immutable", "imports", b"imports", "input_types", b"input_types", "is_permanent", b"is_permanent", "kwargs", b"kwargs", "max_batch_size", b"max_batch_size", "name", b"name", "packages", b"packages", "parallel", b"parallel", "replace", b"replace", "resource_constraint", b"resource_constraint", "return_type", b"return_type", "secrets", b"secrets", "secure", b"secure", "source_code_display", b"source_code_display", "src", b"src", "stage_location", b"stage_location", "statement_params", b"statement_params", "strict", b"strict"]) -> None: ...

global___Udf = Udf

@typing.final
class Udtf(google.protobuf.message.Message):
    """fn.ir:93"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ARTIFACT_REPOSITORY_FIELD_NUMBER: builtins.int
    COMMENT_FIELD_NUMBER: builtins.int
    EXTERNAL_ACCESS_INTEGRATIONS_FIELD_NUMBER: builtins.int
    HANDLER_FIELD_NUMBER: builtins.int
    IF_NOT_EXISTS_FIELD_NUMBER: builtins.int
    IMMUTABLE_FIELD_NUMBER: builtins.int
    IMPORTS_FIELD_NUMBER: builtins.int
    INPUT_TYPES_FIELD_NUMBER: builtins.int
    IS_PERMANENT_FIELD_NUMBER: builtins.int
    KWARGS_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    OUTPUT_SCHEMA_FIELD_NUMBER: builtins.int
    PACKAGES_FIELD_NUMBER: builtins.int
    PARALLEL_FIELD_NUMBER: builtins.int
    REPLACE_FIELD_NUMBER: builtins.int
    RESOURCE_CONSTRAINT_FIELD_NUMBER: builtins.int
    SECRETS_FIELD_NUMBER: builtins.int
    SECURE_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STAGE_LOCATION_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    STRICT_FIELD_NUMBER: builtins.int
    if_not_exists: builtins.bool
    immutable: builtins.bool
    is_permanent: builtins.bool
    parallel: builtins.int
    replace: builtins.bool
    secure: builtins.bool
    stage_location: builtins.str
    strict: builtins.bool
    @property
    def artifact_repository(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def comment(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def external_access_integrations(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def handler(self) -> global___Callable: ...
    @property
    def imports(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___NameRef]: ...
    @property
    def input_types(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___DataType]: ...
    @property
    def kwargs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_Expr]: ...
    @property
    def name(self) -> global___NameRef: ...
    @property
    def output_schema(self) -> global___UdtfSchema: ...
    @property
    def packages(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def resource_constraint(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    @property
    def secrets(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    def __init__(
        self,
        *,
        artifact_repository: google.protobuf.wrappers_pb2.StringValue | None = ...,
        comment: google.protobuf.wrappers_pb2.StringValue | None = ...,
        external_access_integrations: collections.abc.Iterable[builtins.str] | None = ...,
        handler: global___Callable | None = ...,
        if_not_exists: builtins.bool = ...,
        immutable: builtins.bool = ...,
        imports: collections.abc.Iterable[global___NameRef] | None = ...,
        input_types: collections.abc.Iterable[global___DataType] | None = ...,
        is_permanent: builtins.bool = ...,
        kwargs: collections.abc.Iterable[global___Tuple_String_Expr] | None = ...,
        name: global___NameRef | None = ...,
        output_schema: global___UdtfSchema | None = ...,
        packages: collections.abc.Iterable[builtins.str] | None = ...,
        parallel: builtins.int = ...,
        replace: builtins.bool = ...,
        resource_constraint: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
        secrets: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
        secure: builtins.bool = ...,
        src: global___SrcPosition | None = ...,
        stage_location: builtins.str = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
        strict: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["artifact_repository", b"artifact_repository", "comment", b"comment", "handler", b"handler", "name", b"name", "output_schema", b"output_schema", "src", b"src"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["artifact_repository", b"artifact_repository", "comment", b"comment", "external_access_integrations", b"external_access_integrations", "handler", b"handler", "if_not_exists", b"if_not_exists", "immutable", b"immutable", "imports", b"imports", "input_types", b"input_types", "is_permanent", b"is_permanent", "kwargs", b"kwargs", "name", b"name", "output_schema", b"output_schema", "packages", b"packages", "parallel", b"parallel", "replace", b"replace", "resource_constraint", b"resource_constraint", "secrets", b"secrets", "secure", b"secure", "src", b"src", "stage_location", b"stage_location", "statement_params", b"statement_params", "strict", b"strict"]) -> None: ...

global___Udtf = Udtf

@typing.final
class UnaryOp(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NEG_FIELD_NUMBER: builtins.int
    NOT_FIELD_NUMBER: builtins.int
    @property
    def neg(self) -> global___Neg: ...
    def __init__(
        self,
        *,
        neg: global___Neg | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["neg", b"neg", "not", b"not", "variant", b"variant"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["neg", b"neg", "not", b"not", "variant", b"variant"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["variant", b"variant"]) -> typing.Literal["neg", "not"] | None: ...

global___UnaryOp = UnaryOp

@typing.final
class WindowSpecEmpty(google.protobuf.message.Message):
    """window.ir:18"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SRC_FIELD_NUMBER: builtins.int
    WND_FIELD_NUMBER: builtins.int
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def wnd(self) -> global___WindowSpecExpr: ...
    def __init__(
        self,
        *,
        src: global___SrcPosition | None = ...,
        wnd: global___WindowSpecExpr | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["src", b"src", "wnd", b"wnd"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["src", b"src", "wnd", b"wnd"]) -> None: ...

global___WindowSpecEmpty = WindowSpecEmpty

@typing.final
class WindowSpecExpr(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    WINDOW_SPEC_EMPTY_FIELD_NUMBER: builtins.int
    WINDOW_SPEC_ORDER_BY_FIELD_NUMBER: builtins.int
    WINDOW_SPEC_PARTITION_BY_FIELD_NUMBER: builtins.int
    WINDOW_SPEC_RANGE_BETWEEN_FIELD_NUMBER: builtins.int
    WINDOW_SPEC_ROWS_BETWEEN_FIELD_NUMBER: builtins.int
    @property
    def window_spec_empty(self) -> global___WindowSpecEmpty: ...
    @property
    def window_spec_order_by(self) -> global___WindowSpecOrderBy: ...
    @property
    def window_spec_partition_by(self) -> global___WindowSpecPartitionBy: ...
    @property
    def window_spec_range_between(self) -> global___WindowSpecRangeBetween: ...
    @property
    def window_spec_rows_between(self) -> global___WindowSpecRowsBetween: ...
    def __init__(
        self,
        *,
        window_spec_empty: global___WindowSpecEmpty | None = ...,
        window_spec_order_by: global___WindowSpecOrderBy | None = ...,
        window_spec_partition_by: global___WindowSpecPartitionBy | None = ...,
        window_spec_range_between: global___WindowSpecRangeBetween | None = ...,
        window_spec_rows_between: global___WindowSpecRowsBetween | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["variant", b"variant", "window_spec_empty", b"window_spec_empty", "window_spec_order_by", b"window_spec_order_by", "window_spec_partition_by", b"window_spec_partition_by", "window_spec_range_between", b"window_spec_range_between", "window_spec_rows_between", b"window_spec_rows_between"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["variant", b"variant", "window_spec_empty", b"window_spec_empty", "window_spec_order_by", b"window_spec_order_by", "window_spec_partition_by", b"window_spec_partition_by", "window_spec_range_between", b"window_spec_range_between", "window_spec_rows_between", b"window_spec_rows_between"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["variant", b"variant"]) -> typing.Literal["window_spec_empty", "window_spec_order_by", "window_spec_partition_by", "window_spec_range_between", "window_spec_rows_between"] | None: ...

global___WindowSpecExpr = WindowSpecExpr

@typing.final
class WindowSpecOrderBy(google.protobuf.message.Message):
    """window.ir:20"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    WND_FIELD_NUMBER: builtins.int
    @property
    def cols(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Expr]: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def wnd(self) -> global___WindowSpecExpr: ...
    def __init__(
        self,
        *,
        cols: collections.abc.Iterable[global___Expr] | None = ...,
        src: global___SrcPosition | None = ...,
        wnd: global___WindowSpecExpr | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["src", b"src", "wnd", b"wnd"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["cols", b"cols", "src", b"src", "wnd", b"wnd"]) -> None: ...

global___WindowSpecOrderBy = WindowSpecOrderBy

@typing.final
class WindowSpecPartitionBy(google.protobuf.message.Message):
    """window.ir:24"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    WND_FIELD_NUMBER: builtins.int
    @property
    def cols(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Expr]: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def wnd(self) -> global___WindowSpecExpr: ...
    def __init__(
        self,
        *,
        cols: collections.abc.Iterable[global___Expr] | None = ...,
        src: global___SrcPosition | None = ...,
        wnd: global___WindowSpecExpr | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["src", b"src", "wnd", b"wnd"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["cols", b"cols", "src", b"src", "wnd", b"wnd"]) -> None: ...

global___WindowSpecPartitionBy = WindowSpecPartitionBy

@typing.final
class WindowSpecRangeBetween(google.protobuf.message.Message):
    """window.ir:28"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    END_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    START_FIELD_NUMBER: builtins.int
    WND_FIELD_NUMBER: builtins.int
    @property
    def end(self) -> global___WindowRelativePosition: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def start(self) -> global___WindowRelativePosition: ...
    @property
    def wnd(self) -> global___WindowSpecExpr: ...
    def __init__(
        self,
        *,
        end: global___WindowRelativePosition | None = ...,
        src: global___SrcPosition | None = ...,
        start: global___WindowRelativePosition | None = ...,
        wnd: global___WindowSpecExpr | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["end", b"end", "src", b"src", "start", b"start", "wnd", b"wnd"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["end", b"end", "src", b"src", "start", b"start", "wnd", b"wnd"]) -> None: ...

global___WindowSpecRangeBetween = WindowSpecRangeBetween

@typing.final
class WindowSpecRowsBetween(google.protobuf.message.Message):
    """window.ir:33"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    END_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    START_FIELD_NUMBER: builtins.int
    WND_FIELD_NUMBER: builtins.int
    @property
    def end(self) -> global___WindowRelativePosition: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def start(self) -> global___WindowRelativePosition: ...
    @property
    def wnd(self) -> global___WindowSpecExpr: ...
    def __init__(
        self,
        *,
        end: global___WindowRelativePosition | None = ...,
        src: global___SrcPosition | None = ...,
        start: global___WindowRelativePosition | None = ...,
        wnd: global___WindowSpecExpr | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["end", b"end", "src", b"src", "start", b"start", "wnd", b"wnd"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["end", b"end", "src", b"src", "start", b"start", "wnd", b"wnd"]) -> None: ...

global___WindowSpecRowsBetween = WindowSpecRowsBetween

@typing.final
class WriteCopyIntoLocation(google.protobuf.message.Message):
    """dataframe-io.ir:101"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BLOCK_FIELD_NUMBER: builtins.int
    COPY_OPTIONS_FIELD_NUMBER: builtins.int
    FILE_FORMAT_NAME_FIELD_NUMBER: builtins.int
    FILE_FORMAT_TYPE_FIELD_NUMBER: builtins.int
    FORMAT_TYPE_OPTIONS_FIELD_NUMBER: builtins.int
    HEADER_FIELD_NUMBER: builtins.int
    LOCATION_FIELD_NUMBER: builtins.int
    PARTITION_BY_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    WRITER_FIELD_NUMBER: builtins.int
    block: builtins.bool
    header: builtins.bool
    location: builtins.str
    @property
    def copy_options(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_Expr]: ...
    @property
    def file_format_name(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def file_format_type(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def format_type_options(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    @property
    def partition_by(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    @property
    def writer(self) -> global___Expr: ...
    def __init__(
        self,
        *,
        block: builtins.bool = ...,
        copy_options: collections.abc.Iterable[global___Tuple_String_Expr] | None = ...,
        file_format_name: google.protobuf.wrappers_pb2.StringValue | None = ...,
        file_format_type: google.protobuf.wrappers_pb2.StringValue | None = ...,
        format_type_options: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
        header: builtins.bool = ...,
        location: builtins.str = ...,
        partition_by: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
        writer: global___Expr | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["file_format_name", b"file_format_name", "file_format_type", b"file_format_type", "partition_by", b"partition_by", "src", b"src", "writer", b"writer"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["block", b"block", "copy_options", b"copy_options", "file_format_name", b"file_format_name", "file_format_type", b"file_format_type", "format_type_options", b"format_type_options", "header", b"header", "location", b"location", "partition_by", b"partition_by", "src", b"src", "statement_params", b"statement_params", "writer", b"writer"]) -> None: ...

global___WriteCopyIntoLocation = WriteCopyIntoLocation

@typing.final
class WriteCsv(google.protobuf.message.Message):
    """dataframe-io.ir:108"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BLOCK_FIELD_NUMBER: builtins.int
    COPY_OPTIONS_FIELD_NUMBER: builtins.int
    FORMAT_TYPE_OPTIONS_FIELD_NUMBER: builtins.int
    HEADER_FIELD_NUMBER: builtins.int
    LOCATION_FIELD_NUMBER: builtins.int
    PARTITION_BY_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    WRITER_FIELD_NUMBER: builtins.int
    block: builtins.bool
    header: builtins.bool
    location: builtins.str
    @property
    def copy_options(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_Expr]: ...
    @property
    def format_type_options(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    @property
    def partition_by(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    @property
    def writer(self) -> global___Expr: ...
    def __init__(
        self,
        *,
        block: builtins.bool = ...,
        copy_options: collections.abc.Iterable[global___Tuple_String_Expr] | None = ...,
        format_type_options: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
        header: builtins.bool = ...,
        location: builtins.str = ...,
        partition_by: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
        writer: global___Expr | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["partition_by", b"partition_by", "src", b"src", "writer", b"writer"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["block", b"block", "copy_options", b"copy_options", "format_type_options", b"format_type_options", "header", b"header", "location", b"location", "partition_by", b"partition_by", "src", b"src", "statement_params", b"statement_params", "writer", b"writer"]) -> None: ...

global___WriteCsv = WriteCsv

@typing.final
class WriteFile(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    WRITE_COPY_INTO_LOCATION_FIELD_NUMBER: builtins.int
    WRITE_CSV_FIELD_NUMBER: builtins.int
    WRITE_JSON_FIELD_NUMBER: builtins.int
    WRITE_PARQUET_FIELD_NUMBER: builtins.int
    WRITE_SAVE_FIELD_NUMBER: builtins.int
    @property
    def write_copy_into_location(self) -> global___WriteCopyIntoLocation: ...
    @property
    def write_csv(self) -> global___WriteCsv: ...
    @property
    def write_json(self) -> global___WriteJson: ...
    @property
    def write_parquet(self) -> global___WriteParquet: ...
    @property
    def write_save(self) -> global___WriteSave: ...
    def __init__(
        self,
        *,
        write_copy_into_location: global___WriteCopyIntoLocation | None = ...,
        write_csv: global___WriteCsv | None = ...,
        write_json: global___WriteJson | None = ...,
        write_parquet: global___WriteParquet | None = ...,
        write_save: global___WriteSave | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["variant", b"variant", "write_copy_into_location", b"write_copy_into_location", "write_csv", b"write_csv", "write_json", b"write_json", "write_parquet", b"write_parquet", "write_save", b"write_save"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["variant", b"variant", "write_copy_into_location", b"write_copy_into_location", "write_csv", b"write_csv", "write_json", b"write_json", "write_parquet", b"write_parquet", "write_save", b"write_save"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["variant", b"variant"]) -> typing.Literal["write_copy_into_location", "write_csv", "write_json", "write_parquet", "write_save"] | None: ...

global___WriteFile = WriteFile

@typing.final
class WriteInsertInto(google.protobuf.message.Message):
    """dataframe-io.ir:114"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    OVERWRITE_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    TABLE_NAME_FIELD_NUMBER: builtins.int
    WRITER_FIELD_NUMBER: builtins.int
    overwrite: builtins.bool
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def table_name(self) -> global___NameRef: ...
    @property
    def writer(self) -> global___Expr: ...
    def __init__(
        self,
        *,
        overwrite: builtins.bool = ...,
        src: global___SrcPosition | None = ...,
        table_name: global___NameRef | None = ...,
        writer: global___Expr | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["src", b"src", "table_name", b"table_name", "writer", b"writer"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["overwrite", b"overwrite", "src", b"src", "table_name", b"table_name", "writer", b"writer"]) -> None: ...

global___WriteInsertInto = WriteInsertInto

@typing.final
class WriteJson(google.protobuf.message.Message):
    """dataframe-io.ir:110"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BLOCK_FIELD_NUMBER: builtins.int
    COPY_OPTIONS_FIELD_NUMBER: builtins.int
    FORMAT_TYPE_OPTIONS_FIELD_NUMBER: builtins.int
    HEADER_FIELD_NUMBER: builtins.int
    LOCATION_FIELD_NUMBER: builtins.int
    PARTITION_BY_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    WRITER_FIELD_NUMBER: builtins.int
    block: builtins.bool
    header: builtins.bool
    location: builtins.str
    @property
    def copy_options(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_Expr]: ...
    @property
    def format_type_options(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    @property
    def partition_by(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    @property
    def writer(self) -> global___Expr: ...
    def __init__(
        self,
        *,
        block: builtins.bool = ...,
        copy_options: collections.abc.Iterable[global___Tuple_String_Expr] | None = ...,
        format_type_options: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
        header: builtins.bool = ...,
        location: builtins.str = ...,
        partition_by: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
        writer: global___Expr | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["partition_by", b"partition_by", "src", b"src", "writer", b"writer"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["block", b"block", "copy_options", b"copy_options", "format_type_options", b"format_type_options", "header", b"header", "location", b"location", "partition_by", b"partition_by", "src", b"src", "statement_params", b"statement_params", "writer", b"writer"]) -> None: ...

global___WriteJson = WriteJson

@typing.final
class WritePandas(google.protobuf.message.Message):
    """dataframe.ir:44"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    AUTO_CREATE_TABLE_FIELD_NUMBER: builtins.int
    CHUNK_SIZE_FIELD_NUMBER: builtins.int
    COMPRESSION_FIELD_NUMBER: builtins.int
    CREATE_TEMP_TABLE_FIELD_NUMBER: builtins.int
    DF_FIELD_NUMBER: builtins.int
    KWARGS_FIELD_NUMBER: builtins.int
    ON_ERROR_FIELD_NUMBER: builtins.int
    OVERWRITE_FIELD_NUMBER: builtins.int
    PARALLEL_FIELD_NUMBER: builtins.int
    QUOTE_IDENTIFIERS_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    TABLE_NAME_FIELD_NUMBER: builtins.int
    TABLE_TYPE_FIELD_NUMBER: builtins.int
    auto_create_table: builtins.bool
    compression: builtins.str
    create_temp_table: builtins.bool
    on_error: builtins.str
    overwrite: builtins.bool
    parallel: builtins.int
    quote_identifiers: builtins.bool
    table_type: builtins.str
    @property
    def chunk_size(self) -> google.protobuf.wrappers_pb2.Int64Value: ...
    @property
    def df(self) -> global___DataframeData: ...
    @property
    def kwargs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_Expr]: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def table_name(self) -> global___NameRef: ...
    def __init__(
        self,
        *,
        auto_create_table: builtins.bool = ...,
        chunk_size: google.protobuf.wrappers_pb2.Int64Value | None = ...,
        compression: builtins.str = ...,
        create_temp_table: builtins.bool = ...,
        df: global___DataframeData | None = ...,
        kwargs: collections.abc.Iterable[global___Tuple_String_Expr] | None = ...,
        on_error: builtins.str = ...,
        overwrite: builtins.bool = ...,
        parallel: builtins.int = ...,
        quote_identifiers: builtins.bool = ...,
        src: global___SrcPosition | None = ...,
        table_name: global___NameRef | None = ...,
        table_type: builtins.str = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["chunk_size", b"chunk_size", "df", b"df", "src", b"src", "table_name", b"table_name"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["auto_create_table", b"auto_create_table", "chunk_size", b"chunk_size", "compression", b"compression", "create_temp_table", b"create_temp_table", "df", b"df", "kwargs", b"kwargs", "on_error", b"on_error", "overwrite", b"overwrite", "parallel", b"parallel", "quote_identifiers", b"quote_identifiers", "src", b"src", "table_name", b"table_name", "table_type", b"table_type"]) -> None: ...

global___WritePandas = WritePandas

@typing.final
class WriteParquet(google.protobuf.message.Message):
    """dataframe-io.ir:112"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BLOCK_FIELD_NUMBER: builtins.int
    COPY_OPTIONS_FIELD_NUMBER: builtins.int
    FORMAT_TYPE_OPTIONS_FIELD_NUMBER: builtins.int
    HEADER_FIELD_NUMBER: builtins.int
    LOCATION_FIELD_NUMBER: builtins.int
    PARTITION_BY_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    WRITER_FIELD_NUMBER: builtins.int
    block: builtins.bool
    header: builtins.bool
    location: builtins.str
    @property
    def copy_options(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_Expr]: ...
    @property
    def format_type_options(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    @property
    def partition_by(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    @property
    def writer(self) -> global___Expr: ...
    def __init__(
        self,
        *,
        block: builtins.bool = ...,
        copy_options: collections.abc.Iterable[global___Tuple_String_Expr] | None = ...,
        format_type_options: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
        header: builtins.bool = ...,
        location: builtins.str = ...,
        partition_by: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
        writer: global___Expr | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["partition_by", b"partition_by", "src", b"src", "writer", b"writer"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["block", b"block", "copy_options", b"copy_options", "format_type_options", b"format_type_options", "header", b"header", "location", b"location", "partition_by", b"partition_by", "src", b"src", "statement_params", b"statement_params", "writer", b"writer"]) -> None: ...

global___WriteParquet = WriteParquet

@typing.final
class WriteSave(google.protobuf.message.Message):
    """dataframe-io.ir:106"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BLOCK_FIELD_NUMBER: builtins.int
    COPY_OPTIONS_FIELD_NUMBER: builtins.int
    FORMAT_TYPE_OPTIONS_FIELD_NUMBER: builtins.int
    HEADER_FIELD_NUMBER: builtins.int
    LOCATION_FIELD_NUMBER: builtins.int
    PARTITION_BY_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    WRITER_FIELD_NUMBER: builtins.int
    block: builtins.bool
    header: builtins.bool
    location: builtins.str
    @property
    def copy_options(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_Expr]: ...
    @property
    def format_type_options(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    @property
    def partition_by(self) -> global___Expr: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    @property
    def writer(self) -> global___Expr: ...
    def __init__(
        self,
        *,
        block: builtins.bool = ...,
        copy_options: collections.abc.Iterable[global___Tuple_String_Expr] | None = ...,
        format_type_options: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
        header: builtins.bool = ...,
        location: builtins.str = ...,
        partition_by: global___Expr | None = ...,
        src: global___SrcPosition | None = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
        writer: global___Expr | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["partition_by", b"partition_by", "src", b"src", "writer", b"writer"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["block", b"block", "copy_options", b"copy_options", "format_type_options", b"format_type_options", "header", b"header", "location", b"location", "partition_by", b"partition_by", "src", b"src", "statement_params", b"statement_params", "writer", b"writer"]) -> None: ...

global___WriteSave = WriteSave

@typing.final
class WriteTable(google.protobuf.message.Message):
    """dataframe-io.ir:70"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BLOCK_FIELD_NUMBER: builtins.int
    CHANGE_TRACKING_FIELD_NUMBER: builtins.int
    CLUSTERING_KEYS_FIELD_NUMBER: builtins.int
    COLUMN_ORDER_FIELD_NUMBER: builtins.int
    COMMENT_FIELD_NUMBER: builtins.int
    COPY_GRANTS_FIELD_NUMBER: builtins.int
    CREATE_TEMP_TABLE_FIELD_NUMBER: builtins.int
    DATA_RETENTION_TIME_FIELD_NUMBER: builtins.int
    ENABLE_SCHEMA_EVOLUTION_FIELD_NUMBER: builtins.int
    ICEBERG_CONFIG_FIELD_NUMBER: builtins.int
    MAX_DATA_EXTENSION_TIME_FIELD_NUMBER: builtins.int
    MODE_FIELD_NUMBER: builtins.int
    SRC_FIELD_NUMBER: builtins.int
    STATEMENT_PARAMS_FIELD_NUMBER: builtins.int
    TABLE_NAME_FIELD_NUMBER: builtins.int
    TABLE_TYPE_FIELD_NUMBER: builtins.int
    WRITER_FIELD_NUMBER: builtins.int
    block: builtins.bool
    column_order: builtins.str
    copy_grants: builtins.bool
    create_temp_table: builtins.bool
    table_type: builtins.str
    @property
    def change_tracking(self) -> google.protobuf.wrappers_pb2.BoolValue: ...
    @property
    def clustering_keys(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Expr]: ...
    @property
    def comment(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def data_retention_time(self) -> google.protobuf.wrappers_pb2.Int64Value: ...
    @property
    def enable_schema_evolution(self) -> google.protobuf.wrappers_pb2.BoolValue: ...
    @property
    def iceberg_config(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_Expr]: ...
    @property
    def max_data_extension_time(self) -> google.protobuf.wrappers_pb2.Int64Value: ...
    @property
    def mode(self) -> global___SaveMode: ...
    @property
    def src(self) -> global___SrcPosition: ...
    @property
    def statement_params(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Tuple_String_String]: ...
    @property
    def table_name(self) -> global___NameRef: ...
    @property
    def writer(self) -> global___Expr: ...
    def __init__(
        self,
        *,
        block: builtins.bool = ...,
        change_tracking: google.protobuf.wrappers_pb2.BoolValue | None = ...,
        clustering_keys: collections.abc.Iterable[global___Expr] | None = ...,
        column_order: builtins.str = ...,
        comment: google.protobuf.wrappers_pb2.StringValue | None = ...,
        copy_grants: builtins.bool = ...,
        create_temp_table: builtins.bool = ...,
        data_retention_time: google.protobuf.wrappers_pb2.Int64Value | None = ...,
        enable_schema_evolution: google.protobuf.wrappers_pb2.BoolValue | None = ...,
        iceberg_config: collections.abc.Iterable[global___Tuple_String_Expr] | None = ...,
        max_data_extension_time: google.protobuf.wrappers_pb2.Int64Value | None = ...,
        mode: global___SaveMode | None = ...,
        src: global___SrcPosition | None = ...,
        statement_params: collections.abc.Iterable[global___Tuple_String_String] | None = ...,
        table_name: global___NameRef | None = ...,
        table_type: builtins.str = ...,
        writer: global___Expr | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["change_tracking", b"change_tracking", "comment", b"comment", "data_retention_time", b"data_retention_time", "enable_schema_evolution", b"enable_schema_evolution", "max_data_extension_time", b"max_data_extension_time", "mode", b"mode", "src", b"src", "table_name", b"table_name", "writer", b"writer"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["block", b"block", "change_tracking", b"change_tracking", "clustering_keys", b"clustering_keys", "column_order", b"column_order", "comment", b"comment", "copy_grants", b"copy_grants", "create_temp_table", b"create_temp_table", "data_retention_time", b"data_retention_time", "enable_schema_evolution", b"enable_schema_evolution", "iceberg_config", b"iceberg_config", "max_data_extension_time", b"max_data_extension_time", "mode", b"mode", "src", b"src", "statement_params", b"statement_params", "table_name", b"table_name", "table_type", b"table_type", "writer", b"writer"]) -> None: ...

global___WriteTable = WriteTable
