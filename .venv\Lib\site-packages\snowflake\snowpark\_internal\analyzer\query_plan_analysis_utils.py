#
# Copyright (c) 2012-2025 Snowflake Computing Inc. All rights reserved.
#

from collections import Counter
from enum import Enum
from typing import TYPE_CHECKING, Dict

if TYPE_CHECKING:
    from snowflake.snowpark._internal.analyzer.snowflake_plan_node import LogicalPlan


class PlanNodeCategory(Enum):
    """This enum class is used to account for different types of sql
    text generated by expressions and logical plan nodes. A bottom up
    aggregation of the number of occurrences of each enum type is
    done in Expression and LogicalPlan class to calculate and stat
    of overall query complexity in the context of compiling for the
    generated sql.
    """

    FILTER = "filter"
    ORDER_BY = "order_by"
    JOIN = "join"
    SET_OPERATION = "set_operation"  # UNION, EXCEPT, INTERSECT, UNION ALL
    SAMPLE = "sample"
    PIVOT = "pivot"
    UNPIVOT = "unpivot"
    WINDOW = "window"
    GROUP_BY = "group_by"
    DISTINCT = "distinct"
    PARTITION_BY = "partition_by"
    CASE_WHEN = "case_when"
    LITERAL = "literal"  # cover all literals like numbers, constant strings, etc
    COLUMN = "column"  # covers all cases where a table column is referred
    FUNCTION = (
        "function"  # cover all snowflake built-in function, table functions and UDXFs
    )
    IN = "in"
    WITH_QUERY = "with_query"
    LOW_IMPACT = "low_impact"
    OTHERS = "others"

    def __repr__(self) -> str:
        return self.name


class PlanState(Enum):
    """
    This is an enum class for the state that are extracted for a given SnowflakePlan
    or SelectStatement.
    """

    # the height of the given plan
    PLAN_HEIGHT = "plan_height"
    # the number of SelectStatement nodes in the plan that have
    # _merge_projection_complexity_with_subquery set to True
    NUM_SELECTS_WITH_COMPLEXITY_MERGED = "num_selects_with_complexity_merged"
    # number of cte nodes detected
    NUM_CTE_NODES = "num_cte_nodes"
    # node complexity distribution for the duplicated nodes that detected as cte
    # NOTE: this is not the cte node complexity distribution, in other words, if a
    #   node occurs twice, it will be counted twice
    DUPLICATED_NODE_COMPLEXITY_DISTRIBUTION = "duplicated_node_distribution"


def sum_node_complexities(
    *node_complexities: Dict[PlanNodeCategory, int]
) -> Dict[PlanNodeCategory, int]:
    """This is a helper function to sum complexity values from all complexity dictionaries. A node
    complexity is a dictionary of node category to node count mapping"""
    counter_sum = sum(
        (Counter(complexity) for complexity in node_complexities), Counter()
    )
    return dict(counter_sum)


def subtract_complexities(
    complexities1: Dict[PlanNodeCategory, int],
    complexities2: Dict[PlanNodeCategory, int],
) -> Dict[PlanNodeCategory, int]:
    """
    This is a helper function for complexities1 - complexities2.
    """

    result_complexities = complexities1.copy()
    for key, value in complexities2.items():
        if key in result_complexities:
            result_complexities[key] -= value
        else:
            result_complexities[key] = -value

    return result_complexities


def get_complexity_score(node: "LogicalPlan") -> int:
    """Calculates the complexity score based on the cumulative node complexity"""
    adjusted_cumulative_complexity = node.cumulative_node_complexity.copy()
    if hasattr(node, "referenced_ctes"):
        for with_query_block in node.referenced_ctes:  # type: ignore
            child_node = with_query_block.children[0]
            for category, value in child_node.cumulative_node_complexity.items():
                if category in adjusted_cumulative_complexity:
                    adjusted_cumulative_complexity[category] += value
                else:
                    adjusted_cumulative_complexity[category] = value

    score = sum(adjusted_cumulative_complexity.values())
    return score
